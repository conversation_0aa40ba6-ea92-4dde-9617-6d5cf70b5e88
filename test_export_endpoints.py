"""
Simple test script to verify export endpoints are working correctly.
This can be run to test the API endpoints without a full test suite.
"""
import requests
import json
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
TEST_DOC_ID = str(uuid.uuid4())  # Replace with actual document ID when testing

def test_export_endpoints():
    """Test all export endpoints"""
    
    print("🧪 Testing Export API Endpoints")
    print("=" * 50)
    
    # Test data
    headers = {
        "Content-Type": "application/json",
        # Add authentication headers when testing with real API
        # "Authorization": "Bearer your-token-here"
    }
    
    # Test 1: Get available export formats
    print("\n1. Testing available export formats...")
    try:
        response = requests.get(f"{BASE_URL}/documents/export/formats/prd", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            formats = response.json()
            print(f"   Available formats: {len(formats.get('formats', []))}")
            for fmt in formats.get('formats', [])[:3]:  # Show first 3
                print(f"   - {fmt['name']}: {fmt['description']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 2: Export to Word
    print("\n2. Testing Word export...")
    word_request = {
        "include_toc": True,
        "page_orientation": "portrait",
        "font_family": "Calibri",
        "font_size": 11,
        "include_metadata": True
    }
    try:
        response = requests.post(
            f"{BASE_URL}/documents/{TEST_DOC_ID}/export/word",
            headers=headers,
            json=word_request
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            job = response.json()
            print(f"   Job ID: {job['job_id']}")
            print(f"   Status: {job['status']}")
            print(f"   Format: {job['export_format']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 3: Export to PDF
    print("\n3. Testing PDF export...")
    pdf_request = {
        "include_toc": True,
        "page_size": "A4",
        "margin_size": "normal",
        "include_headers_footers": True,
        "include_metadata": True
    }
    try:
        response = requests.post(
            f"{BASE_URL}/documents/{TEST_DOC_ID}/export/pdf",
            headers=headers,
            json=pdf_request
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            job = response.json()
            print(f"   Job ID: {job['job_id']}")
            print(f"   Estimated completion: {job.get('estimated_completion', 'N/A')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 4: Export to Confluence
    print("\n4. Testing Confluence export...")
    confluence_request = {
        "space_key": "TEST",
        "page_title": "Test Document Export",
        "update_existing": False,
        "confluence_url": "https://your-domain.atlassian.net",
        "auth_token": "test-token",
        "include_metadata": True
    }
    try:
        response = requests.post(
            f"{BASE_URL}/documents/{TEST_DOC_ID}/export/confluence",
            headers=headers,
            json=confluence_request
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            job = response.json()
            print(f"   Job ID: {job['job_id']}")
            print(f"   Export format: {job['export_format']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 5: Export to OpenAPI
    print("\n5. Testing OpenAPI export...")
    openapi_request = {
        "openapi_version": "3.0.3",
        "include_examples": True,
        "server_urls": ["https://api.example.com"],
        "include_metadata": True
    }
    try:
        response = requests.post(
            f"{BASE_URL}/documents/{TEST_DOC_ID}/export/openapi",
            headers=headers,
            json=openapi_request
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            job = response.json()
            print(f"   Job ID: {job['job_id']}")
            print(f"   Progress: {job['progress']}%")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 6: Custom template export
    print("\n6. Testing custom template export...")
    custom_request = {
        "template_content": "<html><body><h1>{{title}}</h1><p>{{content}}</p></body></html>",
        "template_engine": "jinja2",
        "output_format": "html",
        "include_metadata": True
    }
    try:
        response = requests.post(
            f"{BASE_URL}/documents/{TEST_DOC_ID}/export/custom",
            headers=headers,
            json=custom_request
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            job = response.json()
            print(f"   Job ID: {job['job_id']}")
            print(f"   Document ID: {job['document_id']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Export endpoint tests completed!")
    print("\nNote: These tests assume the API server is running on localhost:8000")
    print("Replace TEST_DOC_ID with an actual document ID for real testing.")


def test_job_management():
    """Test export job management endpoints"""
    
    print("\n🔧 Testing Export Job Management")
    print("=" * 50)
    
    headers = {"Content-Type": "application/json"}
    test_job_id = str(uuid.uuid4())  # Replace with actual job ID
    test_file_id = str(uuid.uuid4())  # Replace with actual file ID
    
    # Test job status
    print("\n1. Testing job status check...")
    try:
        response = requests.get(f"{BASE_URL}/documents/export/status/{test_job_id}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 404:
            print("   Expected: Job not found (using test UUID)")
        elif response.status_code == 200:
            job = response.json()
            print(f"   Job status: {job['status']}")
            print(f"   Progress: {job['progress']}%")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test export history
    print("\n2. Testing export history...")
    try:
        response = requests.get(f"{BASE_URL}/documents/export/history/{TEST_DOC_ID}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 404:
            print("   Expected: Document not found (using test UUID)")
        elif response.status_code == 200:
            history = response.json()
            print(f"   Total exports: {history['total_count']}")
            print(f"   History items: {len(history['exports'])}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test file download
    print("\n3. Testing file download...")
    try:
        response = requests.get(f"{BASE_URL}/documents/export/download/{test_file_id}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 404:
            print("   Expected: File not found (using test UUID)")
        elif response.status_code == 200:
            print("   Download successful")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Job management tests completed!")


if __name__ == "__main__":
    print("🚀 Starting Export API Tests")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_export_endpoints()
    test_job_management()
    
    print("\n📋 Summary:")
    print("- All export endpoints have been created")
    print("- Modular structure follows existing patterns")
    print("- Job management and tracking implemented")
    print("- Integration endpoints for Confluence, Notion, JIRA")
    print("- Document-type specific exports (OpenAPI, PlantUML, etc.)")
    print("- Custom template support")
    print("\n🎯 Next steps:")
    print("1. Run the actual API server")
    print("2. Create test documents")
    print("3. Test with real document IDs")
    print("4. Implement actual export logic")
    print("5. Add database migrations for export tables")
