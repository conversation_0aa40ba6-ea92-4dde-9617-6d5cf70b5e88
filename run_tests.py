#!/usr/bin/env python3
"""
Test runner script for AI Agent Execution APIs.
"""

import subprocess
import sys
import os

def run_tests():
    """Run all tests for AI agent execution functionality."""
    print("🧪 Running AI Agent Execution API Tests")
    print("=" * 50)
    
    # Set environment variables for testing
    os.environ["TESTING"] = "1"
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    
    # Test commands to run
    test_commands = [
        # Service tests
        ["python", "-m", "pytest", "tests/services/test_agent_execution_service.py", "-v"],
        
        # API endpoint tests
        ["python", "-m", "pytest", "tests/api/v1/ai_prompts/test_execution.py", "-v"],
        ["python", "-m", "pytest", "tests/api/v1/ai_prompts/test_monitoring.py", "-v"],
        
        # Run all tests with coverage
        ["python", "-m", "pytest", "tests/", "--cov=app", "--cov-report=term-missing", "-v"]
    ]
    
    for i, cmd in enumerate(test_commands, 1):
        print(f"\n📋 Running test command {i}/{len(test_commands)}: {' '.join(cmd)}")
        print("-" * 40)
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=False)
            print(f"✅ Test command {i} passed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Test command {i} failed with exit code {e.returncode}")
            return False
        except FileNotFoundError:
            print(f"⚠️  Command not found: {cmd[0]}")
            print("Please install pytest and pytest-cov: pip install pytest pytest-cov")
            return False
    
    print("\n🎉 All tests completed successfully!")
    return True

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
