"""
Test script for template and component library API endpoints.
"""
import requests
import json
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api/v1/documents"
TEST_DOC_TYPE = "prd"

def test_template_endpoints():
    """Test template management endpoints"""
    
    print("🧪 Testing Template API Endpoints")
    print("=" * 50)
    
    headers = {
        "Content-Type": "application/json",
        # Add authentication headers when testing with real API
        # "Authorization": "Bearer your-token-here"
    }
    
    # Test 1: List templates
    print("\n1. Testing list templates...")
    try:
        response = requests.get(f"{BASE_URL}/templates/{TEST_DOC_TYPE}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            templates = response.json()
            print(f"   Total templates: {templates.get('total_count', 0)}")
            print(f"   Document type: {templates.get('document_type')}")
            if templates.get('templates'):
                print(f"   First template: {templates['templates'][0]['name']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 2: Create template
    print("\n2. Testing create template...")
    template_data = {
        "name": "Test PRD Template",
        "description": "A test template for PRD documents",
        "document_type": TEST_DOC_TYPE,
        "content": "# {{title}}\n\n## Overview\n{{overview}}\n\n## Requirements\n{{requirements}}",
        "variables": [
            {"name": "title", "type": "string", "required": True, "description": "Document title"},
            {"name": "overview", "type": "text", "required": True, "description": "Project overview"},
            {"name": "requirements", "type": "text", "required": False, "description": "Requirements list"}
        ],
        "metadata": {"category": "standard", "difficulty": "beginner"},
        "visibility": "private",
        "tags": ["test", "prd", "standard"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/templates/{TEST_DOC_TYPE}",
            headers=headers,
            json=template_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            template = response.json()
            print(f"   Created template ID: {template['id']}")
            print(f"   Template name: {template['name']}")
            print(f"   Variables count: {len(template['variables'])}")
            template_id = template['id']
        else:
            print(f"   Error: {response.text}")
            template_id = str(uuid.uuid4())  # Fallback for other tests
    except Exception as e:
        print(f"   Connection error: {e}")
        template_id = str(uuid.uuid4())
    
    # Test 3: Get specific template
    print("\n3. Testing get specific template...")
    try:
        response = requests.get(f"{BASE_URL}/templates/{TEST_DOC_TYPE}/{template_id}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            template = response.json()
            print(f"   Template: {template['name']}")
            print(f"   Usage count: {template['usage_count']}")
            print(f"   Visibility: {template['visibility']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 4: Use template
    print("\n4. Testing use template...")
    use_request = {
        "title": "My Test Document",
        "variable_values": {
            "title": "Test PRD Document",
            "overview": "This is a test document created from a template",
            "requirements": "1. Feature A\n2. Feature B\n3. Feature C"
        },
        "save_as_draft": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/templates/{template_id}/use",
            headers=headers,
            json=use_request
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            document = response.json()
            print(f"   Created document ID: {document['id']}")
            print(f"   Document title: {document['title']}")
            print(f"   Document status: {document['status']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 5: Update template
    print("\n5. Testing update template...")
    update_data = {
        "description": "Updated test template for PRD documents",
        "tags": ["test", "prd", "standard", "updated"]
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/templates/{TEST_DOC_TYPE}/{template_id}",
            headers=headers,
            json=update_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            template = response.json()
            print(f"   Updated description: {template['description']}")
            print(f"   Updated tags: {template['tags']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")


def test_component_endpoints():
    """Test component library endpoints"""
    
    print("\n🔧 Testing Component Library API Endpoints")
    print("=" * 50)
    
    headers = {"Content-Type": "application/json"}
    
    # Test 1: Get components for document type
    print("\n1. Testing get components...")
    try:
        response = requests.get(f"{BASE_URL}/library/components/{TEST_DOC_TYPE}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            components = response.json()
            print(f"   Total components: {components.get('total_count', 0)}")
            print(f"   Available categories: {components.get('categories', [])}")
            if components.get('components'):
                print(f"   First component: {components['components'][0]['name']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 2: Save component
    print("\n2. Testing save component...")
    component_data = {
        "name": "Requirements Table",
        "description": "A reusable table for listing requirements",
        "component_type": "table",
        "document_types": [TEST_DOC_TYPE],
        "content": "| Requirement | Priority | Status |\n|-------------|----------|--------|\n| {{req1}} | {{priority1}} | {{status1}} |\n| {{req2}} | {{priority2}} | {{status2}} |",
        "variables": [
            {"name": "req1", "type": "string", "required": True},
            {"name": "priority1", "type": "string", "required": True},
            {"name": "status1", "type": "string", "required": True}
        ],
        "metadata": {"format": "markdown", "columns": 3},
        "visibility": "tenant",
        "tags": ["table", "requirements", "reusable"],
        "category": "tables"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/library/components/{TEST_DOC_TYPE}",
            headers=headers,
            json=component_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            component = response.json()
            print(f"   Created component ID: {component['id']}")
            print(f"   Component name: {component['name']}")
            print(f"   Component type: {component['component_type']}")
            component_id = component['id']
        else:
            print(f"   Error: {response.text}")
            component_id = str(uuid.uuid4())
    except Exception as e:
        print(f"   Connection error: {e}")
        component_id = str(uuid.uuid4())
    
    # Test 3: Get specific component
    print("\n3. Testing get specific component...")
    try:
        response = requests.get(f"{BASE_URL}/library/components/{TEST_DOC_TYPE}/{component_id}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            component = response.json()
            print(f"   Component: {component['name']}")
            print(f"   Usage count: {component['usage_count']}")
            print(f"   Document types: {component['document_types']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 4: Get common components
    print("\n4. Testing get common components...")
    try:
        response = requests.get(f"{BASE_URL}/library/components/common", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            components = response.json()
            print(f"   Common components: {components.get('total_count', 0)}")
            print(f"   Categories: {components.get('categories', [])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")


def test_section_endpoints():
    """Test section library endpoints"""
    
    print("\n📄 Testing Section Library API Endpoints")
    print("=" * 50)
    
    headers = {"Content-Type": "application/json"}
    
    # Test 1: Get sections
    print("\n1. Testing get sections...")
    try:
        response = requests.get(f"{BASE_URL}/library/sections/{TEST_DOC_TYPE}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            sections = response.json()
            print(f"   Total sections: {sections.get('total_count', 0)}")
            print(f"   Document type: {sections.get('document_type')}")
            if sections.get('sections'):
                print(f"   First section: {sections['sections'][0]['name']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 2: Save section
    print("\n2. Testing save section...")
    section_data = {
        "name": "Executive Summary",
        "description": "Standard executive summary section for PRDs",
        "document_type": TEST_DOC_TYPE,
        "content": "## Executive Summary\n\n### Problem Statement\n{{problem}}\n\n### Proposed Solution\n{{solution}}\n\n### Success Metrics\n{{metrics}}",
        "variables": [
            {"name": "problem", "type": "text", "required": True, "description": "Problem statement"},
            {"name": "solution", "type": "text", "required": True, "description": "Proposed solution"},
            {"name": "metrics", "type": "text", "required": False, "description": "Success metrics"}
        ],
        "metadata": {"section_type": "summary", "importance": "high"},
        "visibility": "tenant",
        "tags": ["summary", "executive", "standard"],
        "order_hint": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/library/sections/{TEST_DOC_TYPE}",
            headers=headers,
            json=section_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            section = response.json()
            print(f"   Created section ID: {section['id']}")
            print(f"   Section name: {section['name']}")
            print(f"   Order hint: {section['order_hint']}")
            section_id = section['id']
        else:
            print(f"   Error: {response.text}")
            section_id = str(uuid.uuid4())
    except Exception as e:
        print(f"   Connection error: {e}")
        section_id = str(uuid.uuid4())
    
    # Test 3: Get specific section
    print("\n3. Testing get specific section...")
    try:
        response = requests.get(f"{BASE_URL}/library/sections/{TEST_DOC_TYPE}/{section_id}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            section = response.json()
            print(f"   Section: {section['name']}")
            print(f"   Usage count: {section['usage_count']}")
            print(f"   Variables: {len(section['variables'])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")


if __name__ == "__main__":
    print("🚀 Starting Template & Component API Tests")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_template_endpoints()
    test_component_endpoints()
    test_section_endpoints()
    
    print("\n📋 Summary:")
    print("- Template management endpoints created")
    print("- Component library endpoints implemented")
    print("- Section pattern endpoints added")
    print("- Modular structure with small, focused files")
    print("- Usage tracking and analytics built-in")
    print("- Visibility controls for sharing")
    print("- Search and filtering capabilities")
    
    print("\n🎯 Next steps:")
    print("1. Run the actual API server")
    print("2. Test with real authentication")
    print("3. Create sample templates and components")
    print("4. Implement template engine (Jinja2)")
    print("5. Add database migrations for template tables")
    print("6. Add template validation and preview features")
