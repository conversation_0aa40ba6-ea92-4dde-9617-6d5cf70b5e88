"""Add form questions tables and indexes

Revision ID: 121e3d79efeb
Revises: 7791733dd565
Create Date: 2025-07-01 15:53:20.387499

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '121e3d79efeb'
down_revision: Union[str, Sequence[str], None] = '7791733dd565'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('export_templates')
    op.drop_table('template_ratings')
    op.drop_table('section_usage_logs')
    op.drop_table('document_templates')
    op.drop_table('export_format_support')
    op.drop_table('template_categories')
    op.drop_table('section_library')
    op.drop_table('export_integrations')
    op.drop_table('component_library')
    op.drop_table('learned_patterns')
    op.drop_table('export_history')
    op.drop_table('component_usage_logs')
    op.drop_table('ai_feedback')
    op.drop_table('learning_sessions')
    op.drop_table('export_files')
    op.drop_table('component_categories')
    op.drop_table('learning_profiles')
    op.drop_table('export_jobs')
    op.drop_table('template_usage_logs')
    op.create_index('idx_conditional_rules_condition_question_id', 'conditional_rules', ['condition_question_id'], unique=False)
    op.create_index('idx_conditional_rules_form_schema_id', 'conditional_rules', ['form_schema_id'], unique=False)
    op.create_index('idx_conditional_rules_is_active', 'conditional_rules', ['is_active'], unique=False)
    op.create_index('idx_conditional_rules_question_id', 'conditional_rules', ['question_id'], unique=False)
    op.create_index('idx_conditional_rules_tenant_id', 'conditional_rules', ['tenant_id'], unique=False)
    op.create_index('idx_form_questions_form_schema_id', 'form_questions', ['form_schema_id'], unique=False)
    op.create_index('idx_form_questions_is_active', 'form_questions', ['is_active'], unique=False)
    op.create_index('idx_form_questions_is_required', 'form_questions', ['is_required'], unique=False)
    op.create_index('idx_form_questions_order_index', 'form_questions', ['order_index'], unique=False)
    op.create_index('idx_form_questions_question_id', 'form_questions', ['question_id'], unique=False)
    op.create_index('idx_form_questions_question_type', 'form_questions', ['question_type'], unique=False)
    op.create_index('idx_form_questions_section', 'form_questions', ['section'], unique=False)
    op.create_index('idx_form_questions_tenant_id', 'form_questions', ['tenant_id'], unique=False)
    op.create_unique_constraint('uq_form_questions_schema_question_id', 'form_questions', ['form_schema_id', 'question_id'])
    op.create_index('idx_form_schemas_document_type', 'form_schemas', ['document_type'], unique=False)
    op.create_index('idx_form_schemas_is_active', 'form_schemas', ['is_active'], unique=False)
    op.create_index('idx_form_schemas_is_published', 'form_schemas', ['is_published'], unique=False)
    op.create_index('idx_form_schemas_tenant_id', 'form_schemas', ['tenant_id'], unique=False)
    op.create_unique_constraint('uq_form_schemas_doc_type_tenant', 'form_schemas', ['document_type', 'tenant_id'])
    op.create_index('idx_form_validation_logs_created_at', 'form_validation_logs', ['created_at'], unique=False)
    op.create_index('idx_form_validation_logs_form_schema_id', 'form_validation_logs', ['form_schema_id'], unique=False)
    op.create_index('idx_form_validation_logs_is_valid', 'form_validation_logs', ['is_valid'], unique=False)
    op.create_index('idx_form_validation_logs_tenant_id', 'form_validation_logs', ['tenant_id'], unique=False)
    op.create_index('idx_form_validation_logs_user_id', 'form_validation_logs', ['user_id'], unique=False)
    op.create_index('idx_question_templates_category', 'question_templates', ['category'], unique=False)
    op.create_index('idx_question_templates_is_active', 'question_templates', ['is_active'], unique=False)
    op.create_index('idx_question_templates_question_type', 'question_templates', ['question_type'], unique=False)
    op.create_index('idx_question_templates_tenant_id', 'question_templates', ['tenant_id'], unique=False)
    op.create_index('idx_question_templates_usage_count', 'question_templates', ['usage_count'], unique=False)
    op.create_unique_constraint('uq_question_templates_tenant_name', 'question_templates', ['tenant_id', 'name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_question_templates_tenant_name', 'question_templates', type_='unique')
    op.drop_index('idx_question_templates_usage_count', table_name='question_templates')
    op.drop_index('idx_question_templates_tenant_id', table_name='question_templates')
    op.drop_index('idx_question_templates_question_type', table_name='question_templates')
    op.drop_index('idx_question_templates_is_active', table_name='question_templates')
    op.drop_index('idx_question_templates_category', table_name='question_templates')
    op.drop_index('idx_form_validation_logs_user_id', table_name='form_validation_logs')
    op.drop_index('idx_form_validation_logs_tenant_id', table_name='form_validation_logs')
    op.drop_index('idx_form_validation_logs_is_valid', table_name='form_validation_logs')
    op.drop_index('idx_form_validation_logs_form_schema_id', table_name='form_validation_logs')
    op.drop_index('idx_form_validation_logs_created_at', table_name='form_validation_logs')
    op.drop_constraint('uq_form_schemas_doc_type_tenant', 'form_schemas', type_='unique')
    op.drop_index('idx_form_schemas_tenant_id', table_name='form_schemas')
    op.drop_index('idx_form_schemas_is_published', table_name='form_schemas')
    op.drop_index('idx_form_schemas_is_active', table_name='form_schemas')
    op.drop_index('idx_form_schemas_document_type', table_name='form_schemas')
    op.drop_constraint('uq_form_questions_schema_question_id', 'form_questions', type_='unique')
    op.drop_index('idx_form_questions_tenant_id', table_name='form_questions')
    op.drop_index('idx_form_questions_section', table_name='form_questions')
    op.drop_index('idx_form_questions_question_type', table_name='form_questions')
    op.drop_index('idx_form_questions_question_id', table_name='form_questions')
    op.drop_index('idx_form_questions_order_index', table_name='form_questions')
    op.drop_index('idx_form_questions_is_required', table_name='form_questions')
    op.drop_index('idx_form_questions_is_active', table_name='form_questions')
    op.drop_index('idx_form_questions_form_schema_id', table_name='form_questions')
    op.drop_index('idx_conditional_rules_tenant_id', table_name='conditional_rules')
    op.drop_index('idx_conditional_rules_question_id', table_name='conditional_rules')
    op.drop_index('idx_conditional_rules_is_active', table_name='conditional_rules')
    op.drop_index('idx_conditional_rules_form_schema_id', table_name='conditional_rules')
    op.drop_index('idx_conditional_rules_condition_question_id', table_name='conditional_rules')
    op.create_table('template_usage_logs',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('template_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('variable_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('success', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('ip_address', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['document_templates.id'], name=op.f('template_usage_logs_template_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('template_usage_logs_pkey'))
    )
    op.create_table('export_jobs',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('export_format', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('export_options', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('progress', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('estimated_completion', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('started_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('completed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('cancelled_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('result_file_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('result_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('integration_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], name='export_jobs_document_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='export_jobs_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('learning_profiles',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_type_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('learning_enabled', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('auto_apply_patterns', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('confidence_threshold', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_documents_processed', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_feedback_received', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('positive_feedback_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('negative_feedback_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('learned_patterns', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('user_preferences', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('performance_metrics', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('last_learning_update', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], name=op.f('learning_profiles_document_type_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('learning_profiles_pkey'))
    )
    op.create_table('component_categories',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('component_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('icon', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('color', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('order_index', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('component_categories_pkey'))
    )
    op.create_table('export_files',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('export_job_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('filename', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('original_filename', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('file_path', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('content_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('file_hash', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_public', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('download_token', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('download_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_downloaded_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['export_job_id'], ['export_jobs.id'], name=op.f('export_files_export_job_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('export_files_pkey'))
    )
    op.create_table('learning_sessions',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_type_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('triggered_by_user_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('session_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('input_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('processing_parameters', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('patterns_discovered', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('patterns_updated', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('patterns_removed', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('processing_time_seconds', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('confidence_improvement', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('started_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('completed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], name=op.f('learning_sessions_document_type_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('learning_sessions_pkey'))
    )
    op.create_table('ai_feedback',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('document_type_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('feedback_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('rating', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sentiment', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('ai_output_section', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('original_prompt', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('ai_generated_content', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('feedback_text', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('suggested_improvement', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('tags', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('processed', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('applied_to_learning', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('processed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], name=op.f('ai_feedback_document_id_fkey')),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], name=op.f('ai_feedback_document_type_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('ai_feedback_pkey'))
    )
    op.create_table('component_usage_logs',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('component_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('document_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('variable_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['component_id'], ['component_library.id'], name=op.f('component_usage_logs_component_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('component_usage_logs_pkey'))
    )
    op.create_table('export_history',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('export_job_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('export_format', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('export_options', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('processing_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('integration_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('integration_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], name=op.f('export_history_document_id_fkey')),
    sa.ForeignKeyConstraint(['export_job_id'], ['export_jobs.id'], name=op.f('export_history_export_job_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('export_history_pkey'))
    )
    op.create_table('learned_patterns',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_type_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('pattern_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('pattern_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('pattern_description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('pattern_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('trigger_conditions', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('confidence_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('success_rate', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('source_feedback_ids', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('source_documents', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('auto_apply', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('last_applied_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], name=op.f('learned_patterns_document_type_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('learned_patterns_pkey'))
    )
    op.create_table('component_library',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('component_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('document_types', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('variables', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('component_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('visibility', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tags', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('category', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('is_featured', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('component_library_pkey'))
    )
    op.create_table('export_integrations',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('display_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('integration_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('config_schema', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('default_config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('base_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('auth_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('credentials', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('last_tested_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('test_status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('test_error', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('export_integrations_pkey'))
    )
    op.create_table('section_library',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('document_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('variables', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('section_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('visibility', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('order_hint', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('tags', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('category', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='section_library_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('template_categories',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('document_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('icon', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('color', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('order_index', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('template_categories_pkey'))
    )
    op.create_table('export_format_support',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_type_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('export_format', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('is_supported', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('is_recommended', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('estimated_time_minutes', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('file_extension', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('mime_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('default_options', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('required_options', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], name=op.f('export_format_support_document_type_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('export_format_support_pkey'))
    )
    op.create_table('document_templates',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('document_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('variables', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('template_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('template_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('visibility', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tags', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('category', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('is_featured', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('rating_sum', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('rating_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='document_templates_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('section_usage_logs',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('section_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('document_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('variable_values', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['section_id'], ['section_library.id'], name=op.f('section_usage_logs_section_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('section_usage_logs_pkey'))
    )
    op.create_table('template_ratings',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('template_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('rating', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('feedback', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['document_templates.id'], name=op.f('template_ratings_template_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('template_ratings_pkey'))
    )
    op.create_table('export_templates',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('template_content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('template_engine', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('output_format', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('document_types', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('variables', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('sample_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('is_public', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('usage_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('last_used_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('export_templates_pkey'))
    )
    # ### end Alembic commands ###
