"""Add indexes and constraints for AI agent tables

Revision ID: 7791733dd565
Revises: add_workflow_tables
Create Date: 2025-07-01 15:23:15.274978

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7791733dd565'
down_revision: Union[str, Sequence[str], None] = 'add_workflow_tables'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add indexes and constraints for AI agent tables."""
    # Add indexes for ai_agents table
    op.create_index('ix_ai_agents_doc_type', 'ai_agents', ['doc_type'])
    op.create_index('ix_ai_agents_status', 'ai_agents', ['status'])
    op.create_index('ix_ai_agents_tenant_id', 'ai_agents', ['tenant_id'])
    op.create_index('ix_ai_agents_agent_type', 'ai_agents', ['agent_type'])
    op.create_index('ix_ai_agents_created_at', 'ai_agents', ['created_at'])

    # Add composite indexes for common queries
    op.create_index('ix_ai_agents_doc_type_status', 'ai_agents', ['doc_type', 'status'])
    op.create_index('ix_ai_agents_tenant_status', 'ai_agents', ['tenant_id', 'status'])

    # Add indexes for ai_prompts table
    op.create_index('ix_ai_prompts_doc_type', 'ai_prompts', ['doc_type'])
    op.create_index('ix_ai_prompts_prompt_type', 'ai_prompts', ['prompt_type'])
    op.create_index('ix_ai_prompts_category', 'ai_prompts', ['category'])
    op.create_index('ix_ai_prompts_tenant_id', 'ai_prompts', ['tenant_id'])
    op.create_index('ix_ai_prompts_agent_id', 'ai_prompts', ['agent_id'])
    op.create_index('ix_ai_prompts_is_active', 'ai_prompts', ['is_active'])
    op.create_index('ix_ai_prompts_created_at', 'ai_prompts', ['created_at'])

    # Add composite indexes for ai_prompts
    op.create_index('ix_ai_prompts_doc_type_active', 'ai_prompts', ['doc_type', 'is_active'])
    op.create_index('ix_ai_prompts_tenant_active', 'ai_prompts', ['tenant_id', 'is_active'])

    # Add indexes for agent_jobs table
    op.create_index('ix_agent_jobs_agent_id', 'agent_jobs', ['agent_id'])
    op.create_index('ix_agent_jobs_status', 'agent_jobs', ['status'])
    op.create_index('ix_agent_jobs_user_id', 'agent_jobs', ['user_id'])
    op.create_index('ix_agent_jobs_tenant_id', 'agent_jobs', ['tenant_id'])
    op.create_index('ix_agent_jobs_document_id', 'agent_jobs', ['document_id'])
    op.create_index('ix_agent_jobs_job_type', 'agent_jobs', ['job_type'])
    op.create_index('ix_agent_jobs_created_at', 'agent_jobs', ['created_at'])
    op.create_index('ix_agent_jobs_started_at', 'agent_jobs', ['started_at'])
    op.create_index('ix_agent_jobs_completed_at', 'agent_jobs', ['completed_at'])

    # Add composite indexes for agent_jobs (common query patterns)
    op.create_index('ix_agent_jobs_user_tenant', 'agent_jobs', ['user_id', 'tenant_id'])
    op.create_index('ix_agent_jobs_user_status', 'agent_jobs', ['user_id', 'status'])
    op.create_index('ix_agent_jobs_tenant_status', 'agent_jobs', ['tenant_id', 'status'])
    op.create_index('ix_agent_jobs_agent_status', 'agent_jobs', ['agent_id', 'status'])
    op.create_index('ix_agent_jobs_status_created', 'agent_jobs', ['status', 'created_at'])


def downgrade() -> None:
    """Remove indexes and constraints for AI agent tables."""
    # Drop composite indexes for agent_jobs
    op.drop_index('ix_agent_jobs_status_created', 'agent_jobs')
    op.drop_index('ix_agent_jobs_agent_status', 'agent_jobs')
    op.drop_index('ix_agent_jobs_tenant_status', 'agent_jobs')
    op.drop_index('ix_agent_jobs_user_status', 'agent_jobs')
    op.drop_index('ix_agent_jobs_user_tenant', 'agent_jobs')

    # Drop single column indexes for agent_jobs
    op.drop_index('ix_agent_jobs_completed_at', 'agent_jobs')
    op.drop_index('ix_agent_jobs_started_at', 'agent_jobs')
    op.drop_index('ix_agent_jobs_created_at', 'agent_jobs')
    op.drop_index('ix_agent_jobs_job_type', 'agent_jobs')
    op.drop_index('ix_agent_jobs_document_id', 'agent_jobs')
    op.drop_index('ix_agent_jobs_tenant_id', 'agent_jobs')
    op.drop_index('ix_agent_jobs_user_id', 'agent_jobs')
    op.drop_index('ix_agent_jobs_status', 'agent_jobs')
    op.drop_index('ix_agent_jobs_agent_id', 'agent_jobs')

    # Drop composite indexes for ai_prompts
    op.drop_index('ix_ai_prompts_tenant_active', 'ai_prompts')
    op.drop_index('ix_ai_prompts_doc_type_active', 'ai_prompts')

    # Drop single column indexes for ai_prompts
    op.drop_index('ix_ai_prompts_created_at', 'ai_prompts')
    op.drop_index('ix_ai_prompts_is_active', 'ai_prompts')
    op.drop_index('ix_ai_prompts_agent_id', 'ai_prompts')
    op.drop_index('ix_ai_prompts_tenant_id', 'ai_prompts')
    op.drop_index('ix_ai_prompts_category', 'ai_prompts')
    op.drop_index('ix_ai_prompts_prompt_type', 'ai_prompts')
    op.drop_index('ix_ai_prompts_doc_type', 'ai_prompts')

    # Drop composite indexes for ai_agents
    op.drop_index('ix_ai_agents_tenant_status', 'ai_agents')
    op.drop_index('ix_ai_agents_doc_type_status', 'ai_agents')

    # Drop single column indexes for ai_agents
    op.drop_index('ix_ai_agents_created_at', 'ai_agents')
    op.drop_index('ix_ai_agents_agent_type', 'ai_agents')
    op.drop_index('ix_ai_agents_tenant_id', 'ai_agents')
    op.drop_index('ix_ai_agents_status', 'ai_agents')
    op.drop_index('ix_ai_agents_doc_type', 'ai_agents')
