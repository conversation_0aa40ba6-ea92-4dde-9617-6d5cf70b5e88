"""Update document_types table schema

Revision ID: 4b701e415b03
Revises: 
Create Date: 2025-06-30 21:59:45.497421

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4b701e415b03'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_agents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('doc_type', sa.String(length=50), nullable=False),
    sa.Column('agent_type', sa.Enum('SYSTEM', 'CUSTOM', 'SPECIALIZED', name='agenttype'), nullable=True),
    sa.Column('model_name', sa.String(length=100), nullable=False),
    sa.Column('system_prompt', sa.Text(), nullable=False),
    sa.Column('temperature', sa.Integer(), nullable=True),
    sa.Column('max_tokens', sa.Integer(), nullable=True),
    sa.Column('capabilities', sa.JSON(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'DEPRECATED', name='agentstatus'), nullable=True),
    sa.Column('is_system_default', sa.Boolean(), nullable=True),
    sa.Column('version', sa.String(length=20), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_agents_doc_type'), 'ai_agents', ['doc_type'], unique=False)
    op.create_table('analytics_metrics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('metric_type', sa.String(length=50), nullable=False),
    sa.Column('metric_name', sa.String(length=100), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('document_type_id', sa.UUID(), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('period_type', sa.String(length=20), nullable=False),
    sa.Column('count_value', sa.Integer(), nullable=True),
    sa.Column('sum_value', sa.Float(), nullable=True),
    sa.Column('avg_value', sa.Float(), nullable=True),
    sa.Column('min_value', sa.Float(), nullable=True),
    sa.Column('max_value', sa.Float(), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_analytics_doctype_date', 'analytics_metrics', ['document_type_id', 'date'], unique=False)
    op.create_index('idx_analytics_tenant_date', 'analytics_metrics', ['tenant_id', 'date'], unique=False)
    op.create_index('idx_analytics_type_date', 'analytics_metrics', ['metric_type', 'date'], unique=False)
    op.create_index(op.f('ix_analytics_metrics_date'), 'analytics_metrics', ['date'], unique=False)
    op.create_index(op.f('ix_analytics_metrics_document_type_id'), 'analytics_metrics', ['document_type_id'], unique=False)
    op.create_index(op.f('ix_analytics_metrics_metric_type'), 'analytics_metrics', ['metric_type'], unique=False)
    op.create_index(op.f('ix_analytics_metrics_tenant_id'), 'analytics_metrics', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_analytics_metrics_user_id'), 'analytics_metrics', ['user_id'], unique=False)
    op.create_table('team_performance_metrics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('period_start', sa.Date(), nullable=False),
    sa.Column('period_end', sa.Date(), nullable=False),
    sa.Column('period_type', sa.String(length=20), nullable=False),
    sa.Column('active_users', sa.Integer(), nullable=True),
    sa.Column('total_users', sa.Integer(), nullable=True),
    sa.Column('new_users', sa.Integer(), nullable=True),
    sa.Column('documents_created', sa.Integer(), nullable=True),
    sa.Column('documents_completed', sa.Integer(), nullable=True),
    sa.Column('avg_completion_time_hours', sa.Float(), nullable=True),
    sa.Column('documents_shared', sa.Integer(), nullable=True),
    sa.Column('comments_made', sa.Integer(), nullable=True),
    sa.Column('avg_collaboration_score', sa.Float(), nullable=True),
    sa.Column('refinements_requested', sa.Integer(), nullable=True),
    sa.Column('avg_refinements_per_doc', sa.Float(), nullable=True),
    sa.Column('avg_document_rating', sa.Float(), nullable=True),
    sa.Column('ai_generations', sa.Integer(), nullable=True),
    sa.Column('ai_success_rate', sa.Float(), nullable=True),
    sa.Column('avg_ai_response_time', sa.Float(), nullable=True),
    sa.Column('exports_completed', sa.Integer(), nullable=True),
    sa.Column('most_popular_export_format', sa.String(length=50), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_team_performance_metrics_tenant_id'), 'team_performance_metrics', ['tenant_id'], unique=False)
    op.create_table('agent_jobs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('agent_id', sa.UUID(), nullable=False),
    sa.Column('job_type', sa.String(length=50), nullable=False),
    sa.Column('input_data', sa.JSON(), nullable=False),
    sa.Column('output_data', sa.JSON(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='jobstatus'), nullable=True),
    sa.Column('progress_percentage', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('execution_time_ms', sa.Integer(), nullable=True),
    sa.Column('tokens_used', sa.Integer(), nullable=True),
    sa.Column('model_used', sa.String(length=100), nullable=True),
    sa.Column('document_id', sa.UUID(), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['ai_agents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ai_prompts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('doc_type', sa.String(length=50), nullable=False),
    sa.Column('prompt_template', sa.Text(), nullable=False),
    sa.Column('variables', sa.JSON(), nullable=True),
    sa.Column('prompt_type', sa.String(length=50), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('agent_id', sa.UUID(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_system_default', sa.Boolean(), nullable=True),
    sa.Column('version', sa.String(length=20), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['ai_agents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_prompts_doc_type'), 'ai_prompts', ['doc_type'], unique=False)
    op.create_table('document_types',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('slug', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('industry', sa.String(), nullable=True),
    sa.Column('form_schema', sa.JSON(), nullable=False),
    sa.Column('template_structure', sa.JSON(), nullable=False),
    sa.Column('ai_agents', sa.JSON(), nullable=True),
    sa.Column('refinement_options', sa.JSON(), nullable=True),
    sa.Column('is_system_default', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('version', sa.String(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_types_slug'), 'document_types', ['slug'], unique=True)
    op.create_table('ai_effectiveness_metrics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('document_type_id', sa.UUID(), nullable=False),
    sa.Column('agent_id', sa.UUID(), nullable=True),
    sa.Column('period_start', sa.Date(), nullable=False),
    sa.Column('period_end', sa.Date(), nullable=False),
    sa.Column('period_type', sa.String(length=20), nullable=False),
    sa.Column('total_generations', sa.Integer(), nullable=True),
    sa.Column('successful_generations', sa.Integer(), nullable=True),
    sa.Column('failed_generations', sa.Integer(), nullable=True),
    sa.Column('avg_generation_time_seconds', sa.Float(), nullable=True),
    sa.Column('total_tokens_used', sa.Integer(), nullable=True),
    sa.Column('avg_tokens_per_generation', sa.Float(), nullable=True),
    sa.Column('avg_user_rating', sa.Float(), nullable=True),
    sa.Column('refinement_rate', sa.Float(), nullable=True),
    sa.Column('avg_refinements_per_doc', sa.Float(), nullable=True),
    sa.Column('positive_feedback_count', sa.Integer(), nullable=True),
    sa.Column('negative_feedback_count', sa.Integer(), nullable=True),
    sa.Column('satisfaction_score', sa.Float(), nullable=True),
    sa.Column('improvement_rate', sa.Float(), nullable=True),
    sa.Column('consistency_score', sa.Float(), nullable=True),
    sa.Column('primary_model_used', sa.String(length=100), nullable=True),
    sa.Column('model_performance_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['ai_agents.id'], ),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_effectiveness_metrics_tenant_id'), 'ai_effectiveness_metrics', ['tenant_id'], unique=False)
    op.create_table('documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('document_type_id', sa.UUID(), nullable=False),
    sa.Column('form_data', sa.JSON(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('version', sa.String(), nullable=True),
    sa.Column('version_number', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('shared_with', sa.JSON(), nullable=True),
    sa.Column('permissions', sa.JSON(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=True),
    sa.Column('last_viewed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('export_usage_metrics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('document_type_id', sa.UUID(), nullable=True),
    sa.Column('period_start', sa.Date(), nullable=False),
    sa.Column('period_end', sa.Date(), nullable=False),
    sa.Column('period_type', sa.String(length=20), nullable=False),
    sa.Column('pdf_exports', sa.Integer(), nullable=True),
    sa.Column('docx_exports', sa.Integer(), nullable=True),
    sa.Column('html_exports', sa.Integer(), nullable=True),
    sa.Column('confluence_exports', sa.Integer(), nullable=True),
    sa.Column('notion_exports', sa.Integer(), nullable=True),
    sa.Column('jira_exports', sa.Integer(), nullable=True),
    sa.Column('custom_exports', sa.Integer(), nullable=True),
    sa.Column('total_exports', sa.Integer(), nullable=True),
    sa.Column('successful_exports', sa.Integer(), nullable=True),
    sa.Column('failed_exports', sa.Integer(), nullable=True),
    sa.Column('avg_export_time_seconds', sa.Float(), nullable=True),
    sa.Column('most_popular_format', sa.String(length=50), nullable=True),
    sa.Column('peak_usage_hour', sa.Integer(), nullable=True),
    sa.Column('avg_file_size_mb', sa.Float(), nullable=True),
    sa.Column('integration_usage', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_export_usage_metrics_tenant_id'), 'export_usage_metrics', ['tenant_id'], unique=False)
    op.create_table('form_completion_metrics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('document_type_id', sa.UUID(), nullable=False),
    sa.Column('period_start', sa.Date(), nullable=False),
    sa.Column('period_end', sa.Date(), nullable=False),
    sa.Column('period_type', sa.String(length=20), nullable=False),
    sa.Column('total_form_starts', sa.Integer(), nullable=True),
    sa.Column('total_form_completions', sa.Integer(), nullable=True),
    sa.Column('completion_rate', sa.Float(), nullable=True),
    sa.Column('avg_completion_time_minutes', sa.Float(), nullable=True),
    sa.Column('avg_fields_completed', sa.Float(), nullable=True),
    sa.Column('most_abandoned_field', sa.String(length=100), nullable=True),
    sa.Column('validation_error_rate', sa.Float(), nullable=True),
    sa.Column('avg_session_duration_minutes', sa.Float(), nullable=True),
    sa.Column('bounce_rate', sa.Float(), nullable=True),
    sa.Column('return_rate', sa.Float(), nullable=True),
    sa.Column('question_metrics', sa.JSON(), nullable=True),
    sa.Column('mobile_completion_rate', sa.Float(), nullable=True),
    sa.Column('desktop_completion_rate', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_form_completion_metrics_tenant_id'), 'form_completion_metrics', ['tenant_id'], unique=False)
    op.create_table('document_analytics',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('document_type_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('generation_time_seconds', sa.Float(), nullable=True),
    sa.Column('ai_model_used', sa.String(length=100), nullable=True),
    sa.Column('tokens_used', sa.Integer(), nullable=True),
    sa.Column('generation_attempts', sa.Integer(), nullable=True),
    sa.Column('content_length', sa.Integer(), nullable=True),
    sa.Column('word_count', sa.Integer(), nullable=True),
    sa.Column('section_count', sa.Integer(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=True),
    sa.Column('edit_count', sa.Integer(), nullable=True),
    sa.Column('share_count', sa.Integer(), nullable=True),
    sa.Column('comment_count', sa.Integer(), nullable=True),
    sa.Column('refinement_count', sa.Integer(), nullable=True),
    sa.Column('export_count', sa.Integer(), nullable=True),
    sa.Column('rating_sum', sa.Integer(), nullable=True),
    sa.Column('rating_count', sa.Integer(), nullable=True),
    sa.Column('time_to_completion_minutes', sa.Float(), nullable=True),
    sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('form_completion_time_seconds', sa.Float(), nullable=True),
    sa.Column('form_field_count', sa.Integer(), nullable=True),
    sa.Column('form_validation_errors', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.ForeignKeyConstraint(['document_type_id'], ['document_types.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('document_id')
    )
    op.create_table('document_generation_sessions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('session_type', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('progress', sa.Integer(), nullable=True),
    sa.Column('original_form_data', sa.JSON(), nullable=False),
    sa.Column('followup_questions', sa.JSON(), nullable=True),
    sa.Column('followup_answers', sa.JSON(), nullable=True),
    sa.Column('generation_instructions', sa.Text(), nullable=True),
    sa.Column('generated_content', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('document_refinement_jobs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('job_type', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('progress', sa.Integer(), nullable=True),
    sa.Column('target', sa.String(), nullable=False),
    sa.Column('refinement_type', sa.String(), nullable=False),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('original_content', sa.Text(), nullable=True),
    sa.Column('refined_content', sa.Text(), nullable=True),
    sa.Column('estimated_completion', sa.DateTime(timezone=True), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('cancelled_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('document_versions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('version', sa.String(), nullable=False),
    sa.Column('version_number', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('form_data', sa.JSON(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('change_summary', sa.String(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('tenants', sa.Column('plan', sa.String(), nullable=True))
    op.add_column('tenants', sa.Column('theme', sa.String(), nullable=True))
    op.alter_column('tenants', 'settings',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True,
               existing_server_default=sa.text("'{}'::jsonb"))
    op.drop_index(op.f('idx_tenants_slug'), table_name='tenants')
    op.drop_constraint(op.f('tenants_slug_key'), 'tenants', type_='unique')
    op.create_index(op.f('ix_tenants_slug'), 'tenants', ['slug'], unique=True)
    op.drop_column('tenants', 'is_active')
    op.alter_column('users', 'preferences',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True,
               existing_server_default=sa.text('\'{"theme": "light", "language": "en", "timezone": "UTC", "notifications": {"marketing_emails": false, "push_notifications": true, "email_notifications": true}}\'::jsonb'))
    op.drop_index(op.f('idx_users_email'), table_name='users')
    op.drop_index(op.f('idx_users_is_active'), table_name='users')
    op.drop_index(op.f('idx_users_tenant_id'), table_name='users')
    op.drop_constraint(op.f('users_email_key'), 'users', type_='unique')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.drop_constraint(op.f('users_tenant_id_fkey'), 'users', type_='foreignkey')
    op.create_foreign_key(None, 'users', 'tenants', ['tenant_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.create_foreign_key(op.f('users_tenant_id_fkey'), 'users', 'tenants', ['tenant_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_unique_constraint(op.f('users_email_key'), 'users', ['email'], postgresql_nulls_not_distinct=False)
    op.create_index(op.f('idx_users_tenant_id'), 'users', ['tenant_id'], unique=False)
    op.create_index(op.f('idx_users_is_active'), 'users', ['is_active'], unique=False)
    op.create_index(op.f('idx_users_email'), 'users', ['email'], unique=False)
    op.alter_column('users', 'preferences',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True,
               existing_server_default=sa.text('\'{"theme": "light", "language": "en", "timezone": "UTC", "notifications": {"marketing_emails": false, "push_notifications": true, "email_notifications": true}}\'::jsonb'))
    op.add_column('tenants', sa.Column('is_active', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_tenants_slug'), table_name='tenants')
    op.create_unique_constraint(op.f('tenants_slug_key'), 'tenants', ['slug'], postgresql_nulls_not_distinct=False)
    op.create_index(op.f('idx_tenants_slug'), 'tenants', ['slug'], unique=False)
    op.alter_column('tenants', 'settings',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True,
               existing_server_default=sa.text("'{}'::jsonb"))
    op.drop_column('tenants', 'theme')
    op.drop_column('tenants', 'plan')
    op.drop_table('document_versions')
    op.drop_table('document_refinement_jobs')
    op.drop_table('document_generation_sessions')
    op.drop_table('document_analytics')
    op.drop_index(op.f('ix_form_completion_metrics_tenant_id'), table_name='form_completion_metrics')
    op.drop_table('form_completion_metrics')
    op.drop_index(op.f('ix_export_usage_metrics_tenant_id'), table_name='export_usage_metrics')
    op.drop_table('export_usage_metrics')
    op.drop_table('documents')
    op.drop_index(op.f('ix_ai_effectiveness_metrics_tenant_id'), table_name='ai_effectiveness_metrics')
    op.drop_table('ai_effectiveness_metrics')
    op.drop_index(op.f('ix_document_types_slug'), table_name='document_types')
    op.drop_table('document_types')
    op.drop_index(op.f('ix_ai_prompts_doc_type'), table_name='ai_prompts')
    op.drop_table('ai_prompts')
    op.drop_table('agent_jobs')
    op.drop_index(op.f('ix_team_performance_metrics_tenant_id'), table_name='team_performance_metrics')
    op.drop_table('team_performance_metrics')
    op.drop_index(op.f('ix_analytics_metrics_user_id'), table_name='analytics_metrics')
    op.drop_index(op.f('ix_analytics_metrics_tenant_id'), table_name='analytics_metrics')
    op.drop_index(op.f('ix_analytics_metrics_metric_type'), table_name='analytics_metrics')
    op.drop_index(op.f('ix_analytics_metrics_document_type_id'), table_name='analytics_metrics')
    op.drop_index(op.f('ix_analytics_metrics_date'), table_name='analytics_metrics')
    op.drop_index('idx_analytics_type_date', table_name='analytics_metrics')
    op.drop_index('idx_analytics_tenant_date', table_name='analytics_metrics')
    op.drop_index('idx_analytics_doctype_date', table_name='analytics_metrics')
    op.drop_table('analytics_metrics')
    op.drop_index(op.f('ix_ai_agents_doc_type'), table_name='ai_agents')
    op.drop_table('ai_agents')
    # ### end Alembic commands ###
