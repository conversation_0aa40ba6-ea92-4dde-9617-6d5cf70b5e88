"""Add workflow tables

Revision ID: add_workflow_tables
Revises: 4b701e415b03
Create Date: 2025-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_workflow_tables'
down_revision = '4b701e415b03'
branch_labels = None
depends_on = None


def upgrade():
    # Create document_workflow_states table
    op.create_table('document_workflow_states',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('document_id', sa.UUID(), nullable=False),
        sa.Column('workflow_template_id', sa.UUID(), nullable=True),
        sa.Column('current_state', sa.String(), nullable=False),
        sa.Column('previous_state', sa.String(), nullable=True),
        sa.Column('assigned_to', sa.UUID(), nullable=True),
        sa.Column('assigned_role', sa.String(), nullable=True),
        sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('workflow_data', sa.JSON(), nullable=True),
        sa.Column('priority', sa.String(), nullable=True),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('tenant_id', sa.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create workflow_templates table
    op.create_table('workflow_templates',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('workflow_steps', sa.JSON(), nullable=False),
        sa.Column('default_assignments', sa.JSON(), nullable=True),
        sa.Column('automation_rules', sa.JSON(), nullable=True),
        sa.Column('document_types', sa.JSON(), nullable=True),
        sa.Column('conditions', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('created_by', sa.UUID(), nullable=False),
        sa.Column('tenant_id', sa.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create document_workflow_assignments table
    op.create_table('document_workflow_assignments',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('document_id', sa.UUID(), nullable=False),
        sa.Column('workflow_state_id', sa.UUID(), nullable=False),
        sa.Column('assigned_to', sa.UUID(), nullable=False),
        sa.Column('assigned_by', sa.UUID(), nullable=False),
        sa.Column('assignment_type', sa.String(), nullable=False),
        sa.Column('task_description', sa.Text(), nullable=True),
        sa.Column('instructions', sa.Text(), nullable=True),
        sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('priority', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('completion_notes', sa.Text(), nullable=True),
        sa.Column('tenant_id', sa.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
        sa.ForeignKeyConstraint(['workflow_state_id'], ['document_workflow_states.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create document_workflow_comments table
    op.create_table('document_workflow_comments',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('document_id', sa.UUID(), nullable=False),
        sa.Column('workflow_state_id', sa.UUID(), nullable=True),
        sa.Column('assignment_id', sa.UUID(), nullable=True),
        sa.Column('comment_text', sa.Text(), nullable=False),
        sa.Column('comment_type', sa.String(), nullable=True),
        sa.Column('section_reference', sa.String(), nullable=True),
        sa.Column('line_reference', sa.Integer(), nullable=True),
        sa.Column('context_data', sa.JSON(), nullable=True),
        sa.Column('is_resolved', sa.Boolean(), nullable=True),
        sa.Column('resolution_notes', sa.Text(), nullable=True),
        sa.Column('resolved_by', sa.UUID(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by', sa.UUID(), nullable=False),
        sa.Column('tenant_id', sa.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['assignment_id'], ['document_workflow_assignments.id'], ),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
        sa.ForeignKeyConstraint(['workflow_state_id'], ['document_workflow_states.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraint for workflow_template_id in document_workflow_states
    op.create_foreign_key(
        'fk_document_workflow_states_workflow_template_id',
        'document_workflow_states', 'workflow_templates',
        ['workflow_template_id'], ['id']
    )
    
    # Create indexes for better performance
    op.create_index('ix_document_workflow_states_document_id', 'document_workflow_states', ['document_id'])
    op.create_index('ix_document_workflow_states_tenant_id', 'document_workflow_states', ['tenant_id'])
    op.create_index('ix_workflow_templates_tenant_id', 'workflow_templates', ['tenant_id'])
    op.create_index('ix_document_workflow_assignments_document_id', 'document_workflow_assignments', ['document_id'])
    op.create_index('ix_document_workflow_assignments_assigned_to', 'document_workflow_assignments', ['assigned_to'])
    op.create_index('ix_document_workflow_comments_document_id', 'document_workflow_comments', ['document_id'])
    op.create_index('ix_document_workflow_comments_created_by', 'document_workflow_comments', ['created_by'])


def downgrade():
    # Drop indexes
    op.drop_index('ix_document_workflow_comments_created_by')
    op.drop_index('ix_document_workflow_comments_document_id')
    op.drop_index('ix_document_workflow_assignments_assigned_to')
    op.drop_index('ix_document_workflow_assignments_document_id')
    op.drop_index('ix_workflow_templates_tenant_id')
    op.drop_index('ix_document_workflow_states_tenant_id')
    op.drop_index('ix_document_workflow_states_document_id')
    
    # Drop foreign key constraint
    op.drop_constraint('fk_document_workflow_states_workflow_template_id', 'document_workflow_states', type_='foreignkey')
    
    # Drop tables
    op.drop_table('document_workflow_comments')
    op.drop_table('document_workflow_assignments')
    op.drop_table('workflow_templates')
    op.drop_table('document_workflow_states')
