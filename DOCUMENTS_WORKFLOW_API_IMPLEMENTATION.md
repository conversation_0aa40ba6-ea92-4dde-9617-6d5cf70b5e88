# Documents Workflow API Implementation

## Overview

The Documents Workflow API provides comprehensive workflow management capabilities for document generation, review, approval, and collaboration processes. This implementation extends the existing document generation workflow with advanced features like approval chains, workflow templates, assignments, and collaborative commenting.

## 🚀 Features Implemented

### 1. Core Workflow Management
- **Document Generation Workflow**: Form submission, validation, follow-up questions, regeneration
- **Workflow State Management**: Track document states through approval processes
- **Workflow Templates**: Reusable workflow definitions for different document types
- **Assignment Management**: Assign tasks to users for review, approval, or editing
- **Collaborative Comments**: Add comments and feedback during workflow processes

### 2. Database Tables Created

#### Core Workflow Tables
- ✅ `document_workflow_states` - Track document workflow states and transitions
- ✅ `workflow_templates` - Define reusable workflow templates
- ✅ `document_workflow_assignments` - Manage workflow assignments and tasks
- ✅ `document_workflow_comments` - Handle comments and feedback

#### Existing Document Tables (Enhanced)
- ✅ `documents` - Main document storage with workflow integration
- ✅ `document_versions` - Version history with workflow transitions
- ✅ `document_generation_sessions` - AI generation process tracking
- ✅ `document_refinement_jobs` - Document refinement task management

### 3. API Endpoints

#### Document Generation Workflow
```http
POST /api/v1/documents/form/submit
POST /api/v1/documents/form/validate
GET  /api/v1/documents/form/schema/{type_id}
POST /api/v1/documents/{doc_id}/followup-questions
POST /api/v1/documents/{doc_id}/followup-answers
GET  /api/v1/documents/{doc_id}/generation-status
POST /api/v1/documents/{doc_id}/regenerate
```

#### Workflow State Management
```http
GET  /api/v1/documents/{doc_id}/workflow-state
POST /api/v1/documents/{doc_id}/workflow-state
```

#### Workflow Assignments
```http
GET  /api/v1/documents/{doc_id}/workflow-assignments
POST /api/v1/documents/{doc_id}/workflow-assignments
PUT  /api/v1/documents/{doc_id}/workflow-assignments/{assignment_id}
```

#### Workflow Comments
```http
GET  /api/v1/documents/{doc_id}/workflow-comments
POST /api/v1/documents/{doc_id}/workflow-comments
PUT  /api/v1/documents/{doc_id}/workflow-comments/{comment_id}
```

#### Workflow Templates
```http
GET  /api/v1/documents/templates
POST /api/v1/documents/templates
GET  /api/v1/documents/templates/{template_id}
POST /api/v1/documents/{doc_id}/apply-template/{template_id}
```

## 🔧 Database Schema

### DocumentWorkflowState
```sql
- id (UUID, Primary Key)
- document_id (FK to Document)
- workflow_template_id (FK to WorkflowTemplate, Optional)
- current_state, previous_state
- assigned_to, assigned_role, due_date
- workflow_data (JSON), priority
- user_id, tenant_id
- created_at, updated_at
```

### WorkflowTemplate
```sql
- id (UUID, Primary Key)
- name, description
- workflow_steps (JSON), default_assignments (JSON)
- automation_rules (JSON), document_types (JSON)
- conditions (JSON)
- is_active, is_default
- created_by, tenant_id
- created_at, updated_at
```

### DocumentWorkflowAssignment
```sql
- id (UUID, Primary Key)
- document_id (FK), workflow_state_id (FK)
- assigned_to, assigned_by, assignment_type
- task_description, instructions, due_date, priority
- status, completion_notes
- tenant_id
- created_at, updated_at, completed_at
```

### DocumentWorkflowComment
```sql
- id (UUID, Primary Key)
- document_id (FK), workflow_state_id (FK), assignment_id (FK)
- comment_text, comment_type
- section_reference, line_reference, context_data
- is_resolved, resolution_notes, resolved_by, resolved_at
- created_by, tenant_id
- created_at, updated_at
```

## 📋 Usage Examples

### 1. Submit Form and Generate Document
```json
POST /api/v1/documents/form/submit
{
  "document_type_id": "uuid",
  "form_data": {
    "title": "Product Requirements Document",
    "description": "New feature requirements"
  },
  "title": "PRD for Feature X",
  "save_as_draft": false
}
```

### 2. Update Workflow State
```json
POST /api/v1/documents/{doc_id}/workflow-state
{
  "new_state": "review",
  "assigned_to": "reviewer_user_id",
  "assigned_role": "reviewer",
  "due_date": "2025-01-15T10:00:00Z",
  "priority": "high",
  "transition_notes": "Ready for technical review"
}
```

### 3. Create Workflow Assignment
```json
POST /api/v1/documents/{doc_id}/workflow-assignments
{
  "assigned_to": "user_id",
  "assignment_type": "review",
  "task_description": "Review technical specifications",
  "instructions": "Focus on feasibility and implementation details",
  "due_date": "2025-01-15T17:00:00Z",
  "priority": "high"
}
```

### 4. Add Workflow Comment
```json
POST /api/v1/documents/{doc_id}/workflow-comments
{
  "comment_text": "The user story needs more detail about edge cases",
  "comment_type": "suggestion",
  "section_reference": "User Stories",
  "context_data": {
    "line_number": 45,
    "suggestion_type": "enhancement"
  }
}
```

### 5. Create Workflow Template
```json
POST /api/v1/documents/templates
{
  "name": "Standard PRD Approval",
  "description": "Standard approval workflow for PRDs",
  "workflow_steps": [
    {"state": "draft", "name": "Initial Draft"},
    {"state": "review", "name": "Technical Review"},
    {"state": "approval", "name": "Stakeholder Approval"},
    {"state": "published", "name": "Published"}
  ],
  "default_assignments": {
    "review": {"role": "technical_reviewer"},
    "approval": {"role": "product_manager"}
  },
  "document_types": ["prd_uuid"],
  "is_default": true
}
```

## 🔐 Security & Permissions

### Permission Levels
- **Document Owner**: Full access to all workflow operations
- **Can Edit**: Can update workflow states, create assignments, add comments
- **Can View**: Can view workflow states and comments
- **Can Comment**: Can add comments but not change workflow states
- **Assigned Users**: Can update their specific assignments

### Multi-tenant Isolation
- All workflow data is isolated by tenant_id
- Users can only access workflows within their tenant
- Workflow templates are tenant-specific

## 🚀 Getting Started

### 1. Database Migration
```bash
# Run the new workflow tables migration
alembic upgrade head

# Or use the comprehensive setup script
python create_database_tables.py
```

### 2. API Integration
The workflow APIs are automatically included in the main documents router:
```python
# Already included in app/api/v1/documents.py
router.include_router(workflow_router, tags=["documents-workflow"])
```

### 3. Testing Workflow APIs
```bash
# Start the server
python -m uvicorn app.main:app --reload --port 8000

# Test workflow endpoints
curl -X GET "http://localhost:8000/api/v1/documents/{doc_id}/workflow-state" \
  -H "Authorization: Bearer {token}"
```

## 🔄 Workflow States

### Standard Document States
- **draft**: Initial document creation
- **review**: Under review by assigned reviewers
- **approval**: Awaiting approval from stakeholders
- **published**: Approved and published
- **archived**: No longer active
- **rejected**: Rejected during review/approval

### Assignment Types
- **review**: Review document content
- **approve**: Approve/reject document
- **edit**: Make edits to document
- **comment**: Provide feedback

### Comment Types
- **general**: General feedback
- **suggestion**: Improvement suggestions
- **issue**: Problems or concerns
- **approval**: Approval comments
- **rejection**: Rejection reasons

## 📈 Next Steps

1. **Testing**: Create comprehensive tests for all workflow functionality
2. **Notifications**: Add email/webhook notifications for workflow events
3. **Analytics**: Track workflow performance and bottlenecks
4. **Automation**: Implement automated workflow transitions
5. **Integration**: Connect with external approval systems

This implementation provides a complete workflow management system that can handle complex document approval processes while maintaining flexibility for different organizational needs.
