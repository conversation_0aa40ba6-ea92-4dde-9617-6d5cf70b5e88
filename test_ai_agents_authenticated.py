#!/usr/bin/env python3
"""
Authenticated test script for AI Agents API endpoints.
Creates a test user and token to test protected endpoints.
"""

import requests
import json
import sys
import uuid
from datetime import datetime, timedelta
from jose import jwt
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def create_test_user_and_token():
    """Create a test user and generate a JWT token for testing."""
    try:
        # Get database connection
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("❌ DATABASE_URL not found")
            return None
        
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Test user details
        user_id = str(uuid.uuid4())
        tenant_id = "00000000-0000-0000-0000-000000000000"  # Default tenant
        email = "<EMAIL>"
        
        try:
            # Check if user exists and delete if so
            result = db.execute(text("SELECT COUNT(*) FROM users WHERE email = :email"), {"email": email})
            if result.scalar() > 0:
                db.execute(text("DELETE FROM users WHERE email = :email"), {"email": email})
                db.commit()
            
            # Create test user
            db.execute(text("""
                INSERT INTO users (id, email, full_name, hashed_password, tenant_id, role, is_active)
                VALUES (:id, :email, :full_name, :hashed_password, :tenant_id, :role, :is_active)
            """), {
                "id": user_id,
                "email": email,
                "full_name": "Test User",
                "hashed_password": "$2b$12$dummy.hash.for.testing",
                "tenant_id": tenant_id,
                "role": "admin",  # Admin role to test admin endpoints
                "is_active": True
            })
            db.commit()
            print(f"✅ Created test user: {email}")
            
            # Generate JWT token
            secret_key = os.getenv('SECRET_KEY', 'test-secret-key')
            expire = datetime.utcnow() + timedelta(minutes=30)
            to_encode = {"exp": expire, "sub": user_id, "iat": datetime.utcnow()}
            token = jwt.encode(to_encode, secret_key, algorithm="HS256")
            
            print(f"✅ Generated JWT token")
            return token
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error creating test user: {str(e)}")
        return None

def test_authenticated_endpoints(token):
    """Test AI agents endpoints with authentication."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n🧪 Testing Authenticated AI Agents Endpoints")
    print("=" * 50)
    
    # Test 1: Get agents for PRD
    print("📋 Testing: GET /agents/prd")
    try:
        response = requests.get(f"{BASE_URL}/agents/prd", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            agents = data.get("agents", [])
            print(f"   Found {len(agents)} agents")
            for agent in agents[:3]:  # Show first 3
                print(f"   - {agent['name']} ({agent['agent_type']})")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    # Test 2: Get specific agent details
    print("\n📋 Testing: GET /agents/prd/{agent_id}")
    try:
        # First get list of agents to get an ID
        response = requests.get(f"{BASE_URL}/agents/prd", headers=headers)
        if response.status_code == 200:
            agents = response.json().get("agents", [])
            if agents:
                agent_id = agents[0]["id"]
                response = requests.get(f"{BASE_URL}/agents/prd/{agent_id}", headers=headers)
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    agent = response.json()
                    print(f"   Agent: {agent['name']}")
                    print(f"   Model: {agent['model_name']}")
                    print(f"   Capabilities: {list(agent['capabilities'].keys())}")
                else:
                    print(f"   Error: {response.text}")
            else:
                print("   No agents found to test with")
        else:
            print("   Could not get agents list")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    # Test 3: Get prompts for PRD
    print("\n📝 Testing: GET /prompts/prd")
    try:
        response = requests.get(f"{BASE_URL}/prompts/prd", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            prompts = data.get("prompts", [])
            print(f"   Found {len(prompts)} prompts")
            for prompt in prompts:
                print(f"   - {prompt['name']} ({prompt['prompt_type']})")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    # Test 4: Get agent capabilities
    print("\n🔧 Testing: GET /agents/capabilities/prd")
    try:
        response = requests.get(f"{BASE_URL}/agents/capabilities/prd", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            capabilities = data.get("capabilities", {})
            operations = data.get("supported_operations", [])
            print(f"   Available capabilities: {list(capabilities.keys())}")
            print(f"   Supported operations: {operations}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    # Test 5: Test other document types
    print("\n📋 Testing other document types:")
    for doc_type in ["brd", "technical_spec"]:
        try:
            response = requests.get(f"{BASE_URL}/agents/{doc_type}", headers=headers)
            print(f"   {doc_type}: Status {response.status_code}")
            if response.status_code == 200:
                agents = response.json().get("agents", [])
                print(f"      Found {len(agents)} agents")
        except Exception as e:
            print(f"   {doc_type}: Exception {str(e)}")

def cleanup_test_user():
    """Clean up the test user."""
    try:
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            return
        
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        db.execute(text("DELETE FROM users WHERE email = :email"), {"email": "<EMAIL>"})
        db.commit()
        db.close()
        print("🧹 Cleaned up test user")
        
    except Exception as e:
        print(f"⚠️  Error cleaning up test user: {str(e)}")

def main():
    """Run authenticated tests."""
    print("🚀 AI Agents API Authenticated Test Suite")
    print("=" * 50)
    
    # Create test user and token
    token = create_test_user_and_token()
    if not token:
        print("❌ Failed to create test user and token")
        return
    
    try:
        # Run authenticated tests
        test_authenticated_endpoints(token)
        
        print("\n🎉 Authenticated test suite completed!")
        print("\n📊 Summary:")
        print("- Created test user with admin role")
        print("- Generated JWT token for authentication")
        print("- Tested protected AI agents endpoints")
        print("- Verified system agents and prompts are accessible")
        
    finally:
        # Clean up
        cleanup_test_user()

if __name__ == "__main__":
    main()
