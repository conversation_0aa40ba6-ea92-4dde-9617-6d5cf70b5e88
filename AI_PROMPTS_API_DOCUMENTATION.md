# AI-Prompts API Documentation

## Overview

The AI-Prompts API provides a comprehensive, modular interface for managing AI agents, prompts, execution, and capabilities. The API is organized into four main modules to keep files small and logically organized:

- **Agents**: AI agent CRUD operations and management
- **Prompts**: AI prompt CRUD operations and template management
- **Execution**: Agent job execution and status tracking
- **Capabilities**: Agent capabilities and system integrations

## Base URL

All endpoints are prefixed with: `/api/v1/ai-prompts`

## Authentication

All endpoints require authentication. Include the authorization header:
```
Authorization: Bearer <your-token>
```

## Modules

### 1. Agents Module (`/agents`)

Manages AI agents with proper tenant isolation and admin permissions.

#### Endpoints

**GET `/agents/{doc_type}`**
- Get all agents for a document type
- Query parameters: `status`, `agent_type`, `limit`, `offset`
- Response: `AgentListResponse`

**GET `/agents/{doc_type}/{agent_id}`**
- Get specific agent details
- Response: `AgentResponse`

**POST `/agents/{doc_type}`**
- Create a new agent (admin only)
- Request body: `AgentCreate`
- Response: `AgentResponse`

**PUT `/agents/{doc_type}/{agent_id}`**
- Update agent configuration (admin only)
- Request body: `AgentUpdate`
- Response: `AgentResponse`

**DELETE `/agents/{doc_type}/{agent_id}`**
- Delete an agent (admin only)
- Response: `MessageResponse`

#### Example: Create Agent

```json
POST /api/v1/ai-prompts/agents/prd
{
  "name": "PRD Expert Agent",
  "description": "Specialized agent for creating comprehensive PRDs",
  "doc_type": "prd",
  "agent_type": "custom",
  "model_name": "claude-3-5-sonnet-20241022",
  "system_prompt": "You are an expert product manager...",
  "temperature": 30,
  "max_tokens": 4000,
  "capabilities": {
    "document_generation": true,
    "refinement": true,
    "follow_up_questions": true,
    "suggestions": true
  },
  "parameters": {
    "focus_area": "technical_requirements"
  }
}
```

### 2. Prompts Module (`/prompts`)

Manages AI prompts with template handling and variable management.

#### Endpoints

**GET `/prompts/{doc_type}`**
- Get all prompts for a document type
- Query parameters: `prompt_type`, `category`, `is_active`, `limit`, `offset`
- Response: `PromptListResponse`

**GET `/prompts/{doc_type}/{prompt_id}`**
- Get specific prompt details
- Response: `PromptResponse`

**POST `/prompts/{doc_type}`**
- Create a new prompt (admin only)
- Request body: `PromptCreate`
- Response: `PromptResponse`

**PUT `/prompts/{doc_type}/{prompt_id}`**
- Update prompt configuration (admin only)
- Request body: `PromptUpdate`
- Response: `PromptResponse`

**DELETE `/prompts/{doc_type}/{prompt_id}`**
- Delete a prompt (admin only)
- Response: `MessageResponse`

#### Example: Create Prompt

```json
POST /api/v1/ai-prompts/prompts/prd
{
  "name": "PRD Generation Template",
  "description": "Template for generating comprehensive PRDs",
  "doc_type": "prd",
  "prompt_template": "Create a {doc_type} for {product_name} focusing on {focus_area}. Include {sections}.",
  "variables": ["doc_type", "product_name", "focus_area", "sections"],
  "prompt_type": "generation",
  "category": "business",
  "agent_id": "agent-uuid-here"
}
```

### 3. Execution Module (`/execution`)

Handles agent job execution with async processing and status tracking.

#### Endpoints

**POST `/execution/{doc_type}/{agent_id}/execute`**
- Execute a specific agent
- Request body: `AgentExecuteRequest`
- Response: `AgentJobResponse`

**GET `/execution/jobs/{job_id}`**
- Get agent job status
- Response: `AgentJobResponse`

**POST `/execution/jobs/{job_id}/cancel`**
- Cancel an agent job
- Response: `MessageResponse`

#### Example: Execute Agent

```json
POST /api/v1/ai-prompts/execution/prd/agent-uuid/execute
{
  "input_data": {
    "prompt": "Create a PRD for a mobile app",
    "product_name": "TaskMaster Pro",
    "focus_area": "user_experience"
  },
  "document_id": "doc-uuid-optional",
  "job_type": "execute"
}
```

### 4. Capabilities Module (`/capabilities`)

Manages agent capabilities discovery and operation validation.

#### Endpoints

**GET `/capabilities/{doc_type}`**
- Get agent capabilities for a document type
- Query parameters: `include_inactive`
- Response: `AgentCapabilitiesResponse`

**GET `/capabilities/{doc_type}/operations`**
- Get supported operations with agent details
- Response: `Dict[str, List[str]]`

**GET `/capabilities/{doc_type}/validate/{operation}`**
- Validate if an operation is supported
- Query parameters: `agent_id` (optional)
- Response: Operation validation result

#### Example: Get Capabilities

```json
GET /api/v1/ai-prompts/capabilities/prd

Response:
{
  "doc_type": "prd",
  "available_agents": [...],
  "capabilities": {
    "document_generation": true,
    "refinement": true,
    "follow_up_questions": true,
    "suggestions": true
  },
  "supported_operations": ["generate", "execute", "refine", "follow_up", "suggest"]
}
```

## Data Models

### AgentCreate
```json
{
  "name": "string",
  "description": "string (optional)",
  "doc_type": "string",
  "agent_type": "custom|system|specialized",
  "model_name": "string",
  "system_prompt": "string",
  "temperature": "integer (0-100)",
  "max_tokens": "integer (100-8000)",
  "capabilities": "object (optional)",
  "parameters": "object (optional)"
}
```

### PromptCreate
```json
{
  "name": "string",
  "description": "string (optional)",
  "doc_type": "string",
  "prompt_template": "string",
  "variables": "array of strings",
  "prompt_type": "string",
  "category": "string (optional)",
  "agent_id": "uuid (optional)"
}
```

### AgentExecuteRequest
```json
{
  "input_data": "object",
  "document_id": "uuid (optional)",
  "job_type": "string (default: execute)"
}
```

## Error Handling

All endpoints return standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

Error responses include detailed messages:
```json
{
  "detail": "Error description"
}
```

## Rate Limiting

API endpoints are subject to rate limiting based on your subscription tier.

## Migration from Old Structure

The new modular structure replaces the old individual endpoints:
- Old: `/api/v1/agents/` → New: `/api/v1/ai-prompts/agents/`
- Old: `/api/v1/prompts/` → New: `/api/v1/ai-prompts/prompts/`
- Old: `/api/v1/agents/{doc_type}/{agent_id}/execute` → New: `/api/v1/ai-prompts/execution/{doc_type}/{agent_id}/execute`

All functionality remains the same, just organized in a more modular structure.
