# Documents CRUD API Setup and Usage Guide

This guide provides comprehensive instructions for setting up and using the Documents CRUD API in the PRD Generator system.

## 🚀 Quick Start

### 1. Database Setup

The Documents CRUD API requires a PostgreSQL database with all necessary tables. Use the provided migration tools:

```bash
# Check database status
python migrate_database.py --check

# Run database migration (creates all tables)
python migrate_database.py --migrate

# Alternative: Use the comprehensive setup script
python create_database_tables.py
```

### 2. Start the API Server

```bash
# Start the FastAPI server
python -m uvicorn app.main:app --reload --port 8000

# The API will be available at:
# - Main API: http://localhost:8000
# - Documentation: http://localhost:8000/docs
# - ReDoc: http://localhost:8000/redoc
```

### 3. Test the API

```bash
# Run the test suite
python test_documents_api.py
```

## 📊 Database Schema

### Core Tables

The Documents CRUD API uses the following core tables:

#### `documents`
- **Purpose**: Stores document content and metadata
- **Key Fields**: 
  - `id` (UUID, Primary Key)
  - `title` (String, Document title)
  - `content` (Text, Document content)
  - `document_type_id` (UUID, Foreign Key to document_types)
  - `form_data` (JSON, Form data used to generate document)
  - `status` (String, Document status: draft, review, approved, published, archived)
  - `user_id` (UUID, Document owner)
  - `tenant_id` (UUID, Tenant association)

#### `document_types`
- **Purpose**: Defines document templates and configurations
- **Key Fields**:
  - `id` (UUID, Primary Key)
  - `name` (String, Document type name)
  - `slug` (String, URL-friendly identifier)
  - `form_schema` (JSON, Form configuration)
  - `template_structure` (JSON, Document template)
  - `is_system_default` (Boolean, System vs custom type)

#### `document_versions`
- **Purpose**: Tracks document version history
- **Key Fields**:
  - `id` (UUID, Primary Key)
  - `document_id` (UUID, Foreign Key to documents)
  - `version` (String, Version identifier)
  - `content` (Text, Content snapshot)
  - `change_summary` (String, Description of changes)

#### Supporting Tables
- `document_generation_sessions` - Tracks document generation processes
- `document_refinement_jobs` - Manages document refinement tasks
- `users` - User management
- `tenants` - Multi-tenant support

## 🔧 API Endpoints

### Core CRUD Operations

#### Create Document
```http
POST /api/v1/documents/generate
Content-Type: application/json
Authorization: Bearer <token>

{
  "document_type_id": "uuid",
  "form_data": {
    "title": "My Document",
    "description": "Document description"
  },
  "title": "Optional custom title"
}
```

#### List Documents
```http
GET /api/v1/documents/
Authorization: Bearer <token>

# Query parameters:
# - document_type_id: Filter by document type
# - status: Filter by status
# - limit: Number of results (max 100)
# - offset: Pagination offset
```

#### Get Document
```http
GET /api/v1/documents/{doc_id}
Authorization: Bearer <token>
```

#### Update Document
```http
PUT /api/v1/documents/{doc_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "title": "Updated title",
  "content": "Updated content",
  "form_data": {}
}
```

#### Delete Document (Soft Delete)
```http
DELETE /api/v1/documents/{doc_id}
Authorization: Bearer <token>
```

### Advanced Operations

#### Duplicate Document
```http
POST /api/v1/documents/{doc_id}/duplicate
Content-Type: application/json
Authorization: Bearer <token>

{
  "title": "Copy of Original",
  "copy_permissions": false
}
```

#### Update Document Status
```http
PUT /api/v1/documents/{doc_id}/status
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "published",
  "change_summary": "Ready for publication"
}
```

#### Get Document History
```http
GET /api/v1/documents/{doc_id}/history
Authorization: Bearer <token>
```

#### Restore Previous Version
```http
POST /api/v1/documents/{doc_id}/restore/{version}
Authorization: Bearer <token>
```

#### Convert Document Type
```http
POST /api/v1/documents/{doc_id}/convert/{target_type_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "preserve_content": true,
  "update_form_data": {}
}
```

## 🔐 Authentication

All document endpoints require authentication. The API uses JWT tokens for authentication:

1. **Sign up/Sign in** through the auth endpoints
2. **Include the JWT token** in the Authorization header: `Bearer <token>`
3. **Ensure proper tenant association** - documents are scoped to tenants

## 📝 Document Types

### Default Document Types

The system comes with default document types:

1. **Product Requirements Document (PRD)**
   - ID: `11111111-1111-1111-1111-111111111111`
   - Slug: `prd`
   - Category: `product`

2. **API Documentation**
   - ID: `22222222-2222-2222-2222-222222222222`
   - Slug: `api-doc`
   - Category: `technical`

### Managing Document Types

```http
# List document types
GET /api/v1/document-types/

# Get specific document type
GET /api/v1/document-types/{type_id}

# Create custom document type
POST /api/v1/document-types/
```

## 🧪 Testing

### Automated Tests

Run the provided test suite:

```bash
python test_documents_api.py
```

### Manual Testing

1. **Start the server**: `uvicorn app.main:app --reload`
2. **Open API docs**: http://localhost:8000/docs
3. **Test endpoints** using the interactive documentation

### Test Data

Use the default document type IDs for testing:
- PRD: `11111111-1111-1111-1111-111111111111`
- API Doc: `22222222-2222-2222-2222-222222222222`

## 🔧 Configuration

### Environment Variables

Required environment variables (in `.env` file):

```env
# Database
DATABASE_URL=postgresql://user:password@localhost/dbname

# Supabase (for auth)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_KEY=your_service_key
SUPABASE_JWT_SECRET=your_jwt_secret

# AI Service
ANTHROPIC_API_KEY=your_anthropic_key

# App
SECRET_KEY=your_secret_key
```

### Database Connection

The API uses SQLAlchemy with PostgreSQL. Ensure your database is running and accessible.

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `DATABASE_URL` in `.env`
   - Ensure PostgreSQL is running
   - Verify database exists and is accessible

2. **Authentication Errors**
   - Verify Supabase configuration
   - Check JWT token validity
   - Ensure user has proper tenant association

3. **Missing Tables**
   - Run migration: `python migrate_database.py --migrate`
   - Check database status: `python migrate_database.py --check`

4. **Import Errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python path and virtual environment

### Logs and Debugging

- Check server logs for detailed error messages
- Use `--reload` flag for development
- Enable debug mode in FastAPI for detailed error responses

## 📚 Next Steps

1. **Set up authentication** - Create users and tenants
2. **Create custom document types** - Define your own templates
3. **Integrate with frontend** - Connect your UI to the API
4. **Configure AI services** - Set up Anthropic API for document generation
5. **Add export functionality** - Enable document export to various formats

For more detailed API documentation, visit http://localhost:8000/docs when the server is running.
