# Supabase Email Confirmation Setup

This guide will help you configure Supabase to properly handle email confirmations and redirects for your authentication system.

## 🔧 **Supabase Dashboard Configuration**

### 1. **Configure Redirect URLs**

1. Go to your Supabase project dashboard: https://supabase.com/dashboard/projects
2. Select your project: `wbvcbsufhvlqhbgzzvtg`
3. Navigate to **Authentication** → **URL Configuration**
4. Update the following settings:

#### **Site URL**
```
http://localhost:8000
```

#### **Redirect URLs** (Add all of these)
```
http://localhost:8000/auth/confirm
http://localhost:8000/static/email-confirmation.html
http://localhost:3000/auth/callback
http://localhost:3000/auth/confirm
```

### 2. **Email Templates (Optional)**

1. Go to **Authentication** → **Email Templates**
2. Customize the **Confirm signup** template
3. Update the confirmation URL to:
```
{{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=signup
```

### 3. **Auth Settings**

1. Go to **Authentication** → **Settings**
2. Ensure these settings:
   - **Enable email confirmations**: ✅ Enabled
   - **Enable email change confirmations**: ✅ Enabled
   - **Secure email change**: ✅ Enabled (recommended)

## 🧪 **Testing Email Confirmation**

### 1. **Register a New User**

```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "full_name": "Test User",
    "tenant_id": "00000000-0000-0000-0000-000000000000"
  }'
```

### 2. **Check Your Email**

You should receive an email with a confirmation link that looks like:
```
http://localhost:8000/auth/confirm?token_hash=...&type=signup
```

### 3. **Click the Confirmation Link**

The link will:
1. Open the confirmation page at `http://localhost:8000/auth/confirm`
2. Automatically verify your email with the backend API
3. Show a success message
4. Provide a link to continue to your app

## 🔗 **Available Endpoints**

### **Email Confirmation Endpoints**

- `GET /auth/confirm` - Email confirmation page (HTML)
- `GET /api/v1/auth/confirm` - Email confirmation API endpoint
- `GET /api/v1/auth/verify-email/{token}` - Direct token verification
- `POST /api/v1/auth/resend-verification` - Resend verification email

### **Testing Endpoints**

```bash
# Check if email confirmation page is accessible
curl http://localhost:8000/auth/confirm

# Test API confirmation endpoint
curl "http://localhost:8000/api/v1/auth/confirm?token_hash=YOUR_TOKEN&type=signup"

# Resend verification email
curl -X POST "http://localhost:8000/api/v1/auth/resend-verification" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **"Invalid email address" error**
   - Supabase may have domain restrictions
   - Try using a real email domain (gmail.com, etc.)
   - Check if your Supabase project allows the email domain

2. **Redirect goes to localhost:3000**
   - Update the Site URL in Supabase dashboard
   - Add proper redirect URLs in Authentication settings

3. **Email not received**
   - Check spam folder
   - Verify email settings in Supabase dashboard
   - Check Supabase logs for email delivery issues

4. **Confirmation link doesn't work**
   - Ensure redirect URLs are properly configured
   - Check that the FastAPI server is running on port 8000
   - Verify the static files are being served correctly

### **Debug Steps**

1. **Check Supabase Logs**
   - Go to your Supabase dashboard
   - Navigate to **Logs** → **Auth Logs**
   - Look for email confirmation events

2. **Test API Endpoints**
   ```bash
   # Test health endpoint
   curl http://localhost:8000/health
   
   # Test auth status
   curl http://localhost:8000/api/v1/auth/monitoring/status
   ```

3. **Check Server Logs**
   - Look at your FastAPI server console output
   - Check for any error messages during email confirmation

## 🎯 **Production Setup**

For production deployment, update the URLs to your actual domain:

### **Site URL**
```
https://yourdomain.com
```

### **Redirect URLs**
```
https://yourdomain.com/auth/confirm
https://yourdomain.com/auth/callback
https://app.yourdomain.com/auth/confirm
```

### **Email Template**
```
{{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=signup
```

## ✅ **Verification Checklist**

- [ ] Supabase Site URL configured
- [ ] Redirect URLs added to Supabase
- [ ] Email confirmation enabled in Supabase
- [ ] FastAPI server running on correct port
- [ ] Static files being served correctly
- [ ] Email confirmation page accessible
- [ ] API endpoints responding correctly
- [ ] Test user registration successful
- [ ] Email received and confirmation link works

Once you've completed this setup, your email confirmation flow should work seamlessly!
