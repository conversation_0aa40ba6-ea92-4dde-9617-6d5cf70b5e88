#!/usr/bin/env python3
"""
Simple test to verify auth system without Supabase Auth.
This tests just the local database operations.
"""

import os
import sys
import uuid
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Load environment variables
load_dotenv()

def test_local_user_creation():
    """Test creating a user directly in the local database."""
    try:
        # Get database URL
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("❌ DATABASE_URL not found in environment variables")
            return False
        
        # Create engine and session
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        print("🧪 Testing local user creation...")
        print("=" * 50)
        
        # Test data
        user_id = str(uuid.uuid4())
        email = "<EMAIL>"
        full_name = "Test User"
        tenant_id = "00000000-0000-0000-0000-000000000000"
        
        try:
            # Check if user already exists
            result = db.execute(text("""
                SELECT COUNT(*) FROM users WHERE email = :email
            """), {"email": email})
            
            count = result.scalar()
            
            if count > 0:
                print(f"ℹ️  User with email {email} already exists, deleting first...")
                db.execute(text("DELETE FROM users WHERE email = :email"), {"email": email})
                db.commit()
            
            # Create user
            db.execute(text("""
                INSERT INTO users (id, email, full_name, hashed_password, tenant_id, role)
                VALUES (:id, :email, :full_name, :hashed_password, :tenant_id, :role)
            """), {
                "id": user_id,
                "email": email,
                "full_name": full_name,
                "hashed_password": "$2b$12$dummy.hash.for.testing",
                "tenant_id": tenant_id,
                "role": "member"
            })
            
            db.commit()
            print(f"✅ User created successfully with ID: {user_id}")
            
            # Verify user was created
            result = db.execute(text("""
                SELECT id, email, full_name, role, tenant_id 
                FROM users 
                WHERE email = :email
            """), {"email": email})
            
            user = result.fetchone()
            
            if user:
                print("✅ User verification successful:")
                print(f"   - ID: {user[0]}")
                print(f"   - Email: {user[1]}")
                print(f"   - Full Name: {user[2]}")
                print(f"   - Role: {user[3]}")
                print(f"   - Tenant ID: {user[4]}")
                return True
            else:
                print("❌ User verification failed - user not found after creation")
                return False
                
        except Exception as e:
            print(f"❌ Error during user creation: {str(e)}")
            db.rollback()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Database connection error: {str(e)}")
        return False

def test_login_endpoint():
    """Test the login endpoint with a simple request."""
    try:
        import requests
        
        print("\n🔐 Testing login endpoint...")
        print("=" * 50)
        
        # Test login with OAuth2 format (which doesn't use Supabase Auth)
        response = requests.post(
            "http://localhost:8000/api/v1/auth/login/oauth",
            data={
                "username": "<EMAIL>",
                "password": "SecurePass123!"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Login endpoint working!")
            return True
        else:
            print("⚠️  Login failed (expected - user doesn't have correct password)")
            return True  # This is expected since we used a dummy hash
            
    except Exception as e:
        print(f"❌ Error testing login endpoint: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🚀 Simple Auth System Test")
    print("=" * 50)
    
    # Test local database operations
    if test_local_user_creation():
        print("\n✅ Local database operations working!")
    else:
        print("\n❌ Local database operations failed!")
        return
    
    # Test login endpoint
    if test_login_endpoint():
        print("\n✅ Login endpoint accessible!")
    else:
        print("\n❌ Login endpoint failed!")
        return
    
    print("\n🎉 Basic auth system components are working!")
    print("\nNext steps:")
    print("1. Check Supabase project settings for email restrictions")
    print("2. Try registering with a real email domain")
    print("3. Check Supabase Auth settings in your dashboard")

if __name__ == "__main__":
    main()
