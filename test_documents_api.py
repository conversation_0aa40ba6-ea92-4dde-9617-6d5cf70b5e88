#!/usr/bin/env python3
"""
Test script for the Documents CRUD API.
This script tests the core functionality of the documents API endpoints.
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

BASE_URL = "http://127.0.0.1:8000"
API_BASE = f"{BASE_URL}/api/v1"

class DocumentsAPITester:
    """Test class for Documents CRUD API."""
    
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        
    def test_connection(self) -> bool:
        """Test basic API connection."""
        try:
            response = self.session.get(BASE_URL)
            if response.status_code == 200:
                print("✅ API connection successful")
                return True
            else:
                print(f"❌ API connection failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API connection error: {str(e)}")
            return False
    
    def test_openapi_schema(self) -> bool:
        """Test that OpenAPI schema is available."""
        try:
            response = self.session.get(f"{API_BASE}/openapi.json")
            if response.status_code == 200:
                schema = response.json()
                document_paths = [path for path in schema.get('paths', {}).keys() if 'documents' in path]
                print(f"✅ OpenAPI schema available with {len(document_paths)} document endpoints")
                return True
            else:
                print(f"❌ OpenAPI schema not available: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ OpenAPI schema error: {str(e)}")
            return False
    
    def test_document_types_endpoint(self) -> bool:
        """Test document types endpoint (should require auth)."""
        try:
            response = self.session.get(f"{API_BASE}/document-types/")
            if response.status_code in [401, 403]:
                print("✅ Document types endpoint properly requires authentication")
                return True
            elif response.status_code == 200:
                print("⚠️  Document types endpoint accessible without auth (might be intended)")
                return True
            else:
                print(f"❌ Document types endpoint unexpected response: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Document types endpoint error: {str(e)}")
            return False

    def test_documents_endpoint(self) -> bool:
        """Test documents endpoint (should require auth)."""
        try:
            response = self.session.get(f"{API_BASE}/documents/")
            if response.status_code in [401, 403]:
                print("✅ Documents endpoint properly requires authentication")
                return True
            elif response.status_code == 200:
                print("⚠️  Documents endpoint accessible without auth (might be intended)")
                return True
            else:
                print(f"❌ Documents endpoint unexpected response: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Documents endpoint error: {str(e)}")
            return False

    def test_document_generation_endpoint(self) -> bool:
        """Test document generation endpoint (should require auth)."""
        try:
            test_data = {
                "document_type_id": "11111111-1111-1111-1111-111111111111",
                "form_data": {"title": "Test Document", "description": "Test description"},
                "title": "Test PRD"
            }

            response = self.session.post(
                f"{API_BASE}/documents/generate",
                json=test_data,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code in [401, 403]:
                print("✅ Document generation endpoint properly requires authentication")
                return True
            elif response.status_code == 422:
                print("✅ Document generation endpoint validates input (validation error expected)")
                return True
            else:
                print(f"❌ Document generation endpoint unexpected response: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Document generation endpoint error: {str(e)}")
            return False
    
    def test_health_endpoints(self) -> bool:
        """Test various health and info endpoints."""
        endpoints_to_test = [
            ("/", "Root endpoint"),
            ("/docs", "API documentation"),
            ("/redoc", "ReDoc documentation"),
        ]
        
        all_passed = True
        
        for endpoint, description in endpoints_to_test:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}")
                if response.status_code == 200:
                    print(f"✅ {description} accessible")
                else:
                    print(f"❌ {description} failed: {response.status_code}")
                    all_passed = False
            except Exception as e:
                print(f"❌ {description} error: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def run_all_tests(self) -> bool:
        """Run all tests."""
        print("🚀 Testing Documents CRUD API")
        print("=" * 50)
        
        tests = [
            ("API Connection", self.test_connection),
            ("OpenAPI Schema", self.test_openapi_schema),
            ("Health Endpoints", self.test_health_endpoints),
            ("Document Types Endpoint", self.test_document_types_endpoint),
            ("Documents Endpoint", self.test_documents_endpoint),
            ("Document Generation Endpoint", self.test_document_generation_endpoint),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing {test_name}...")
            try:
                if test_func():
                    passed += 1
                else:
                    print(f"❌ {test_name} failed")
            except Exception as e:
                print(f"❌ {test_name} error: {str(e)}")
        
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Documents CRUD API is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            return False


def main():
    """Main function."""
    tester = DocumentsAPITester()
    
    if tester.run_all_tests():
        print("\n✅ Documents CRUD API is ready for use!")
        print("\nNext steps:")
        print("1. Set up authentication (users and tenants)")
        print("2. Create document types")
        print("3. Start generating documents")
        print("4. Test with authenticated requests")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the API setup.")
        sys.exit(1)


if __name__ == "__main__":
    main()
