#!/usr/bin/env python3
"""
Test script to verify workflow API setup and basic functionality.
Run this after setting up the database to ensure everything works correctly.
"""

import sys
import os
import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_tables():
    """Test that all workflow tables exist in the database."""
    print("🔍 Testing database tables...")
    
    try:
        from app.core.config import settings
        
        engine = create_engine(settings.DATABASE_URL)
        
        # Test table existence
        workflow_tables = [
            'document_workflow_states',
            'workflow_templates', 
            'document_workflow_assignments',
            'document_workflow_comments'
        ]
        
        with engine.connect() as conn:
            for table in workflow_tables:
                result = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    );
                """))
                exists = result.scalar()
                
                if exists:
                    print(f"  ✅ Table '{table}' exists")
                else:
                    print(f"  ❌ Table '{table}' missing")
                    return False
        
        print("✅ All workflow tables exist!")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False


def test_model_imports():
    """Test that all workflow models can be imported."""
    print("\n🔍 Testing model imports...")
    
    try:
        from app.models.document import (
            Document, DocumentVersion, DocumentGenerationSession, DocumentRefinementJob,
            DocumentWorkflowState, WorkflowTemplate, DocumentWorkflowAssignment, DocumentWorkflowComment
        )
        print("  ✅ Document models imported successfully")
        
        from app.schemas.document import (
            WorkflowStateResponse, WorkflowTemplateResponse, WorkflowAssignmentResponse,
            WorkflowCommentResponse, WorkflowStateUpdateRequest, WorkflowTemplateCreateRequest,
            WorkflowAssignmentCreateRequest, WorkflowCommentCreateRequest
        )
        print("  ✅ Workflow schemas imported successfully")
        
        print("✅ All model imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Model import test failed: {str(e)}")
        return False


def test_api_imports():
    """Test that workflow API endpoints can be imported."""
    print("\n🔍 Testing API imports...")
    
    try:
        from app.api.v1.document_workflow import router
        print("  ✅ Workflow router imported successfully")
        
        # Check that router has the expected endpoints
        routes = [route.path for route in router.routes]
        expected_paths = [
            '/{doc_id}/workflow-state',
            '/{doc_id}/workflow-assignments',
            '/{doc_id}/workflow-comments',
            '/templates'
        ]
        
        for path in expected_paths:
            if any(path in route for route in routes):
                print(f"  ✅ Route '{path}' found")
            else:
                print(f"  ⚠️  Route '{path}' not found in router")
        
        print("✅ API import test completed!")
        return True
        
    except Exception as e:
        print(f"❌ API import test failed: {str(e)}")
        return False


def test_database_connection():
    """Test basic database connectivity."""
    print("\n🔍 Testing database connection...")
    
    try:
        from app.core.database import get_db, engine
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("  ✅ Database connection successful")
                return True
            else:
                print("  ❌ Database connection failed")
                return False
                
    except Exception as e:
        print(f"❌ Database connection test failed: {str(e)}")
        return False


def test_table_structure():
    """Test that workflow tables have the expected structure."""
    print("\n🔍 Testing table structure...")
    
    try:
        from app.core.config import settings
        engine = create_engine(settings.DATABASE_URL)
        
        # Test document_workflow_states table structure
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'document_workflow_states'
                ORDER BY ordinal_position;
            """))
            
            columns = result.fetchall()
            expected_columns = ['id', 'document_id', 'workflow_template_id', 'current_state', 'previous_state']
            
            found_columns = [col[0] for col in columns]
            
            for expected_col in expected_columns:
                if expected_col in found_columns:
                    print(f"  ✅ Column '{expected_col}' found in document_workflow_states")
                else:
                    print(f"  ❌ Column '{expected_col}' missing from document_workflow_states")
                    return False
        
        print("✅ Table structure test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Table structure test failed: {str(e)}")
        return False


def main():
    """Run all workflow setup tests."""
    print("🚀 Starting Workflow API Setup Tests\n")
    
    tests = [
        test_database_connection,
        test_model_imports,
        test_api_imports,
        test_database_tables,
        test_table_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Workflow API is ready to use.")
        print("\n📋 Next steps:")
        print("1. Start the API server: python -m uvicorn app.main:app --reload --port 8000")
        print("2. Test the endpoints using the examples in DOCUMENTS_WORKFLOW_API_IMPLEMENTATION.md")
        print("3. Create workflow templates for your document types")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure database is running and accessible")
        print("2. Run database migrations: python create_database_tables.py")
        print("3. Check that all dependencies are installed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
