#!/usr/bin/env python3
"""
Test script for the Documents Refinement API
"""
import requests
import json
import uuid
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000/api/v1"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_api_health():
    """Test if the API server is running"""
    try:
        response = requests.get(f"{BASE_URL.replace('/api/v1', '')}/health")
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"❌ API server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        return False

def test_refinement_endpoints():
    """Test refinement API endpoints structure"""
    print("\n🔍 Testing Refinement API Endpoints...")
    
    # Test OpenAPI documentation
    try:
        response = requests.get(f"{BASE_URL}/openapi.json")
        if response.status_code == 200:
            openapi_spec = response.json()
            
            # Check for refinement endpoints
            paths = openapi_spec.get("paths", {})
            refinement_endpoints = [
                path for path in paths.keys() 
                if "refine" in path or "refinement" in path
            ]
            
            print(f"✅ Found {len(refinement_endpoints)} refinement endpoints:")
            for endpoint in refinement_endpoints:
                methods = list(paths[endpoint].keys())
                print(f"   - {endpoint} ({', '.join(methods).upper()})")
            
            return len(refinement_endpoints) > 0
        else:
            print(f"❌ Cannot fetch OpenAPI spec: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")
        return False

def test_database_tables():
    """Test if refinement database tables exist"""
    print("\n🗄️  Testing Database Tables...")
    
    try:
        # Import database modules
        import sys
        import os
        sys.path.append(os.getcwd())
        
        from app.core.database import get_db
        from app.models.document import DocumentRefinementJob
        from sqlalchemy import text
        
        db = next(get_db())
        
        # Check if refinement jobs table exists
        result = db.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'document_refinement_jobs'
        """))
        
        if result.fetchone():
            print("✅ document_refinement_jobs table exists")
            
            # Check table structure
            result = db.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'document_refinement_jobs'
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            print(f"✅ Table has {len(columns)} columns:")
            for col_name, col_type in columns:
                print(f"   - {col_name}: {col_type}")
            
            return True
        else:
            print("❌ document_refinement_jobs table not found")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_ai_service():
    """Test if AI service is configured"""
    print("\n🤖 Testing AI Service Configuration...")
    
    try:
        import sys
        import os
        sys.path.append(os.getcwd())
        
        from app.services.anthropic_service import anthropic_service
        from app.core.config import settings
        
        # Check if API key is configured
        if hasattr(settings, 'ANTHROPIC_API_KEY') and settings.ANTHROPIC_API_KEY:
            print("✅ Anthropic API key is configured")
            
            # Check if service has required methods
            required_methods = [
                'refine_section',
                'refine_component', 
                'custom_refinement',
                'generate_refinement_suggestions',
                'apply_suggestions'
            ]
            
            missing_methods = []
            for method in required_methods:
                if not hasattr(anthropic_service, method):
                    missing_methods.append(method)
            
            if not missing_methods:
                print("✅ All required AI service methods are available")
                return True
            else:
                print(f"❌ Missing AI service methods: {missing_methods}")
                return False
        else:
            print("❌ Anthropic API key not configured")
            return False
            
    except Exception as e:
        print(f"❌ AI service test failed: {e}")
        return False

def test_schema_validation():
    """Test schema definitions"""
    print("\n📋 Testing Schema Definitions...")
    
    try:
        import sys
        import os
        sys.path.append(os.getcwd())
        
        from app.schemas.document import (
            SectionRefinementRequest,
            ComponentRefinementRequest,
            CustomRefinementRequest,
            RefinementJobResponse,
            RefinementJobListResponse,
            RefinementHistoryResponse
        )
        
        print("✅ All refinement schemas imported successfully")
        
        # Test schema creation
        section_request = SectionRefinementRequest(
            section_name="Test Section",
            refinement_type="expand",
            instructions="Test instructions",
            target_length="detailed",
            tone="professional"
        )
        print("✅ SectionRefinementRequest schema validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Documents Refinement API Test Suite")
    print("=" * 50)
    
    tests = [
        ("API Health", test_api_health),
        ("Refinement Endpoints", test_refinement_endpoints),
        ("Database Tables", test_database_tables),
        ("AI Service", test_ai_service),
        ("Schema Validation", test_schema_validation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Documents Refinement API is ready!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
