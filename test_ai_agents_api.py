#!/usr/bin/env python3
"""
Test script for AI Agents API endpoints.
Tests the basic functionality of the AI agents and prompts APIs.
"""

import requests
import json
import sys
from typing import Dict, Any

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_get_agents_for_doc_type():
    """Test getting agents for a specific document type."""
    print("🧪 Testing: GET /agents/{doc_type}")
    
    doc_types = ["prd", "brd", "technical_spec"]
    
    for doc_type in doc_types:
        try:
            response = requests.get(f"{BASE_URL}/agents/{doc_type}")
            print(f"   📋 {doc_type}: Status {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                agents = data.get("agents", [])
                print(f"      Found {len(agents)} agents")
                for agent in agents:
                    print(f"      - {agent['name']} ({agent['agent_type']})")
            elif response.status_code == 401:
                print("      ⚠️  Authentication required (expected for protected endpoints)")
            else:
                print(f"      ❌ Error: {response.text}")
                
        except Exception as e:
            print(f"      ❌ Exception: {str(e)}")
    
    print()

def test_get_prompts_for_doc_type():
    """Test getting prompts for a specific document type."""
    print("🧪 Testing: GET /prompts/{doc_type}")
    
    doc_types = ["prd", "brd", "technical_spec"]
    
    for doc_type in doc_types:
        try:
            response = requests.get(f"{BASE_URL}/prompts/{doc_type}")
            print(f"   📝 {doc_type}: Status {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                prompts = data.get("prompts", [])
                print(f"      Found {len(prompts)} prompts")
                for prompt in prompts:
                    print(f"      - {prompt['name']} ({prompt['prompt_type']})")
            elif response.status_code == 401:
                print("      ⚠️  Authentication required (expected for protected endpoints)")
            else:
                print(f"      ❌ Error: {response.text}")
                
        except Exception as e:
            print(f"      ❌ Exception: {str(e)}")
    
    print()

def test_agent_capabilities():
    """Test getting agent capabilities for document types."""
    print("🧪 Testing: GET /agents/capabilities/{doc_type}")
    
    doc_types = ["prd", "brd", "technical_spec"]
    
    for doc_type in doc_types:
        try:
            response = requests.get(f"{BASE_URL}/agents/capabilities/{doc_type}")
            print(f"   🔧 {doc_type}: Status {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                capabilities = data.get("capabilities", {})
                print(f"      Available capabilities: {list(capabilities.keys())}")
            elif response.status_code == 401:
                print("      ⚠️  Authentication required (expected for protected endpoints)")
            else:
                print(f"      ❌ Error: {response.text}")
                
        except Exception as e:
            print(f"      ❌ Exception: {str(e)}")
    
    print()

def test_api_health():
    """Test basic API health."""
    print("🧪 Testing: API Health Check")
    
    try:
        # Test root endpoint
        response = requests.get("http://localhost:8000/")
        print(f"   🏠 Root endpoint: Status {response.status_code}")
        if response.status_code == 200:
            print(f"      Response: {response.json()}")
        
        # Test health endpoint
        response = requests.get("http://localhost:8000/health")
        print(f"   ❤️  Health endpoint: Status {response.status_code}")
        if response.status_code == 200:
            print(f"      Response: {response.json()}")
            
    except Exception as e:
        print(f"      ❌ Exception: {str(e)}")
    
    print()

def test_openapi_docs():
    """Test OpenAPI documentation endpoint."""
    print("🧪 Testing: OpenAPI Documentation")
    
    try:
        response = requests.get(f"{BASE_URL}/openapi.json")
        print(f"   📚 OpenAPI JSON: Status {response.status_code}")
        
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get("paths", {})
            agent_paths = [path for path in paths.keys() if "/agents" in path or "/prompts" in path]
            print(f"      Found {len(agent_paths)} AI agent/prompt endpoints")
            for path in sorted(agent_paths)[:5]:  # Show first 5
                print(f"      - {path}")
            if len(agent_paths) > 5:
                print(f"      ... and {len(agent_paths) - 5} more")
        else:
            print(f"      ❌ Error: {response.text}")
            
    except Exception as e:
        print(f"      ❌ Exception: {str(e)}")
    
    print()

def main():
    """Run all tests."""
    print("🚀 AI Agents API Test Suite")
    print("=" * 50)
    print("Testing AI Agents and Prompts API endpoints...")
    print()
    
    # Run tests
    test_api_health()
    test_openapi_docs()
    test_get_agents_for_doc_type()
    test_get_prompts_for_doc_type()
    test_agent_capabilities()
    
    print("🎉 Test suite completed!")
    print("\n📝 Notes:")
    print("- Authentication errors (401) are expected for protected endpoints")
    print("- This tests basic endpoint availability and response structure")
    print("- For full testing, authentication tokens would be needed")

if __name__ == "__main__":
    main()
