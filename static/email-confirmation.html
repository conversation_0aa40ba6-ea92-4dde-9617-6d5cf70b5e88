<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Confirmation - PRD Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success {
            color: #22c55e;
        }
        .error {
            color: #ef4444;
        }
        .loading {
            color: #3b82f6;
        }
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .button {
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Confirmation</h1>
        <div id="status" class="loading">
            <div class="spinner"></div>
            Confirming your email address...
        </div>
        <div id="message"></div>
        <div id="actions" style="display: none;">
            <a href="http://localhost:3000" class="button">Continue to App</a>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const tokenHash = urlParams.get('token_hash');
        const type = urlParams.get('type');
        const accessToken = urlParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token');

        async function confirmEmail() {
            try {
                // Build confirmation URL
                const confirmUrl = new URL('http://localhost:8000/api/v1/auth/confirm');
                
                if (tokenHash) confirmUrl.searchParams.set('token_hash', tokenHash);
                if (type) confirmUrl.searchParams.set('type', type);
                if (accessToken) confirmUrl.searchParams.set('access_token', accessToken);
                if (refreshToken) confirmUrl.searchParams.set('refresh_token', refreshToken);

                const response = await fetch(confirmUrl);
                const data = await response.json();

                const statusDiv = document.getElementById('status');
                const messageDiv = document.getElementById('message');
                const actionsDiv = document.getElementById('actions');

                if (response.ok) {
                    statusDiv.className = 'success';
                    statusDiv.innerHTML = '✅ Email Confirmed!';
                    messageDiv.innerHTML = `<p>${data.message}</p>`;
                    actionsDiv.style.display = 'block';
                } else {
                    statusDiv.className = 'error';
                    statusDiv.innerHTML = '❌ Confirmation Failed';
                    messageDiv.innerHTML = `<p>Error: ${data.detail || 'Unknown error occurred'}</p>`;
                }
            } catch (error) {
                const statusDiv = document.getElementById('status');
                const messageDiv = document.getElementById('message');
                
                statusDiv.className = 'error';
                statusDiv.innerHTML = '❌ Confirmation Failed';
                messageDiv.innerHTML = `<p>Network error: ${error.message}</p>`;
            }
        }

        // Auto-confirm when page loads
        confirmEmail();
    </script>
</body>
</html>
