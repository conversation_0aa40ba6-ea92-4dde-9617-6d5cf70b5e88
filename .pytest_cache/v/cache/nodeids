["test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_duplicate_slug", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_forbidden_non_admin", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_invalid_data", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_success_admin", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_delete_document_type_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_ai_agents_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_by_category_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_by_industry_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_document_type_not_found", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_document_type_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_form_schema_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_refinement_options_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_template_structure_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_list_document_types_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_list_document_types_with_filters", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_update_document_type_forbidden_non_admin", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_update_document_type_success", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_cancel_completed_job", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_cancel_job_success", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_agent_inactive_agent", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_agent_invalid_agent", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_agent_no_generation_capability", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_agent_success", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_batch_agents_success", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_with_retry_failure", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_execute_with_retry_success", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_get_execution_metrics", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_get_job_status_not_found", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_get_job_status_success", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_get_user_jobs", "tests/services/test_agent_execution_service.py::TestAgentExecutionService::test_get_user_jobs_with_status_filter"]