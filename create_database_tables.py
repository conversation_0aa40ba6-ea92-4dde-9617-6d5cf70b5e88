#!/usr/bin/env python3
"""
Create database tables using SQLAlchemy models.
This script will create all the necessary tables for the complete PRD Generator system,
including documents, document types, users, tenants, and all related functionality.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def import_all_models():
    """Import all models to ensure they are registered with SQLAlchemy."""
    try:
        # Core models
        from app.models.user import User
        from app.models.tenant import Tenant

        # Document-related models
        from app.models.document import (
            Document, DocumentVersion, DocumentGenerationSession, DocumentRefinementJob,
            DocumentWorkflowState, WorkflowTemplate, DocumentWorkflowAssignment, DocumentWorkflowComment
        )
        from app.models.document_type import DocumentType

        # AI and Agent models
        from app.models.agent import <PERSON>Agent, AIPrompt, AgentJob, AgentStatus, AgentType

        # Analytics models
        from app.models.analytics import (
            AnalyticsMetric, DocumentAnalytics, TeamPerformanceMetrics,
            AIEffectivenessMetrics, ExportUsageMetrics, FormCompletionMetrics
        )

        # Export models
        from app.models.export import ExportJob, ExportFile, ExportHistory, ExportIntegration

        # Form and template models
        from app.models.form_schema import FormSchema, FormQuestion, FormValidationLog
        from app.models.template import DocumentTemplate, ComponentLibrary, SectionLibrary, ComponentUsageLog, SectionUsageLog

        # Learning models
        from app.models.learning import LearningProfile, AIFeedback, LearnedPattern, LearningSession

        print("✅ All models imported successfully!")
        return True

    except ImportError as e:
        print(f"⚠️  Could not import some models: {str(e)}")
        print("   This might be expected if some models don't exist yet.")
        return True
    except Exception as e:
        print(f"❌ Error importing models: {str(e)}")
        return False

def create_tables():
    """Create all database tables."""
    try:
        # Import models first
        if not import_all_models():
            return False

        # Import after adding to path
        from app.core.database import Base, engine

        print("🔧 Creating database tables...")
        print("=" * 50)

        # Create all tables
        Base.metadata.create_all(bind=engine)

        print("✅ Database tables created successfully!")

        # Create a session to insert default data
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            # Check if default tenant exists
            result = db.execute(text("SELECT COUNT(*) FROM tenants WHERE id = '00000000-0000-0000-0000-000000000000'"))
            count = result.scalar()

            if count == 0:
                # Insert default tenant
                db.execute(text("""
                    INSERT INTO tenants (id, name, slug, plan, settings, theme, created_at, updated_at)
                    VALUES (
                        '00000000-0000-0000-0000-000000000000',
                        'Default Tenant',
                        'default',
                        'free',
                        '{}',
                        'default',
                        NOW(),
                        NOW()
                    )
                """))
                db.commit()
                print("✅ Default tenant created!")
            else:
                print("ℹ️  Default tenant already exists")

        except Exception as e:
            print(f"⚠️  Could not create default tenant: {str(e)}")
            db.rollback()
        finally:
            db.close()

        return True

    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
        return False

def verify_tables():
    """Verify that all expected tables were created."""
    try:
        from app.core.database import engine

        print("\n📋 Verifying created tables...")

        # Expected core tables for documents CRUD API
        expected_tables = [
            'users', 'tenants', 'documents', 'document_versions', 'document_types',
            'document_generation_sessions', 'document_refinement_jobs',
            'document_workflow_states', 'workflow_templates', 'document_workflow_assignments', 'document_workflow_comments',
            'ai_agents', 'ai_prompts', 'agent_jobs',
            'export_jobs', 'export_files', 'export_history', 'export_integrations',
            'form_schemas', 'form_questions', 'form_validation_logs',
            'document_templates', 'component_library', 'section_library',
            'learning_profiles', 'ai_feedback', 'learned_patterns', 'learning_sessions',
            'analytics_metrics', 'document_analytics', 'team_performance_metrics',
            'ai_effectiveness_metrics', 'export_usage_metrics', 'form_completion_metrics'
        ]

        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))

            actual_tables = [row[0] for row in result]

            print("✅ Created tables:")
            for table in sorted(actual_tables):
                status = "✓" if table in expected_tables else "?"
                print(f"   {status} {table}")

            # Check for missing critical tables
            missing_critical = []
            critical_tables = ['users', 'tenants', 'documents', 'document_types', 'document_versions']

            for table in critical_tables:
                if table not in actual_tables:
                    missing_critical.append(table)

            if missing_critical:
                print(f"\n❌ Missing critical tables: {', '.join(missing_critical)}")
                return False
            else:
                print(f"\n✅ All critical tables created successfully!")
                print(f"   Total tables: {len(actual_tables)}")
                return True

    except Exception as e:
        print(f"❌ Error verifying tables: {str(e)}")
        return False

def check_table_structure():
    """Check the structure of key tables."""
    try:
        from app.core.database import engine

        print("\n🔍 Checking key table structures...")

        # Check documents table structure
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'documents'
                    AND table_schema = 'public'
                ORDER BY ordinal_position
            """))

            columns = list(result)
            if columns:
                print("✅ Documents table columns:")
                for col_name, data_type, nullable in columns:
                    print(f"   - {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
            else:
                print("⚠️  Documents table not found or has no columns")

        # Check document_types table structure
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'document_types'
                    AND table_schema = 'public'
                ORDER BY ordinal_position
            """))

            columns = list(result)
            if columns:
                print("\n✅ Document Types table columns:")
                for col_name, data_type, nullable in columns:
                    print(f"   - {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
            else:
                print("⚠️  Document Types table not found or has no columns")

        return True

    except Exception as e:
        print(f"❌ Error checking table structure: {str(e)}")
        return False

def test_connection():
    """Test database connection."""
    try:
        from app.core.database import engine

        print("🔗 Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("✅ Database connection successful!")
                return True
            else:
                print("❌ Database connection test failed!")
                return False

    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False


def create_default_ai_agents():
    """Create default system AI agents for common document types."""
    try:
        from app.core.database import SessionLocal
        from app.models.agent import AIAgent, AgentStatus, AgentType

        db = SessionLocal()

        # Check if system agents already exist
        existing_agents = db.query(AIAgent).filter(AIAgent.is_system_default == True).count()
        if existing_agents > 0:
            print(f"ℹ️  {existing_agents} system agents already exist, skipping creation")
            db.close()
            return True

        print("🤖 Creating default system AI agents...")

        # Default system agents configuration
        default_agents = [
            {
                "name": "PRD Generator",
                "description": "System agent specialized in generating Product Requirements Documents",
                "doc_type": "prd",
                "model_name": "claude-3-5-sonnet-20241022",
                "system_prompt": """You are an expert Product Manager AI assistant specialized in creating comprehensive Product Requirements Documents (PRDs).

Your role is to:
- Analyze user requirements and business objectives
- Structure information into clear, actionable PRD sections
- Ensure technical feasibility and business alignment
- Create detailed user stories and acceptance criteria
- Define clear success metrics and KPIs

Always maintain a professional, analytical tone and focus on creating documents that drive successful product development.""",
                "temperature": 20,
                "max_tokens": 4000,
                "capabilities": {
                    "document_generation": True,
                    "requirements_analysis": True,
                    "user_story_creation": True,
                    "technical_specification": True,
                    "business_analysis": True
                }
            },
            {
                "name": "BRD Generator",
                "description": "System agent specialized in generating Business Requirements Documents",
                "doc_type": "brd",
                "model_name": "claude-3-5-sonnet-20241022",
                "system_prompt": """You are an expert Business Analyst AI assistant specialized in creating comprehensive Business Requirements Documents (BRDs).

Your role is to:
- Analyze business needs and stakeholder requirements
- Define clear business objectives and success criteria
- Document functional and non-functional requirements
- Create process flows and business rules
- Ensure alignment between business goals and technical solutions

Focus on creating documents that bridge the gap between business strategy and technical implementation.""",
                "temperature": 25,
                "max_tokens": 4000,
                "capabilities": {
                    "document_generation": True,
                    "business_analysis": True,
                    "process_modeling": True,
                    "stakeholder_analysis": True,
                    "requirements_gathering": True
                }
            },
            {
                "name": "Technical Spec Generator",
                "description": "System agent specialized in generating Technical Specification documents",
                "doc_type": "technical_spec",
                "model_name": "claude-3-5-sonnet-20241022",
                "system_prompt": """You are an expert Technical Architect AI assistant specialized in creating detailed Technical Specification documents.

Your role is to:
- Translate business requirements into technical specifications
- Design system architecture and component interactions
- Define APIs, data models, and integration patterns
- Specify performance, security, and scalability requirements
- Create implementation guidelines and best practices

Maintain technical accuracy while ensuring clarity for both technical and non-technical stakeholders.""",
                "temperature": 15,
                "max_tokens": 4000,
                "capabilities": {
                    "document_generation": True,
                    "technical_architecture": True,
                    "api_design": True,
                    "system_design": True,
                    "performance_analysis": True
                }
            }
        ]

        # Create agents
        created_count = 0
        for agent_config in default_agents:
            agent = AIAgent(
                name=agent_config["name"],
                description=agent_config["description"],
                doc_type=agent_config["doc_type"],
                agent_type=AgentType.SYSTEM,
                model_name=agent_config["model_name"],
                system_prompt=agent_config["system_prompt"],
                temperature=agent_config["temperature"],
                max_tokens=agent_config["max_tokens"],
                capabilities=agent_config["capabilities"],
                parameters={},
                status=AgentStatus.ACTIVE,
                is_system_default=True,
                version="1.0",
                usage_count=0,
                tenant_id=None,  # System agents are not tenant-specific
                created_by=None
            )

            db.add(agent)
            created_count += 1
            print(f"   ✅ Created {agent_config['name']} for {agent_config['doc_type']}")

        db.commit()
        print(f"🎉 Successfully created {created_count} default system agents")
        db.close()
        return True

    except Exception as e:
        print(f"❌ Error creating default AI agents: {str(e)}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False


def create_default_ai_prompts():
    """Create default AI prompt templates for common operations."""
    try:
        from app.core.database import SessionLocal
        from app.models.agent import AIPrompt, AIAgent

        db = SessionLocal()

        # Check if system prompts already exist
        existing_prompts = db.query(AIPrompt).filter(AIPrompt.is_system_default == True).count()
        if existing_prompts > 0:
            print(f"ℹ️  {existing_prompts} system prompts already exist, skipping creation")
            db.close()
            return True

        print("📝 Creating default AI prompt templates...")

        # Get system agents to associate prompts with
        agents = db.query(AIAgent).filter(AIAgent.is_system_default == True).all()
        agent_map = {agent.doc_type: agent.id for agent in agents}

        # Default prompt templates
        default_prompts = [
            # PRD Prompts
            {
                "name": "PRD Generation Prompt",
                "description": "Main prompt for generating Product Requirements Documents",
                "doc_type": "prd",
                "prompt_template": """Based on the provided information, create a comprehensive Product Requirements Document (PRD) with the following structure:

## Product Overview
- Product Name: {product_name}
- Product Vision: {product_vision}
- Target Audience: {target_audience}

## Business Objectives
{business_objectives}

## User Stories & Requirements
{user_requirements}

## Technical Requirements
{technical_requirements}

## Success Metrics
{success_metrics}

## Timeline & Milestones
{timeline}

Please ensure the PRD is detailed, actionable, and follows industry best practices.""",
                "variables": ["product_name", "product_vision", "target_audience", "business_objectives", "user_requirements", "technical_requirements", "success_metrics", "timeline"],
                "prompt_type": "generation",
                "category": "business"
            },
            {
                "name": "PRD Refinement Prompt",
                "description": "Prompt for refining and improving existing PRDs",
                "doc_type": "prd",
                "prompt_template": """Review and refine the following PRD content:

{existing_content}

Focus on:
1. Clarity and completeness of requirements
2. Alignment between business objectives and user stories
3. Technical feasibility assessment
4. Missing critical information
5. Consistency in terminology and structure

Provide specific suggestions for improvement and highlight any gaps or inconsistencies.""",
                "variables": ["existing_content"],
                "prompt_type": "refinement",
                "category": "business"
            },
            # BRD Prompts
            {
                "name": "BRD Generation Prompt",
                "description": "Main prompt for generating Business Requirements Documents",
                "doc_type": "brd",
                "prompt_template": """Create a comprehensive Business Requirements Document (BRD) based on the following information:

## Business Context
- Business Problem: {business_problem}
- Stakeholders: {stakeholders}
- Business Goals: {business_goals}

## Functional Requirements
{functional_requirements}

## Non-Functional Requirements
{non_functional_requirements}

## Business Rules
{business_rules}

## Process Flows
{process_flows}

## Success Criteria
{success_criteria}

Ensure the BRD clearly defines business needs and provides a foundation for technical implementation.""",
                "variables": ["business_problem", "stakeholders", "business_goals", "functional_requirements", "non_functional_requirements", "business_rules", "process_flows", "success_criteria"],
                "prompt_type": "generation",
                "category": "business"
            },
            # Technical Spec Prompts
            {
                "name": "Technical Spec Generation Prompt",
                "description": "Main prompt for generating Technical Specification documents",
                "doc_type": "technical_spec",
                "prompt_template": """Create a detailed Technical Specification document based on the requirements:

## System Architecture
- Architecture Pattern: {architecture_pattern}
- Technology Stack: {technology_stack}
- System Components: {system_components}

## API Specifications
{api_specifications}

## Data Models
{data_models}

## Security Requirements
{security_requirements}

## Performance Requirements
{performance_requirements}

## Integration Points
{integration_points}

## Deployment Architecture
{deployment_architecture}

Provide detailed technical specifications that enable development teams to implement the solution effectively.""",
                "variables": ["architecture_pattern", "technology_stack", "system_components", "api_specifications", "data_models", "security_requirements", "performance_requirements", "integration_points", "deployment_architecture"],
                "prompt_type": "generation",
                "category": "technical"
            }
        ]

        # Create prompts
        created_count = 0
        for prompt_config in default_prompts:
            agent_id = agent_map.get(prompt_config["doc_type"])

            prompt = AIPrompt(
                name=prompt_config["name"],
                description=prompt_config["description"],
                doc_type=prompt_config["doc_type"],
                prompt_template=prompt_config["prompt_template"],
                variables=prompt_config["variables"],
                prompt_type=prompt_config["prompt_type"],
                category=prompt_config["category"],
                agent_id=agent_id,
                is_active=True,
                is_system_default=True,
                version="1.0",
                usage_count=0,
                tenant_id=None,  # System prompts are not tenant-specific
                created_by=None
            )

            db.add(prompt)
            created_count += 1
            print(f"   ✅ Created {prompt_config['name']} for {prompt_config['doc_type']}")

        db.commit()
        print(f"🎉 Successfully created {created_count} default prompt templates")
        db.close()
        return True

    except Exception as e:
        print(f"❌ Error creating default AI prompts: {str(e)}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False


def main():
    """Main function."""
    print("🚀 PRD Generator Database Setup Script")
    print("=" * 50)

    # Test connection first
    if not test_connection():
        print("\n❌ Cannot proceed without database connection.")
        print("Please check your DATABASE_URL in .env file.")
        return

    # Create tables
    if create_tables():
        # Verify tables were created
        if verify_tables():
            # Check table structures
            check_table_structure()

            print("\n🎉 Database setup completed successfully!")
            print("✅ All tables created and verified")

            # Create default AI agents
            if create_default_ai_agents():
                print("✅ Default AI agents created")
            else:
                print("⚠️  Warning: Failed to create default AI agents")

            # Create default AI prompts
            if create_default_ai_prompts():
                print("✅ Default AI prompts created")
            else:
                print("⚠️  Warning: Failed to create default AI prompts")

            print("✅ Documents CRUD API is ready to use")
            print("\nNext steps:")
            print("1. Start the FastAPI server: uvicorn app.main:app --reload")
            print("2. Test the API endpoints at http://localhost:8000/docs")
            print("3. Create document types and start generating documents")
        else:
            print("\n⚠️  Database setup completed with warnings.")
            print("Some tables may be missing. Check the output above.")
    else:
        print("\n❌ Database setup failed!")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()
