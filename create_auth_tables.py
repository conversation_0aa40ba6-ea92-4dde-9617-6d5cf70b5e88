#!/usr/bin/env python3
"""
Create only the essential tables for authentication.
This script creates just the users and tenants tables to get auth working.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Boolean, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import sessionmaker
import uuid

# Load environment variables
load_dotenv()

def create_auth_tables():
    """Create only the essential auth tables."""
    try:
        # Get database URL
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("❌ DATABASE_URL not found in environment variables")
            return False
        
        # Create engine
        engine = create_engine(database_url)
        
        print("🔧 Creating authentication tables...")
        print("=" * 50)
        
        with engine.connect() as conn:
            # Start a transaction
            trans = conn.begin()
            
            try:
                # Enable UUID extension
                conn.execute(text("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\""))
                
                # Create tenants table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS tenants (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        name VARCHAR NOT NULL,
                        slug VARCHAR UNIQUE NOT NULL,
                        is_active BOOLEAN DEFAULT true,
                        settings JSONB DEFAULT '{}',
                        created_at TIMESTAMPTZ DEFAULT NOW(),
                        updated_at TIMESTAMPTZ DEFAULT NOW()
                    )
                """))
                print("✅ Tenants table created")
                
                # Create users table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS users (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        email VARCHAR UNIQUE NOT NULL,
                        full_name VARCHAR NOT NULL,
                        hashed_password VARCHAR NOT NULL,
                        is_active BOOLEAN DEFAULT true,
                        is_superuser BOOLEAN DEFAULT false,
                        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
                        role VARCHAR DEFAULT 'member' CHECK (role IN ('admin', 'member', 'viewer')),
                        last_login TIMESTAMPTZ,
                        preferences JSONB DEFAULT '{
                            "theme": "light",
                            "language": "en",
                            "notifications": {
                                "email_notifications": true,
                                "push_notifications": true,
                                "marketing_emails": false
                            },
                            "timezone": "UTC"
                        }',
                        created_at TIMESTAMPTZ DEFAULT NOW(),
                        updated_at TIMESTAMPTZ DEFAULT NOW()
                    )
                """))
                print("✅ Users table created")
                
                # Create indexes
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id)"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)"))
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug)"))
                print("✅ Indexes created")
                
                # Create updated_at trigger function
                conn.execute(text("""
                    CREATE OR REPLACE FUNCTION update_updated_at_column()
                    RETURNS TRIGGER AS $$
                    BEGIN
                        NEW.updated_at = NOW();
                        RETURN NEW;
                    END;
                    $$ language 'plpgsql'
                """))
                
                # Create triggers
                conn.execute(text("DROP TRIGGER IF EXISTS update_users_updated_at ON users"))
                conn.execute(text("""
                    CREATE TRIGGER update_users_updated_at
                        BEFORE UPDATE ON users
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column()
                """))
                
                conn.execute(text("DROP TRIGGER IF EXISTS update_tenants_updated_at ON tenants"))
                conn.execute(text("""
                    CREATE TRIGGER update_tenants_updated_at
                        BEFORE UPDATE ON tenants
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column()
                """))
                print("✅ Triggers created")
                
                # Insert default tenant
                conn.execute(text("""
                    INSERT INTO tenants (id, name, slug) 
                    VALUES ('00000000-0000-0000-0000-000000000000', 'Default Tenant', 'default')
                    ON CONFLICT (id) DO NOTHING
                """))
                print("✅ Default tenant created")
                
                # Commit transaction
                trans.commit()
                print("✅ All tables created successfully!")
                
            except Exception as e:
                trans.rollback()
                raise e
        
        # Verify tables
        print("\n📋 Verifying created tables...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                    AND table_name IN ('users', 'tenants')
                ORDER BY table_name
            """))
            
            tables = [row[0] for row in result]
            
            if 'tenants' in tables and 'users' in tables:
                print("✅ Both tables verified:")
                for table in tables:
                    print(f"   - {table}")
            else:
                print(f"❌ Missing tables. Found: {tables}")
                return False
        
        # Check users table structure
        print("\n🔍 Checking users table structure...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                    AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            columns = list(result)
            if columns:
                print("✅ Users table columns:")
                for col_name, data_type, nullable in columns:
                    print(f"   - {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
                
                # Check if 'role' column exists
                column_names = [col[0] for col in columns]
                if 'role' in column_names:
                    print("✅ 'role' column found - this should fix the error!")
                else:
                    print("❌ 'role' column not found!")
                    return False
            else:
                print("❌ Could not retrieve users table structure!")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
        return False

def test_connection():
    """Test database connection."""
    try:
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            print("❌ DATABASE_URL not found in environment variables")
            return False
        
        engine = create_engine(database_url)
        
        print("🔗 Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("✅ Database connection successful!")
                return True
            else:
                print("❌ Database connection test failed!")
                return False
                
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def main():
    """Main function."""
    print("🚀 Authentication Tables Setup")
    print("=" * 50)
    
    # Test connection first
    if not test_connection():
        print("\n❌ Cannot proceed without database connection.")
        print("Please check your DATABASE_URL in .env file.")
        return
    
    # Create tables
    if create_auth_tables():
        print("\n🎉 Authentication tables setup completed successfully!")
        print("You can now test your authentication endpoints.")
        print("\nTry running the registration test again:")
        print('curl -X POST "http://localhost:8000/api/v1/auth/register" \\')
        print('  -H "Content-Type: application/json" \\')
        print('  -d \'{"email": "<EMAIL>", "password": "SecurePass123!", "full_name": "Test User", "tenant_id": "00000000-0000-0000-0000-000000000000"}\'')
    else:
        print("\n❌ Authentication tables setup failed!")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()
