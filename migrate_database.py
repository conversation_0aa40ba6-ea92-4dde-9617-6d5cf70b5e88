#!/usr/bin/env python3
"""
Database migration runner for the PRD Generator system.
This script provides a simple interface to run database migrations.
"""

import os
import sys
import argparse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Database Migration Tool')
    parser.add_argument('--check', action='store_true', help='Check database status without making changes')
    parser.add_argument('--migrate', action='store_true', help='Run database migration')
    parser.add_argument('--force', action='store_true', help='Force migration even if tables exist')
    
    args = parser.parse_args()
    
    try:
        from app.core.migrations import DatabaseMigrator, check_database_status
        
        print("🚀 PRD Generator Database Migration Tool")
        print("=" * 50)
        
        if args.check:
            print("📊 Checking database status...")
            status = check_database_status()
            
            print(f"🔗 Database Connection: {'✅ Connected' if status['connection'] else '❌ Failed'}")
            print(f"📊 Total Tables: {status['total_tables']}")
            print(f"✅ Required Tables Ready: {'Yes' if status['required_tables_ready'] else 'No'}")
            
            print("\n📋 Required Tables Status:")
            for table, exists in status['tables_exist'].items():
                status_icon = "✅" if exists else "❌"
                print(f"   {status_icon} {table}")
            
            if not status['required_tables_ready']:
                print("\n⚠️  Some required tables are missing. Run with --migrate to create them.")
            else:
                print("\n🎉 Database is ready for the Documents CRUD API!")
        
        elif args.migrate:
            print("🔧 Running database migration...")
            migrator = DatabaseMigrator()
            
            if migrator.run_migration():
                print("\n🎉 Migration completed successfully!")
                print("✅ Documents CRUD API is ready to use")
            else:
                print("\n❌ Migration failed!")
                sys.exit(1)
        
        else:
            print("Please specify --check or --migrate")
            parser.print_help()
    
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("Make sure you're in the correct directory and all dependencies are installed.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
