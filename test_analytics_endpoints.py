"""
Test script for Analytics & Insights API endpoints
"""
import requests
import json
from datetime import datetime, date, timedelta

# Configuration
BASE_URL = "http://localhost:8001/api/v1"

# Test date range (last 30 days)
end_date = date.today()
start_date = end_date - timedelta(days=30)


def get_auth_headers():
    """Get authentication headers (placeholder - implement based on your auth system)"""
    return {
        "Content-Type": "application/json",
        # "Authorization": "Bearer your_token_here"
    }


def test_dashboard_analytics():
    """Test dashboard analytics endpoints"""
    headers = get_auth_headers()
    
    print("=== Testing Dashboard Analytics ===")
    
    # Test 1: Get full dashboard data
    print("\n1. Testing GET /analytics/dashboard")
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat()
    }
    response = requests.get(f"{BASE_URL}/analytics/dashboard", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Overview metrics: {list(data.get('overview', {}).keys())}")
        print(f"Charts available: {list(data.get('charts', {}).keys())}")
        print(f"Insights count: {len(data.get('insights', []))}")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Get overview only
    print("\n2. Testing GET /analytics/dashboard/overview")
    response = requests.get(f"{BASE_URL}/analytics/dashboard/overview", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total documents: {data.get('total_documents', {}).get('value', 'N/A')}")
        print(f"Active users: {data.get('active_users', {}).get('value', 'N/A')}")
    
    # Test 3: Get specific chart
    print("\n3. Testing GET /analytics/dashboard/charts?chart_type=document_creation")
    params["chart_type"] = "document_creation"
    response = requests.get(f"{BASE_URL}/analytics/dashboard/charts", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Chart data keys: {list(data.keys())}")


def test_document_analytics():
    """Test document analytics endpoints"""
    headers = get_auth_headers()
    
    print("\n=== Testing Document Analytics ===")
    
    # Test 1: Get document type analytics
    print("\n1. Testing GET /analytics/document-types/{type_id}")
    # You'll need to replace with actual document type ID
    doc_type_id = "550e8400-e29b-41d4-a716-************"  # Example UUID
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat()
    }
    response = requests.get(f"{BASE_URL}/analytics/document-types/{doc_type_id}", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Document type: {data.get('metrics', {}).get('document_type_name', 'N/A')}")
        print(f"Total documents: {data.get('metrics', {}).get('total_documents', 'N/A')}")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Get specific document analytics
    print("\n2. Testing GET /analytics/documents/{doc_id}")
    # You'll need to replace with actual document ID
    doc_id = "550e8400-e29b-41d4-a716-************"  # Example UUID
    response = requests.get(f"{BASE_URL}/analytics/documents/{doc_id}", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Document title: {data.get('document', {}).get('document_title', 'N/A')}")
        print(f"View count: {data.get('document', {}).get('view_count', 'N/A')}")
    else:
        print(f"Error: {response.text}")


def test_team_ai_analytics():
    """Test team and AI analytics endpoints"""
    headers = get_auth_headers()
    
    print("\n=== Testing Team & AI Analytics ===")
    
    # Test 1: Get team performance
    print("\n1. Testing GET /analytics/team-performance")
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat()
    }
    response = requests.get(f"{BASE_URL}/analytics/team-performance", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total members: {data.get('total_members', 'N/A')}")
        print(f"Active members: {data.get('active_members', 'N/A')}")
        print(f"Completion rate: {data.get('completion_rate', 'N/A')}%")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Get AI effectiveness
    print("\n2. Testing GET /analytics/ai-effectiveness/prd")
    response = requests.get(f"{BASE_URL}/analytics/ai-effectiveness/prd", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total generations: {data.get('total_generations', 'N/A')}")
        print(f"Success rate: {data.get('success_rate', 'N/A')}%")
        print(f"User rating: {data.get('avg_user_rating', 'N/A')}")
    else:
        print(f"Error: {response.text}")


def test_specialized_analytics():
    """Test specialized analytics endpoints"""
    headers = get_auth_headers()
    
    print("\n=== Testing Specialized Analytics ===")
    
    # Test 1: Get export usage analytics
    print("\n1. Testing GET /analytics/export-usage")
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat()
    }
    response = requests.get(f"{BASE_URL}/analytics/export-usage", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total exports: {data.get('total_exports', 'N/A')}")
        print(f"Success rate: {data.get('success_rate', 'N/A')}%")
        print(f"Most popular format: {data.get('most_popular_format', 'N/A')}")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Get refinement patterns
    print("\n2. Testing GET /analytics/refinement-patterns/prd")
    response = requests.get(f"{BASE_URL}/analytics/refinement-patterns/prd", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total refinements: {data.get('total_refinements', 'N/A')}")
        print(f"Avg per document: {data.get('avg_refinements_per_doc', 'N/A')}")
        print(f"Success rate: {data.get('refinement_success_rate', 'N/A')}%")
    else:
        print(f"Error: {response.text}")
    
    # Test 3: Get form completion analytics
    print("\n3. Testing GET /analytics/form-completion/prd")
    response = requests.get(f"{BASE_URL}/analytics/form-completion/prd", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total starts: {data.get('total_starts', 'N/A')}")
        print(f"Total completions: {data.get('total_completions', 'N/A')}")
        print(f"Completion rate: {data.get('completion_rate', 'N/A')}%")
    else:
        print(f"Error: {response.text}")


def test_error_handling():
    """Test error handling and edge cases"""
    headers = get_auth_headers()
    
    print("\n=== Testing Error Handling ===")
    
    # Test 1: Invalid date range
    print("\n1. Testing invalid date range")
    params = {
        "start_date": end_date.isoformat(),  # Start after end
        "end_date": start_date.isoformat()
    }
    response = requests.get(f"{BASE_URL}/analytics/dashboard", 
                           headers=headers, params=params)
    print(f"Status: {response.status_code}")
    print(f"Expected error for invalid date range")
    
    # Test 2: Non-existent document type
    print("\n2. Testing non-existent document type")
    response = requests.get(f"{BASE_URL}/analytics/ai-effectiveness/nonexistent", 
                           headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Expected 404 for non-existent document type")
    
    # Test 3: Invalid UUID
    print("\n3. Testing invalid document ID")
    response = requests.get(f"{BASE_URL}/analytics/documents/invalid-uuid", 
                           headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Expected error for invalid UUID")


def test_performance():
    """Test API performance with different parameters"""
    headers = get_auth_headers()
    
    print("\n=== Testing Performance ===")
    
    # Test 1: Large date range
    print("\n1. Testing large date range (1 year)")
    large_start = end_date - timedelta(days=365)
    params = {
        "start_date": large_start.isoformat(),
        "end_date": end_date.isoformat()
    }
    
    start_time = datetime.now()
    response = requests.get(f"{BASE_URL}/analytics/dashboard/overview", 
                           headers=headers, params=params)
    end_time = datetime.now()
    
    print(f"Status: {response.status_code}")
    print(f"Response time: {(end_time - start_time).total_seconds():.2f} seconds")
    
    # Test 2: Multiple concurrent requests (simplified)
    print("\n2. Testing dashboard charts endpoint")
    start_time = datetime.now()
    response = requests.get(f"{BASE_URL}/analytics/dashboard/charts", 
                           headers=headers, params=params)
    end_time = datetime.now()
    
    print(f"Status: {response.status_code}")
    print(f"Response time: {(end_time - start_time).total_seconds():.2f} seconds")


def main():
    """Run all analytics API tests"""
    print("Starting Analytics & Insights API Tests")
    print("=" * 50)
    
    try:
        # Test all endpoint categories
        test_dashboard_analytics()
        test_document_analytics()
        test_team_ai_analytics()
        test_specialized_analytics()
        test_error_handling()
        test_performance()
        
        print("\n" + "=" * 50)
        print("Analytics API tests completed!")
        print("\nNote: Some tests may fail if:")
        print("- Authentication is not properly configured")
        print("- Database doesn't have sample data")
        print("- Document/type IDs don't exist")
        print("- Server is not running on localhost:8000")
        
    except Exception as e:
        print(f"\nError during testing: {e}")


if __name__ == "__main__":
    main()
