#!/usr/bin/env python3
"""
Test Supabase connection and help identify missing configuration.
This script will help you verify your Supabase setup.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_env_vars():
    """Check if all required environment variables are set."""
    required_vars = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'SUPABASE_SERVICE_KEY',
        'SUPABASE_JWT_SECRET',
        'SECRET_KEY',
        'DATABASE_URL'
    ]
    
    missing_vars = []
    placeholder_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        elif 'your-' in value.lower() or 'placeholder' in value.lower():
            placeholder_vars.append(var)
    
    print("🔍 Environment Variables Check:")
    print("=" * 50)
    
    if not missing_vars and not placeholder_vars:
        print("✅ All required environment variables are set!")
        return True
    
    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
    
    if placeholder_vars:
        print("⚠️  Environment variables with placeholder values:")
        for var in placeholder_vars:
            print(f"   - {var}: {os.getenv(var)}")
    
    return False

def get_supabase_keys_instructions():
    """Provide instructions for getting Supabase keys."""
    print("\n📋 How to get your Supabase keys:")
    print("=" * 50)
    print("1. Go to your Supabase project dashboard:")
    print("   https://supabase.com/dashboard/projects")
    print()
    print("2. Select your project: wbvcbsufhvlqhbgzzvtg")
    print()
    print("3. Navigate to Settings → API")
    print()
    print("4. Copy the following keys:")
    print("   📋 SUPABASE_SERVICE_KEY: Copy the 'service_role' key")
    print("   📋 SUPABASE_JWT_SECRET: Copy the 'JWT Secret' from JWT Settings")
    print()
    print("5. Update your .env file with these values")

def test_basic_connection():
    """Test basic connection to Supabase."""
    try:
        from supabase import create_client
        
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_ANON_KEY')
        
        if not url or not key:
            print("❌ Cannot test connection: Missing SUPABASE_URL or SUPABASE_ANON_KEY")
            return False
        
        print("\n🔗 Testing Supabase connection...")
        print("=" * 50)
        
        # Create client
        supabase = create_client(url, key)
        
        # Test connection with a simple query
        response = supabase.table('users').select('id').limit(1).execute()
        
        print("✅ Supabase connection successful!")
        print(f"   URL: {url}")
        print(f"   Response: {len(response.data) if response.data else 0} records")
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {str(e)}")
        print("   This might be normal if you haven't set up the users table yet.")
        return False

def main():
    """Main function to run all checks."""
    print("🚀 Supabase Configuration Checker")
    print("=" * 50)
    
    # Check environment variables
    env_ok = check_env_vars()
    
    if not env_ok:
        get_supabase_keys_instructions()
        print("\n⚠️  Please update your .env file with the correct values before proceeding.")
        return
    
    # Test connection
    test_basic_connection()
    
    print("\n✅ Configuration check complete!")
    print("   If all checks passed, you can now start your FastAPI server.")

if __name__ == "__main__":
    main()
