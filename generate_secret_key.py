#!/usr/bin/env python3
"""
Generate a secure secret key for the application.
Run this script to generate a new SECRET_KEY for your .env file.
"""

import secrets
import string

def generate_secret_key(length=64):
    """Generate a secure random secret key."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

if __name__ == "__main__":
    secret_key = generate_secret_key()
    print("Generated SECRET_KEY:")
    print(secret_key)
    print("\nAdd this to your .env file:")
    print(f"SECRET_KEY={secret_key}")
