#!/usr/bin/env python3
"""
Seed script to populate default document types in the database.
This script creates system default document types with proper form schemas and templates.
"""

import sys
import os
import uuid
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
# Import models in correct order to avoid circular dependencies
from app.models.tenant import Tenant
from app.models.user import User
from app.models.document_type import DocumentType

def create_prd_document_type():
    """Create Product Requirements Document type."""
    return DocumentType(
        id=uuid.UUID('11111111-1111-1111-1111-111111111111'),
        name="Product Requirements Document (PRD)",
        slug="prd",
        description="Comprehensive product requirements document for software development projects",
        category="product",
        industry="technology",
        form_schema={
            "sections": [
                {
                    "id": "overview",
                    "title": "Product Overview",
                    "questions": [
                        {
                            "id": "product_name",
                            "type": "text",
                            "label": "Product Name",
                            "required": True,
                            "placeholder": "Enter the product name"
                        },
                        {
                            "id": "product_vision",
                            "type": "textarea",
                            "label": "Product Vision",
                            "required": True,
                            "placeholder": "Describe the overall vision for this product"
                        },
                        {
                            "id": "target_audience",
                            "type": "textarea",
                            "label": "Target Audience",
                            "required": True,
                            "placeholder": "Who is the primary target audience?"
                        }
                    ]
                },
                {
                    "id": "objectives",
                    "title": "Objectives & Goals",
                    "questions": [
                        {
                            "id": "business_objectives",
                            "type": "textarea",
                            "label": "Business Objectives",
                            "required": True,
                            "placeholder": "What are the key business objectives?"
                        },
                        {
                            "id": "success_metrics",
                            "type": "textarea",
                            "label": "Success Metrics",
                            "required": True,
                            "placeholder": "How will success be measured?"
                        }
                    ]
                },
                {
                    "id": "features",
                    "title": "Features & Requirements",
                    "questions": [
                        {
                            "id": "core_features",
                            "type": "textarea",
                            "label": "Core Features",
                            "required": True,
                            "placeholder": "List the core features and functionality"
                        },
                        {
                            "id": "user_stories",
                            "type": "textarea",
                            "label": "User Stories",
                            "required": False,
                            "placeholder": "Key user stories (optional)"
                        }
                    ]
                }
            ]
        },
        template_structure={
            "sections": [
                {
                    "id": "executive_summary",
                    "title": "Executive Summary",
                    "content_type": "generated",
                    "order": 1
                },
                {
                    "id": "product_overview",
                    "title": "Product Overview",
                    "content_type": "form_based",
                    "order": 2,
                    "subsections": ["product_name", "product_vision", "target_audience"]
                },
                {
                    "id": "objectives_goals",
                    "title": "Objectives & Goals",
                    "content_type": "form_based",
                    "order": 3,
                    "subsections": ["business_objectives", "success_metrics"]
                },
                {
                    "id": "features_requirements",
                    "title": "Features & Requirements",
                    "content_type": "form_based",
                    "order": 4,
                    "subsections": ["core_features", "user_stories"]
                },
                {
                    "id": "technical_considerations",
                    "title": "Technical Considerations",
                    "content_type": "generated",
                    "order": 5
                },
                {
                    "id": "timeline_milestones",
                    "title": "Timeline & Milestones",
                    "content_type": "generated",
                    "order": 6
                }
            ]
        },
        ai_agents={
            "primary_agent": "claude-3-5-sonnet-20241022",
            "refinement_agents": ["claude-3-5-sonnet-20241022"],
            "specialized_agents": {
                "technical_review": "claude-3-5-sonnet-20241022",
                "business_analysis": "claude-3-5-sonnet-20241022"
            }
        },
        refinement_options={
            "tone_options": ["professional", "technical", "executive"],
            "length_options": ["brief", "standard", "comprehensive"],
            "focus_areas": ["technical_depth", "business_impact", "user_experience", "implementation"],
            "custom_refinements": [
                "Add more technical details",
                "Focus on business value",
                "Include competitive analysis",
                "Add risk assessment"
            ]
        },
        is_system_default=True,
        is_active=True,
        version="1.0",
        usage_count=0,
        tenant_id=None,  # System default
        created_by=None  # System created
    )

def create_brd_document_type():
    """Create Business Requirements Document type."""
    return DocumentType(
        id=uuid.UUID('*************-2222-2222-************'),
        name="Business Requirements Document (BRD)",
        slug="brd",
        description="Business requirements document outlining business needs and objectives",
        category="business",
        industry=None,  # Generic
        form_schema={
            "sections": [
                {
                    "id": "business_context",
                    "title": "Business Context",
                    "questions": [
                        {
                            "id": "business_problem",
                            "type": "textarea",
                            "label": "Business Problem",
                            "required": True,
                            "placeholder": "What business problem are we solving?"
                        },
                        {
                            "id": "current_state",
                            "type": "textarea",
                            "label": "Current State",
                            "required": True,
                            "placeholder": "Describe the current business process or situation"
                        },
                        {
                            "id": "desired_state",
                            "type": "textarea",
                            "label": "Desired Future State",
                            "required": True,
                            "placeholder": "What is the desired outcome or future state?"
                        }
                    ]
                },
                {
                    "id": "stakeholders",
                    "title": "Stakeholders",
                    "questions": [
                        {
                            "id": "key_stakeholders",
                            "type": "textarea",
                            "label": "Key Stakeholders",
                            "required": True,
                            "placeholder": "Who are the key stakeholders and their roles?"
                        },
                        {
                            "id": "business_sponsor",
                            "type": "text",
                            "label": "Business Sponsor",
                            "required": False,
                            "placeholder": "Primary business sponsor"
                        }
                    ]
                }
            ]
        },
        template_structure={
            "sections": [
                {
                    "id": "executive_summary",
                    "title": "Executive Summary",
                    "content_type": "generated",
                    "order": 1
                },
                {
                    "id": "business_context",
                    "title": "Business Context",
                    "content_type": "form_based",
                    "order": 2
                },
                {
                    "id": "stakeholder_analysis",
                    "title": "Stakeholder Analysis",
                    "content_type": "form_based",
                    "order": 3
                },
                {
                    "id": "requirements",
                    "title": "Business Requirements",
                    "content_type": "generated",
                    "order": 4
                }
            ]
        },
        ai_agents={
            "primary_agent": "claude-3-5-sonnet-20241022",
            "refinement_agents": ["claude-3-5-sonnet-20241022"],
            "specialized_agents": {
                "business_analysis": "claude-3-5-sonnet-20241022"
            }
        },
        refinement_options={
            "tone_options": ["professional", "executive", "analytical"],
            "length_options": ["brief", "standard", "detailed"],
            "focus_areas": ["business_impact", "stakeholder_needs", "process_improvement"],
            "custom_refinements": [
                "Add cost-benefit analysis",
                "Include risk assessment",
                "Focus on ROI",
                "Add implementation timeline"
            ]
        },
        is_system_default=True,
        is_active=True,
        version="1.0",
        usage_count=0,
        tenant_id=None,
        created_by=None
    )

def create_technical_spec_document_type():
    """Create Technical Specification Document type."""
    return DocumentType(
        id=uuid.UUID('*************-3333-3333-************'),
        name="Technical Specification",
        slug="technical-spec",
        description="Detailed technical specification for software development projects",
        category="technical",
        industry="technology",
        form_schema={
            "sections": [
                {
                    "id": "system_overview",
                    "title": "System Overview",
                    "questions": [
                        {
                            "id": "system_name",
                            "type": "text",
                            "label": "System/Component Name",
                            "required": True,
                            "placeholder": "Name of the system or component"
                        },
                        {
                            "id": "system_purpose",
                            "type": "textarea",
                            "label": "System Purpose",
                            "required": True,
                            "placeholder": "What is the purpose of this system?"
                        },
                        {
                            "id": "technology_stack",
                            "type": "textarea",
                            "label": "Technology Stack",
                            "required": True,
                            "placeholder": "What technologies will be used?"
                        }
                    ]
                }
            ]
        },
        template_structure={
            "sections": [
                {
                    "id": "overview",
                    "title": "Technical Overview",
                    "content_type": "form_based",
                    "order": 1
                },
                {
                    "id": "architecture",
                    "title": "System Architecture",
                    "content_type": "generated",
                    "order": 2
                },
                {
                    "id": "implementation",
                    "title": "Implementation Details",
                    "content_type": "generated",
                    "order": 3
                }
            ]
        },
        ai_agents={
            "primary_agent": "claude-3-5-sonnet-20241022",
            "refinement_agents": ["claude-3-5-sonnet-20241022"],
            "specialized_agents": {
                "architecture_review": "claude-3-5-sonnet-20241022"
            }
        },
        refinement_options={
            "tone_options": ["technical", "detailed", "concise"],
            "length_options": ["brief", "standard", "comprehensive"],
            "focus_areas": ["architecture", "implementation", "performance", "security"],
            "custom_refinements": [
                "Add more architectural diagrams",
                "Include performance considerations",
                "Add security requirements",
                "Include testing strategy"
            ]
        },
        is_system_default=True,
        is_active=True,
        version="1.0",
        usage_count=0,
        tenant_id=None,
        created_by=None
    )

def seed_document_types():
    """Seed the database with default document types."""
    db = SessionLocal()
    try:
        # Check if document types already exist
        existing_count = db.query(DocumentType).filter(
            DocumentType.is_system_default == True
        ).count()
        
        if existing_count > 0:
            print(f"Found {existing_count} existing system document types. Skipping seed.")
            return
        
        # Create document types
        document_types = [
            create_prd_document_type(),
            create_brd_document_type(),
            create_technical_spec_document_type()
        ]
        
        for doc_type in document_types:
            db.add(doc_type)
            print(f"Added document type: {doc_type.name}")
        
        db.commit()
        print(f"Successfully seeded {len(document_types)} document types.")
        
    except Exception as e:
        db.rollback()
        print(f"Error seeding document types: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    seed_document_types()
