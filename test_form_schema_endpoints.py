"""
Test script for form schema and question management API endpoints.
"""
import requests
import json
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api/v1/form-schemas"
TEST_DOC_TYPE = "prd"

def test_form_schema_endpoints():
    """Test form schema management endpoints"""
    
    print("🧪 Testing Form Schema API Endpoints")
    print("=" * 50)
    
    headers = {
        "Content-Type": "application/json",
        # Add authentication headers when testing with real API
        # "Authorization": "Bearer your-token-here"
    }
    
    # Test 1: Create form schema
    print("\n1. Testing create form schema...")
    schema_data = {
        "document_type": TEST_DOC_TYPE,
        "schema_version": "1.0",
        "title": "PRD Form Schema",
        "description": "Form schema for Product Requirements Documents",
        "sections": ["Overview", "Requirements", "Technical Specs", "Timeline"],
        "global_settings": {
            "allow_save_draft": True,
            "require_all_sections": False,
            "auto_save_interval": 30
        },
        "styling": {
            "theme": "modern",
            "primary_color": "#007bff",
            "layout": "single_column"
        },
        "metadata": {
            "category": "product",
            "difficulty": "intermediate",
            "estimated_time": 15
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/{TEST_DOC_TYPE}",
            headers=headers,
            json=schema_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            schema = response.json()
            print(f"   Created schema ID: {schema['id']}")
            print(f"   Schema title: {schema['title']}")
            print(f"   Sections: {schema['sections']}")
            schema_id = schema['id']
        else:
            print(f"   Error: {response.text}")
            schema_id = None
    except Exception as e:
        print(f"   Connection error: {e}")
        schema_id = None
    
    # Test 2: Get form schema
    print("\n2. Testing get form schema...")
    try:
        response = requests.get(f"{BASE_URL}/{TEST_DOC_TYPE}", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            schema = response.json()
            print(f"   Schema: {schema['title']}")
            print(f"   Question count: {schema['question_count']}")
            print(f"   Version: {schema['schema_version']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 3: Update form schema
    print("\n3. Testing update form schema...")
    update_data = {
        "description": "Updated form schema for Product Requirements Documents",
        "sections": ["Overview", "Requirements", "Technical Specs", "Timeline", "Success Metrics"]
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/{TEST_DOC_TYPE}",
            headers=headers,
            json=update_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            schema = response.json()
            print(f"   Updated description: {schema['description']}")
            print(f"   Updated sections: {schema['sections']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")


def test_question_endpoints():
    """Test question management endpoints"""
    
    print("\n🔧 Testing Question Management API Endpoints")
    print("=" * 50)
    
    headers = {"Content-Type": "application/json"}
    
    # Test 1: Add question
    print("\n1. Testing add question...")
    question_data = {
        "question_id": "product_name",
        "question_text": "What is the name of the product?",
        "question_type": "text",
        "description": "Enter the official product name",
        "placeholder": "e.g., Mobile Banking App",
        "options": [],
        "validation_rules": [
            {
                "rule_type": "required",
                "message": "Product name is required"
            },
            {
                "rule_type": "min_length",
                "value": 3,
                "message": "Product name must be at least 3 characters"
            },
            {
                "rule_type": "max_length",
                "value": 100,
                "message": "Product name must be less than 100 characters"
            }
        ],
        "conditional_logic": [],
        "is_required": True,
        "order_index": 1,
        "section": "Overview",
        "metadata": {
            "help_text": "This will be used throughout the document",
            "importance": "high"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/{TEST_DOC_TYPE}/questions",
            headers=headers,
            json=question_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            question = response.json()
            print(f"   Created question ID: {question['id']}")
            print(f"   Question: {question['question_text']}")
            print(f"   Type: {question['question_type']}")
            question_id = question['id']
        else:
            print(f"   Error: {response.text}")
            question_id = str(uuid.uuid4())
    except Exception as e:
        print(f"   Connection error: {e}")
        question_id = str(uuid.uuid4())
    
    # Test 2: Add select question with options
    print("\n2. Testing add select question...")
    select_question_data = {
        "question_id": "priority_level",
        "question_text": "What is the priority level of this product?",
        "question_type": "select",
        "description": "Select the business priority for this product",
        "options": [
            {"value": "high", "label": "High Priority", "description": "Critical for business success"},
            {"value": "medium", "label": "Medium Priority", "description": "Important but not critical"},
            {"value": "low", "label": "Low Priority", "description": "Nice to have", "is_default": True}
        ],
        "validation_rules": [
            {
                "rule_type": "required",
                "message": "Priority level is required"
            }
        ],
        "conditional_logic": [
            {
                "condition_question_id": "priority_level",
                "operator": "equals",
                "value": "high",
                "action": "show",
                "target_question_ids": ["urgency_reason"]
            }
        ],
        "is_required": True,
        "order_index": 2,
        "section": "Overview",
        "metadata": {"category": "business"}
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/{TEST_DOC_TYPE}/questions",
            headers=headers,
            json=select_question_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            question = response.json()
            print(f"   Created question: {question['question_text']}")
            print(f"   Options count: {len(question['options'])}")
            print(f"   Conditional rules: {len(question['conditional_logic'])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 3: Get questions
    print("\n3. Testing get questions...")
    try:
        response = requests.get(f"{BASE_URL}/{TEST_DOC_TYPE}/questions", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            questions = response.json()
            print(f"   Total questions: {questions['total_count']}")
            print(f"   Document type: {questions['document_type']}")
            print(f"   Sections: {questions['sections']}")
            if questions['questions']:
                print(f"   First question: {questions['questions'][0]['question_text']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 4: Update question
    print("\n4. Testing update question...")
    update_data = {
        "description": "Enter the official product name (this will appear in the document title)",
        "placeholder": "e.g., Mobile Banking App v2.0"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/{TEST_DOC_TYPE}/questions/{question_id}",
            headers=headers,
            json=update_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            question = response.json()
            print(f"   Updated description: {question['description']}")
            print(f"   Updated placeholder: {question['placeholder']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")


def test_validation_and_preview():
    """Test validation and preview endpoints"""
    
    print("\n📋 Testing Validation & Preview Endpoints")
    print("=" * 50)
    
    headers = {"Content-Type": "application/json"}
    
    # Test 1: Validate form data
    print("\n1. Testing form validation...")
    form_data = {
        "form_data": {
            "product_name": "Mobile Banking App",
            "priority_level": "high",
            "target_audience": "Banking customers aged 25-45"
        },
        "validate_conditionals": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/{TEST_DOC_TYPE}/validate",
            headers=headers,
            json=form_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            validation = response.json()
            print(f"   Is valid: {validation['is_valid']}")
            print(f"   Errors: {len(validation['errors'])}")
            print(f"   Warnings: {len(validation['warnings'])}")
            print(f"   Conditional questions: {len(validation['conditional_questions'])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 2: Test validation with invalid data
    print("\n2. Testing validation with invalid data...")
    invalid_form_data = {
        "form_data": {
            "product_name": "AB",  # Too short
            "priority_level": "",   # Required but empty
            "email": "invalid-email"  # Invalid email format
        },
        "validate_conditionals": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/{TEST_DOC_TYPE}/validate",
            headers=headers,
            json=invalid_form_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            validation = response.json()
            print(f"   Is valid: {validation['is_valid']}")
            print(f"   Errors: {len(validation['errors'])}")
            if validation['errors']:
                print(f"   First error: {validation['errors'][0]['message']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 3: Preview form
    print("\n3. Testing form preview...")
    try:
        response = requests.get(f"{BASE_URL}/{TEST_DOC_TYPE}/preview", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            preview = response.json()
            print(f"   Form title: {preview['title']}")
            print(f"   Total questions: {preview['total_questions']}")
            print(f"   Required questions: {preview['required_questions']}")
            print(f"   Estimated time: {preview['estimated_time_minutes']} minutes")
            print(f"   Sections: {len(preview['sections'])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 4: Get conditional logic
    print("\n4. Testing get conditional logic...")
    try:
        response = requests.get(f"{BASE_URL}/{TEST_DOC_TYPE}/conditional", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            logic = response.json()
            print(f"   Document type: {logic['document_type']}")
            print(f"   Total rules: {logic['total_rules']}")
            if logic['rules']:
                rule = logic['rules'][0]
                print(f"   First rule: {rule['condition_question_id']} {rule['operator']} {rule['value']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")


if __name__ == "__main__":
    print("🚀 Starting Form Schema API Tests")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_form_schema_endpoints()
    test_question_endpoints()
    test_validation_and_preview()
    
    print("\n📋 Summary:")
    print("- Form schema management endpoints created")
    print("- Question CRUD operations implemented")
    print("- Conditional logic support added")
    print("- Form validation with custom rules")
    print("- Form preview functionality")
    print("- Modular structure with separate files")
    print("- Admin permission controls")
    print("- Usage tracking and analytics")
    
    print("\n🎯 Next steps:")
    print("1. Run the actual API server")
    print("2. Test with real authentication")
    print("3. Create sample form schemas")
    print("4. Implement advanced conditional logic")
    print("5. Add database migrations for form tables")
    print("6. Add form builder UI integration")
