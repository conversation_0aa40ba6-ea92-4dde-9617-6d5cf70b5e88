"""
Tests for AI Agent Execution API endpoints.
"""

import pytest
import uuid
from unittest.mock import patch, AsyncMock

from app.models.agent import JobStatus


class TestExecutionAPI:
    """Test cases for AI Agent Execution API endpoints."""
    
    def test_execute_agent_success(self, authenticated_client, test_agent):
        """Test successful agent execution."""
        request_data = {
            "input_data": {"prompt": "Test prompt"},
            "job_type": "test"
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            # Mock the service to return a job
            from app.models.agent import AgentJob
            mock_job = AgentJob(
                id=uuid.uuid4(),
                agent_id=test_agent.id,
                job_type="test",
                input_data=request_data["input_data"],
                status=JobStatus.PENDING,
                user_id=uuid.uuid4(),
                tenant_id=uuid.uuid4()
            )
            mock_service.execute_agent.return_value = mock_job
            
            response = authenticated_client.post(
                f"/api/v1/ai-prompts/execution/{test_agent.doc_type}/{test_agent.id}/execute",
                json=request_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["agent_id"] == str(test_agent.id)
            assert data["job_type"] == "test"
            assert data["status"] == JobStatus.PENDING
    
    def test_execute_agent_not_found(self, authenticated_client):
        """Test agent execution with non-existent agent."""
        request_data = {
            "input_data": {"prompt": "Test prompt"},
            "job_type": "test"
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.execute_agent.side_effect = ValueError("Agent not found or not accessible")
            
            response = authenticated_client.post(
                f"/api/v1/ai-prompts/execution/test/{uuid.uuid4()}/execute",
                json=request_data
            )
            
            assert response.status_code == 404
            assert "Agent not found or not accessible" in response.json()["detail"]
    
    def test_execute_agent_server_error(self, authenticated_client, test_agent):
        """Test agent execution with server error."""
        request_data = {
            "input_data": {"prompt": "Test prompt"},
            "job_type": "test"
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.execute_agent.side_effect = Exception("Internal server error")
            
            response = authenticated_client.post(
                f"/api/v1/ai-prompts/execution/{test_agent.doc_type}/{test_agent.id}/execute",
                json=request_data
            )
            
            assert response.status_code == 500
            assert "Failed to execute agent" in response.json()["detail"]
    
    def test_get_job_status_success(self, authenticated_client, test_job):
        """Test getting job status successfully."""
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.get_job_status.return_value = test_job
            
            response = authenticated_client.get(f"/api/v1/ai-prompts/execution/jobs/{test_job.id}")
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == str(test_job.id)
            assert data["status"] == test_job.status
    
    def test_get_job_status_not_found(self, authenticated_client):
        """Test getting job status for non-existent job."""
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.get_job_status.return_value = None
            
            response = authenticated_client.get(f"/api/v1/ai-prompts/execution/jobs/{uuid.uuid4()}")
            
            assert response.status_code == 404
            assert "Job not found" in response.json()["detail"]
    
    def test_cancel_job_success(self, authenticated_client, test_job):
        """Test successful job cancellation."""
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.cancel_job.return_value = True
            
            response = authenticated_client.post(f"/api/v1/ai-prompts/execution/jobs/{test_job.id}/cancel")
            
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Job cancelled successfully"
    
    def test_cancel_job_failure(self, authenticated_client, test_job):
        """Test job cancellation failure."""
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.cancel_job.return_value = False
            
            response = authenticated_client.post(f"/api/v1/ai-prompts/execution/jobs/{test_job.id}/cancel")
            
            assert response.status_code == 400
            assert "Job not found or cannot be cancelled" in response.json()["detail"]
    
    def test_batch_execute_success(self, authenticated_client, test_agent):
        """Test successful batch agent execution."""
        request_data = {
            "execution_requests": [
                {
                    "agent_id": str(test_agent.id),
                    "doc_type": test_agent.doc_type,
                    "request": {
                        "input_data": {"prompt": "Test prompt 1"},
                        "job_type": "test"
                    }
                },
                {
                    "agent_id": str(test_agent.id),
                    "doc_type": test_agent.doc_type,
                    "request": {
                        "input_data": {"prompt": "Test prompt 2"},
                        "job_type": "test"
                    }
                }
            ]
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            # Mock jobs
            from app.models.agent import AgentJob
            mock_jobs = [
                AgentJob(
                    id=uuid.uuid4(),
                    agent_id=test_agent.id,
                    job_type="test",
                    status=JobStatus.PENDING,
                    user_id=uuid.uuid4(),
                    tenant_id=uuid.uuid4()
                ),
                AgentJob(
                    id=uuid.uuid4(),
                    agent_id=test_agent.id,
                    job_type="test",
                    status=JobStatus.PENDING,
                    user_id=uuid.uuid4(),
                    tenant_id=uuid.uuid4()
                )
            ]
            mock_service.execute_batch_agents.return_value = mock_jobs
            
            response = authenticated_client.post(
                "/api/v1/ai-prompts/execution/batch-execute",
                json=request_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["total_jobs"] == 2
            assert data["successful_jobs"] == 2
            assert data["failed_jobs"] == 0
            assert len(data["jobs"]) == 2
    
    def test_batch_execute_server_error(self, authenticated_client, test_agent):
        """Test batch execution with server error."""
        request_data = {
            "execution_requests": [
                {
                    "agent_id": str(test_agent.id),
                    "doc_type": test_agent.doc_type,
                    "request": {
                        "input_data": {"prompt": "Test prompt"},
                        "job_type": "test"
                    }
                }
            ]
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.execute_batch_agents.side_effect = Exception("Batch execution failed")
            
            response = authenticated_client.post(
                "/api/v1/ai-prompts/execution/batch-execute",
                json=request_data
            )
            
            assert response.status_code == 500
            assert "Failed to execute batch agents" in response.json()["detail"]
    
    def test_get_user_jobs_success(self, authenticated_client, test_job, completed_job):
        """Test getting user jobs successfully."""
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.get_user_jobs.return_value = [test_job, completed_job]
            
            # Mock the database query for total count
            with patch('app.api.v1.ai_prompts.execution.db') as mock_db:
                mock_query = mock_db.query.return_value.filter.return_value
                mock_query.count.return_value = 2
                
                response = authenticated_client.get("/api/v1/ai-prompts/execution/jobs")
                
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 2
                assert data["limit"] == 50
                assert data["offset"] == 0
                assert len(data["jobs"]) == 2
    
    def test_get_user_jobs_with_filters(self, authenticated_client):
        """Test getting user jobs with filters."""
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.get_user_jobs.return_value = []
            
            with patch('app.api.v1.ai_prompts.execution.db') as mock_db:
                mock_query = mock_db.query.return_value.filter.return_value
                mock_query.count.return_value = 0
                
                response = authenticated_client.get(
                    "/api/v1/ai-prompts/execution/jobs?limit=10&offset=5&status_filter=completed"
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data["limit"] == 10
                assert data["offset"] == 5
    
    def test_get_execution_metrics_success(self, authenticated_client):
        """Test getting execution metrics successfully."""
        mock_metrics = {
            "total_jobs": 100,
            "completed_jobs": 85,
            "failed_jobs": 10,
            "running_jobs": 5,
            "success_rate": 85.0,
            "average_execution_time_ms": 2500.0,
            "period_days": 30
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.get_execution_metrics.return_value = mock_metrics
            
            response = authenticated_client.get("/api/v1/ai-prompts/execution/metrics")
            
            assert response.status_code == 200
            data = response.json()
            assert data == mock_metrics
    
    def test_get_execution_metrics_with_custom_days(self, authenticated_client):
        """Test getting execution metrics with custom days parameter."""
        mock_metrics = {
            "total_jobs": 50,
            "completed_jobs": 45,
            "failed_jobs": 3,
            "running_jobs": 2,
            "success_rate": 90.0,
            "average_execution_time_ms": 2000.0,
            "period_days": 7
        }
        
        with patch('app.api.v1.ai_prompts.execution.agent_execution_service') as mock_service:
            mock_service.get_execution_metrics.return_value = mock_metrics
            
            response = authenticated_client.get("/api/v1/ai-prompts/execution/metrics?days=7")
            
            assert response.status_code == 200
            data = response.json()
            assert data["period_days"] == 7
