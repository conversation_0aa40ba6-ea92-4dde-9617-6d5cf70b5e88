"""
Tests for AI Agent Monitoring API endpoints.
"""

import pytest
import uuid
from unittest.mock import patch, Mock
from datetime import datetime, timezone

from app.models.agent import JobStatus, AgentStatus


class TestMonitoringAPI:
    """Test cases for AI Agent Monitoring API endpoints."""
    
    def test_get_monitoring_dashboard_success(self, authenticated_client, test_agent, test_job, completed_job):
        """Test getting monitoring dashboard successfully."""
        # Mock the execution service
        mock_metrics = {
            "total_jobs": 10,
            "completed_jobs": 8,
            "failed_jobs": 1,
            "running_jobs": 1,
            "success_rate": 80.0,
            "average_execution_time_ms": 2000.0,
            "period_days": 7
        }
        
        with patch('app.api.v1.ai_prompts.monitoring.agent_execution_service') as mock_service:
            mock_service.get_execution_metrics.return_value = mock_metrics
            
            # Mock database queries
            with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
                # Mock status distribution query
                mock_status_query = Mock()
                mock_status_query.all.return_value = [
                    Mock(status=JobStatus.COMPLETED, count=8),
                    <PERSON><PERSON>(status=JobStatus.FAILED, count=1),
                    <PERSON><PERSON>(status=JobStatus.RUNNING, count=1)
                ]
                
                # Mock top agents query
                mock_agent_query = Mock()
                mock_agent_query.all.return_value = [
                    Mock(
                        id=test_agent.id,
                        name=test_agent.name,
                        doc_type=test_agent.doc_type,
                        job_count=5,
                        avg_execution_time=2000.0,
                        success_count=4
                    )
                ]
                
                # Mock daily trends query
                mock_trends_query = Mock()
                mock_trends_query.all.return_value = [
                    Mock(
                        date=datetime.now().date(),
                        total_jobs=10,
                        completed_jobs=8,
                        failed_jobs=1
                    )
                ]
                
                # Mock running/pending jobs queries
                mock_running_query = Mock()
                mock_running_query.count.return_value = 1
                mock_pending_query = Mock()
                mock_pending_query.count.return_value = 2
                
                # Set up the mock chain
                mock_db.query.return_value.filter.return_value.group_by.return_value = mock_status_query
                mock_db.query.return_value.join.return_value.filter.return_value.group_by.return_value.order_by.return_value.limit.return_value = mock_agent_query
                mock_db.query.return_value.filter.return_value.group_by.return_value.order_by.return_value = mock_trends_query
                
                # Configure different return values for running and pending queries
                def mock_query_side_effect(*args):
                    mock_result = Mock()
                    mock_result.filter.return_value = mock_running_query if 'running' in str(args) else mock_pending_query
                    return mock_result
                
                mock_db.query.side_effect = [
                    mock_db.query.return_value,  # status distribution
                    mock_db.query.return_value,  # top agents
                    mock_db.query.return_value,  # daily trends
                    Mock(filter=Mock(return_value=Mock(count=Mock(return_value=1)))),  # running jobs
                    Mock(filter=Mock(return_value=Mock(count=Mock(return_value=2))))   # pending jobs
                ]
                
                response = authenticated_client.get("/api/v1/ai-prompts/monitoring/dashboard")
                
                assert response.status_code == 200
                data = response.json()
                
                assert "metrics" in data
                assert "status_distribution" in data
                assert "top_agents" in data
                assert "daily_trends" in data
                assert "current_status" in data
                
                assert data["metrics"] == mock_metrics
                assert data["current_status"]["running_jobs"] == 1
                assert data["current_status"]["pending_jobs"] == 2
    
    def test_get_monitoring_dashboard_with_custom_days(self, authenticated_client):
        """Test getting monitoring dashboard with custom days parameter."""
        mock_metrics = {
            "total_jobs": 5,
            "completed_jobs": 4,
            "failed_jobs": 1,
            "running_jobs": 0,
            "success_rate": 80.0,
            "average_execution_time_ms": 1500.0,
            "period_days": 30
        }
        
        with patch('app.api.v1.ai_prompts.monitoring.agent_execution_service') as mock_service:
            mock_service.get_execution_metrics.return_value = mock_metrics
            
            with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
                # Mock all database queries to return empty results
                mock_empty_query = Mock()
                mock_empty_query.all.return_value = []
                mock_empty_query.count.return_value = 0
                
                mock_db.query.return_value.filter.return_value.group_by.return_value = mock_empty_query
                mock_db.query.return_value.join.return_value.filter.return_value.group_by.return_value.order_by.return_value.limit.return_value = mock_empty_query
                mock_db.query.return_value.filter.return_value.group_by.return_value.order_by.return_value = mock_empty_query
                mock_db.query.return_value.filter.return_value.count.return_value = 0
                
                response = authenticated_client.get("/api/v1/ai-prompts/monitoring/dashboard?days=30")
                
                assert response.status_code == 200
                data = response.json()
                assert data["metrics"]["period_days"] == 30
    
    def test_get_agent_performance_success(self, authenticated_client, test_agent):
        """Test getting agent performance successfully."""
        with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
            # Mock agent query
            mock_agent_query = Mock()
            mock_agent_query.first.return_value = test_agent
            mock_db.query.return_value.filter.return_value = mock_agent_query
            
            # Mock jobs query
            mock_jobs_query = Mock()
            mock_jobs_query.count.return_value = 10
            mock_jobs_query.filter.return_value.count.side_effect = [8, 1]  # completed, failed
            
            # Mock execution times query
            mock_execution_query = Mock()
            mock_execution_query.with_entities.return_value.all.return_value = [
                (1000,), (2000,), (1500,), (2500,), (1800,)
            ]
            mock_jobs_query.filter.return_value.with_entities.return_value = mock_execution_query
            
            # Mock recent jobs query
            mock_recent_jobs = [
                Mock(
                    id=uuid.uuid4(),
                    status=JobStatus.COMPLETED,
                    job_type="test",
                    execution_time_ms=1500,
                    created_at=datetime.now(timezone.utc),
                    completed_at=datetime.now(timezone.utc),
                    error_message=None
                )
            ]
            mock_jobs_query.order_by.return_value.limit.return_value.all.return_value = mock_recent_jobs
            
            # Mock error patterns query
            mock_error_query = Mock()
            mock_error_query.all.return_value = [
                Mock(error_message="Test error", count=2)
            ]
            mock_db.query.return_value.filter.return_value.group_by.return_value.order_by.return_value.limit.return_value = mock_error_query
            
            # Set up the query chain
            mock_db.query.return_value.filter.return_value = mock_jobs_query
            
            response = authenticated_client.get(f"/api/v1/ai-prompts/monitoring/agents/{test_agent.id}/performance")
            
            assert response.status_code == 200
            data = response.json()
            
            assert "agent" in data
            assert "performance" in data
            assert "recent_jobs" in data
            assert "error_patterns" in data
            
            assert data["agent"]["id"] == str(test_agent.id)
            assert data["agent"]["name"] == test_agent.name
            assert data["performance"]["total_jobs"] == 10
            assert data["performance"]["completed_jobs"] == 8
            assert data["performance"]["failed_jobs"] == 1
            assert data["performance"]["success_rate"] == 80.0
    
    def test_get_agent_performance_not_found(self, authenticated_client):
        """Test getting performance for non-existent agent."""
        with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
            mock_agent_query = Mock()
            mock_agent_query.first.return_value = None
            mock_db.query.return_value.filter.return_value = mock_agent_query
            
            response = authenticated_client.get(f"/api/v1/ai-prompts/monitoring/agents/{uuid.uuid4()}/performance")
            
            assert response.status_code == 404
            assert "Agent not found or not accessible" in response.json()["detail"]
    
    def test_get_agent_performance_with_custom_days(self, authenticated_client, test_agent):
        """Test getting agent performance with custom days parameter."""
        with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
            # Mock agent query
            mock_agent_query = Mock()
            mock_agent_query.first.return_value = test_agent
            mock_db.query.return_value.filter.return_value = mock_agent_query
            
            # Mock empty results for other queries
            mock_empty_query = Mock()
            mock_empty_query.count.return_value = 0
            mock_empty_query.filter.return_value.count.return_value = 0
            mock_empty_query.filter.return_value.with_entities.return_value.all.return_value = []
            mock_empty_query.order_by.return_value.limit.return_value.all.return_value = []
            
            mock_db.query.return_value.filter.return_value = mock_empty_query
            
            response = authenticated_client.get(
                f"/api/v1/ai-prompts/monitoring/agents/{test_agent.id}/performance?days=7"
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["agent"]["id"] == str(test_agent.id)
    
    def test_get_system_health_healthy(self, authenticated_client):
        """Test getting system health when system is healthy."""
        with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
            # Mock queries to return healthy status
            mock_query = Mock()
            mock_query.count.return_value = 0  # No stuck jobs
            
            mock_db.query.return_value.filter.return_value = mock_query
            
            # Mock recent jobs and failures
            def count_side_effect():
                counts = [0, 10, 1, 5]  # stuck_jobs, recent_jobs, recent_failures, active_agents
                for count in counts:
                    yield count
            
            count_gen = count_side_effect()
            mock_query.count.side_effect = lambda: next(count_gen)
            
            response = authenticated_client.get("/api/v1/ai-prompts/monitoring/system-health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "healthy"
            assert "timestamp" in data
            assert "metrics" in data
            assert "issues" in data
            
            assert data["metrics"]["stuck_jobs"] == 0
            assert len(data["issues"]) == 0
    
    def test_get_system_health_warning(self, authenticated_client):
        """Test getting system health when system has warnings."""
        with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
            mock_query = Mock()
            
            # Mock to return warning conditions
            def count_side_effect():
                counts = [2, 10, 2, 5]  # stuck_jobs, recent_jobs, recent_failures, active_agents
                for count in counts:
                    yield count
            
            count_gen = count_side_effect()
            mock_query.count.side_effect = lambda: next(count_gen)
            mock_db.query.return_value.filter.return_value = mock_query
            
            response = authenticated_client.get("/api/v1/ai-prompts/monitoring/system-health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "warning"
            assert len(data["issues"]) > 0
            assert any("jobs running for more than 1 hour" in issue for issue in data["issues"])
    
    def test_get_system_health_critical(self, authenticated_client):
        """Test getting system health when system is critical."""
        with patch('app.api.v1.ai_prompts.monitoring.db') as mock_db:
            mock_query = Mock()
            
            # Mock to return critical conditions (high error rate)
            def count_side_effect():
                counts = [0, 10, 3, 5]  # stuck_jobs, recent_jobs, recent_failures (30% error rate), active_agents
                for count in counts:
                    yield count
            
            count_gen = count_side_effect()
            mock_query.count.side_effect = lambda: next(count_gen)
            mock_db.query.return_value.filter.return_value = mock_query
            
            response = authenticated_client.get("/api/v1/ai-prompts/monitoring/system-health")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "critical"
            assert len(data["issues"]) > 0
            assert any("High error rate" in issue for issue in data["issues"])
