"""
Test configuration and fixtures for AI agent execution tests.
"""

import pytest
import asyncio
import uuid
from datetime import datetime, timezone
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from unittest.mock import Mock, AsyncMock

from app.main import app
from app.core.database import get_db, Base
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIAgent, AIPrompt, AgentJob, AgentStatus, AgentType, JobStatus
from app.api.deps import get_current_user, get_current_tenant

# Test database URL (use in-memory SQLite for tests)
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine and session
engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def test_tenant(db_session):
    """Create a test tenant."""
    tenant = Tenant(
        id=uuid.uuid4(),
        name="Test Tenant",
        slug="test-tenant",
        plan="pro"
    )
    db_session.add(tenant)
    db_session.commit()
    db_session.refresh(tenant)
    return tenant


@pytest.fixture
def test_user(db_session, test_tenant):
    """Create a test user."""
    user = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        full_name="Test User",
        hashed_password="fake_hashed_password",
        tenant_id=test_tenant.id,
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def authenticated_client(client, test_user, test_tenant):
    """Create an authenticated test client."""
    def override_get_current_user():
        return test_user
    
    def override_get_current_tenant():
        return test_tenant
    
    app.dependency_overrides[get_current_user] = override_get_current_user
    app.dependency_overrides[get_current_tenant] = override_get_current_tenant
    
    yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
def test_agent(db_session, test_tenant, test_user):
    """Create a test AI agent."""
    agent = AIAgent(
        id=uuid.uuid4(),
        name="Test Agent",
        description="A test agent for unit testing",
        doc_type="test",
        agent_type=AgentType.CUSTOM,
        model_name="claude-3-5-sonnet-20241022",
        system_prompt="You are a helpful test assistant.",
        temperature=30,
        max_tokens=4000,
        capabilities={"document_generation": True, "test_mode": True},
        parameters={"test_param": "test_value"},
        status=AgentStatus.ACTIVE,
        tenant_id=test_tenant.id,
        created_by=test_user.id
    )
    db_session.add(agent)
    db_session.commit()
    db_session.refresh(agent)
    return agent


@pytest.fixture
def system_agent(db_session):
    """Create a system AI agent."""
    agent = AIAgent(
        id=uuid.uuid4(),
        name="System Test Agent",
        description="A system test agent",
        doc_type="test",
        agent_type=AgentType.SYSTEM,
        model_name="claude-3-5-sonnet-20241022",
        system_prompt="You are a system test assistant.",
        temperature=30,
        max_tokens=4000,
        capabilities={"document_generation": True},
        parameters={},
        status=AgentStatus.ACTIVE,
        is_system_default=True,
        tenant_id=None,  # System agent
        created_by=None
    )
    db_session.add(agent)
    db_session.commit()
    db_session.refresh(agent)
    return agent


@pytest.fixture
def test_prompt(db_session, test_agent, test_tenant, test_user):
    """Create a test AI prompt."""
    prompt = AIPrompt(
        id=uuid.uuid4(),
        name="Test Prompt",
        description="A test prompt template",
        doc_type="test",
        prompt_template="Generate a test document about {topic}",
        variables=["topic"],
        prompt_type="generation",
        category="test",
        agent_id=test_agent.id,
        is_active=True,
        tenant_id=test_tenant.id,
        created_by=test_user.id
    )
    db_session.add(prompt)
    db_session.commit()
    db_session.refresh(prompt)
    return prompt


@pytest.fixture
def test_job(db_session, test_agent, test_user, test_tenant):
    """Create a test agent job."""
    job = AgentJob(
        id=uuid.uuid4(),
        agent_id=test_agent.id,
        job_type="test",
        input_data={"prompt": "Test prompt", "test_data": "test_value"},
        status=JobStatus.PENDING,
        user_id=test_user.id,
        tenant_id=test_tenant.id
    )
    db_session.add(job)
    db_session.commit()
    db_session.refresh(job)
    return job


@pytest.fixture
def completed_job(db_session, test_agent, test_user, test_tenant):
    """Create a completed test agent job."""
    job = AgentJob(
        id=uuid.uuid4(),
        agent_id=test_agent.id,
        job_type="test",
        input_data={"prompt": "Test prompt"},
        output_data={"result": "Test result"},
        status=JobStatus.COMPLETED,
        execution_time_ms=1500,
        model_used="claude-3-5-sonnet-20241022",
        progress_percentage=100,
        user_id=test_user.id,
        tenant_id=test_tenant.id,
        started_at=datetime.now(timezone.utc),
        completed_at=datetime.now(timezone.utc)
    )
    db_session.add(job)
    db_session.commit()
    db_session.refresh(job)
    return job


@pytest.fixture
def mock_anthropic_service():
    """Mock the Anthropic service for testing."""
    mock_service = Mock()
    mock_service.generate_with_agent_config = AsyncMock(return_value="Test generated content")
    return mock_service
