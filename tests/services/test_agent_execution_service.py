"""
Tests for the AI Agent Execution Service.
"""

import pytest
import uuid
from unittest.mock import patch, AsyncMock
from datetime import datetime, timezone

from app.services.agent_execution_service import AgentExecutionService
from app.schemas.agent import AgentExecuteRequest
from app.models.agent import JobStatus, AgentStatus


class TestAgentExecutionService:
    """Test cases for AgentExecutionService."""
    
    @pytest.fixture
    def service(self):
        """Create an instance of the service for testing."""
        return AgentExecutionService()
    
    @pytest.mark.asyncio
    async def test_execute_agent_success(self, service, db_session, test_agent, test_user, test_tenant):
        """Test successful agent execution."""
        request = AgentExecuteRequest(
            input_data={"prompt": "Test prompt"},
            job_type="test"
        )
        
        with patch.object(service, '_execute_job_async') as mock_execute:
            job = await service.execute_agent(
                agent_id=test_agent.id,
                doc_type=test_agent.doc_type,
                request=request,
                user_id=test_user.id,
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            assert job is not None
            assert job.agent_id == test_agent.id
            assert job.job_type == "test"
            assert job.status == JobStatus.PENDING
            assert job.user_id == test_user.id
            assert job.tenant_id == test_tenant.id
            
            # Verify async execution was started
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_agent_invalid_agent(self, service, db_session, test_user, test_tenant):
        """Test execution with invalid agent ID."""
        request = AgentExecuteRequest(
            input_data={"prompt": "Test prompt"},
            job_type="test"
        )
        
        with pytest.raises(ValueError, match="Agent not found or not accessible"):
            await service.execute_agent(
                agent_id=uuid.uuid4(),  # Non-existent agent
                doc_type="test",
                request=request,
                user_id=test_user.id,
                tenant_id=test_tenant.id,
                db=db_session
            )
    
    @pytest.mark.asyncio
    async def test_execute_agent_inactive_agent(self, service, db_session, test_agent, test_user, test_tenant):
        """Test execution with inactive agent."""
        # Make agent inactive
        test_agent.status = AgentStatus.INACTIVE
        db_session.commit()
        
        request = AgentExecuteRequest(
            input_data={"prompt": "Test prompt"},
            job_type="test"
        )
        
        with pytest.raises(ValueError, match="Agent not found or not accessible"):
            await service.execute_agent(
                agent_id=test_agent.id,
                doc_type=test_agent.doc_type,
                request=request,
                user_id=test_user.id,
                tenant_id=test_tenant.id,
                db=db_session
            )
    
    @pytest.mark.asyncio
    async def test_execute_agent_no_generation_capability(self, service, db_session, test_agent, test_user, test_tenant):
        """Test execution with agent that doesn't support document generation."""
        # Remove document generation capability
        test_agent.capabilities = {"other_capability": True}
        db_session.commit()
        
        request = AgentExecuteRequest(
            input_data={"prompt": "Test prompt"},
            job_type="test"
        )
        
        with pytest.raises(ValueError, match="Agent does not support document generation"):
            await service.execute_agent(
                agent_id=test_agent.id,
                doc_type=test_agent.doc_type,
                request=request,
                user_id=test_user.id,
                tenant_id=test_tenant.id,
                db=db_session
            )
    
    @pytest.mark.asyncio
    async def test_execute_batch_agents_success(self, service, db_session, test_agent, test_user, test_tenant):
        """Test successful batch agent execution."""
        execution_requests = [
            {
                "agent_id": test_agent.id,
                "doc_type": test_agent.doc_type,
                "request": {
                    "input_data": {"prompt": "Test prompt 1"},
                    "job_type": "test"
                }
            },
            {
                "agent_id": test_agent.id,
                "doc_type": test_agent.doc_type,
                "request": {
                    "input_data": {"prompt": "Test prompt 2"},
                    "job_type": "test"
                }
            }
        ]
        
        with patch.object(service, '_execute_job_async'):
            jobs = await service.execute_batch_agents(
                execution_requests=execution_requests,
                user_id=test_user.id,
                tenant_id=test_tenant.id,
                db=db_session
            )
            
            assert len(jobs) == 2
            assert all(job.status == JobStatus.PENDING for job in jobs)
            assert all(job.user_id == test_user.id for job in jobs)
            assert all(job.tenant_id == test_tenant.id for job in jobs)
    
    @pytest.mark.asyncio
    async def test_get_job_status_success(self, service, db_session, test_job, test_user, test_tenant):
        """Test getting job status successfully."""
        job = await service.get_job_status(
            job_id=test_job.id,
            user_id=test_user.id,
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        assert job is not None
        assert job.id == test_job.id
        assert job.status == test_job.status
    
    @pytest.mark.asyncio
    async def test_get_job_status_not_found(self, service, db_session, test_user, test_tenant):
        """Test getting job status for non-existent job."""
        job = await service.get_job_status(
            job_id=uuid.uuid4(),
            user_id=test_user.id,
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        assert job is None
    
    @pytest.mark.asyncio
    async def test_cancel_job_success(self, service, db_session, test_job, test_user, test_tenant):
        """Test successful job cancellation."""
        # Set job to running status
        test_job.status = JobStatus.RUNNING
        db_session.commit()
        
        success = await service.cancel_job(
            job_id=test_job.id,
            user_id=test_user.id,
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        assert success is True
        
        # Refresh job from database
        db_session.refresh(test_job)
        assert test_job.status == JobStatus.CANCELLED
        assert test_job.error_message == "Job cancelled by user"
        assert test_job.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_cancel_completed_job(self, service, db_session, completed_job, test_user, test_tenant):
        """Test cancelling an already completed job."""
        success = await service.cancel_job(
            job_id=completed_job.id,
            user_id=test_user.id,
            tenant_id=test_tenant.id,
            db=db_session
        )
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_get_user_jobs(self, service, db_session, test_job, completed_job, test_user, test_tenant):
        """Test getting user jobs with pagination."""
        jobs = await service.get_user_jobs(
            user_id=test_user.id,
            tenant_id=test_tenant.id,
            db=db_session,
            limit=10,
            offset=0
        )
        
        assert len(jobs) == 2
        # Jobs should be ordered by created_at desc
        assert jobs[0].created_at >= jobs[1].created_at
    
    @pytest.mark.asyncio
    async def test_get_user_jobs_with_status_filter(self, service, db_session, test_job, completed_job, test_user, test_tenant):
        """Test getting user jobs with status filter."""
        jobs = await service.get_user_jobs(
            user_id=test_user.id,
            tenant_id=test_tenant.id,
            db=db_session,
            status_filter=JobStatus.COMPLETED
        )
        
        assert len(jobs) == 1
        assert jobs[0].status == JobStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_get_execution_metrics(self, service, db_session, completed_job, test_tenant):
        """Test getting execution metrics."""
        # Create a failed job for testing
        from app.models.agent import AgentJob
        failed_job = AgentJob(
            id=uuid.uuid4(),
            agent_id=completed_job.agent_id,
            job_type="test",
            input_data={"prompt": "Test"},
            status=JobStatus.FAILED,
            error_message="Test error",
            user_id=completed_job.user_id,
            tenant_id=test_tenant.id
        )
        db_session.add(failed_job)
        db_session.commit()
        
        metrics = await service.get_execution_metrics(
            tenant_id=test_tenant.id,
            db=db_session,
            days=30
        )
        
        assert metrics["total_jobs"] == 2
        assert metrics["completed_jobs"] == 1
        assert metrics["failed_jobs"] == 1
        assert metrics["success_rate"] == 50.0
        assert "average_execution_time_ms" in metrics
    
    @pytest.mark.asyncio
    async def test_execute_with_retry_success(self, service, db_session, test_agent, test_job, mock_anthropic_service):
        """Test successful execution with retry logic."""
        with patch('app.services.agent_execution_service.anthropic_service', mock_anthropic_service):
            result = await service._execute_with_retry(
                agent=test_agent,
                execution_context={"prompt": "Test prompt"},
                job=test_job,
                db=db_session
            )
            
            assert result == "Test generated content"
            mock_anthropic_service.generate_with_agent_config.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_with_retry_failure(self, service, db_session, test_agent, test_job):
        """Test execution failure with retry logic."""
        mock_service = AsyncMock()
        mock_service.generate_with_agent_config.side_effect = Exception("API Error")
        
        with patch('app.services.agent_execution_service.anthropic_service', mock_service):
            with pytest.raises(RuntimeError, match="All 3 execution attempts failed"):
                await service._execute_with_retry(
                    agent=test_agent,
                    execution_context={"prompt": "Test prompt"},
                    job=test_job,
                    db=db_session
                )
            
            # Should have retried 3 times
            assert mock_service.generate_with_agent_config.call_count == 3
