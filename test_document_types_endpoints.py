#!/usr/bin/env python3
"""
Comprehensive test suite for document_types API endpoints.
Tests CRUD operations, validation, permissions, and edge cases.
"""

import pytest
import uuid
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch

# Import the FastAPI app and dependencies
from app.main import app
from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document_type import DocumentType

# Test client
client = TestClient(app)

# Mock data
MOCK_TENANT_ID = uuid.UUID('12345678-1234-1234-1234-123456789012')
MOCK_USER_ID = uuid.UUID('*************-4321-4321-************')
MOCK_ADMIN_USER_ID = uuid.UUID('11111111-1111-1111-1111-111111111111')

def create_mock_user(user_id: uuid.UUID, role: str = "member", is_active: bool = True):
    """Create a mock user for testing."""
    user = Mock(spec=User)
    user.id = user_id
    user.email = f"test-{user_id}@example.com"
    user.full_name = "Test User"
    user.role = role
    user.is_active = is_active
    user.tenant_id = MOCK_TENANT_ID
    return user

def create_mock_tenant():
    """Create a mock tenant for testing."""
    tenant = Mock(spec=Tenant)
    tenant.id = MOCK_TENANT_ID
    tenant.name = "Test Tenant"
    tenant.slug = "test-tenant"
    tenant.plan = "pro"
    return tenant

def create_mock_db():
    """Create a mock database session."""
    return Mock(spec=Session)

def create_test_document_type(tenant_id: uuid.UUID = None, is_system: bool = False):
    """Create a test document type mock object."""
    doc_type = Mock(spec=DocumentType)
    doc_type.id = uuid.uuid4()
    doc_type.name = "Test Document Type"
    doc_type.slug = "test-doc-type"
    doc_type.description = "A test document type"
    doc_type.category = "test"
    doc_type.industry = "technology"
    doc_type.form_schema = {
        "sections": [
            {
                "id": "overview",
                "title": "Overview",
                "questions": [
                    {
                        "id": "title",
                        "type": "text",
                        "label": "Title",
                        "required": True
                    }
                ]
            }
        ]
    }
    doc_type.template_structure = {
        "sections": [
            {
                "id": "overview",
                "title": "Overview",
                "content_type": "form_based",
                "order": 1
            }
        ]
    }
    doc_type.ai_agents = {
        "primary_agent": "claude-3-5-sonnet-20241022"
    }
    doc_type.refinement_options = {
        "tone_options": ["professional", "casual"],
        "length_options": ["brief", "standard"]
    }
    doc_type.is_system_default = is_system
    doc_type.is_active = True
    doc_type.version = "1.0"
    doc_type.usage_count = 0
    doc_type.tenant_id = tenant_id
    doc_type.created_by = MOCK_USER_ID
    doc_type.created_at = "2024-01-01T00:00:00Z"
    doc_type.updated_at = "2024-01-01T00:00:00Z"
    return doc_type

class TestDocumentTypesAPI:
    """Test class for document types API endpoints."""

    def setup_method(self):
        """Set up test dependencies."""
        self.mock_user = create_mock_user(MOCK_USER_ID)
        self.mock_admin_user = create_mock_user(MOCK_ADMIN_USER_ID, role="admin")
        self.mock_tenant = create_mock_tenant()
        self.mock_db = create_mock_db()

    def test_list_document_types_success(self):
        """Test successful listing of document types."""
        # Mock document types
        system_doc_type = create_test_document_type(is_system=True)
        tenant_doc_type = create_test_document_type(tenant_id=MOCK_TENANT_ID)
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [system_doc_type, tenant_doc_type]
        
        self.mock_db.query.return_value = mock_query

        # Override dependencies
        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get("/api/v1/documents/types/")
            assert response.status_code == 200
            
            # Verify the query was called correctly
            self.mock_db.query.assert_called_once()
            
        finally:
            # Clean up overrides
            app.dependency_overrides.clear()

    def test_list_document_types_with_filters(self):
        """Test listing document types with category and industry filters."""
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = []
        
        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(
                "/api/v1/documents/types/?category=product&industry=technology&active_only=true"
            )
            assert response.status_code == 200
            
            # Verify filters were applied
            assert mock_query.filter.call_count >= 3  # Base filter + category + industry
            
        finally:
            app.dependency_overrides.clear()

    def test_get_document_type_success(self):
        """Test successful retrieval of a specific document type."""
        doc_type_id = uuid.uuid4()
        mock_doc_type = create_test_document_type()
        mock_doc_type["id"] = str(doc_type_id)
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_doc_type
        
        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/{doc_type_id}")
            assert response.status_code == 200
            
        finally:
            app.dependency_overrides.clear()

    def test_get_document_type_not_found(self):
        """Test retrieval of non-existent document type."""
        doc_type_id = uuid.uuid4()
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/{doc_type_id}")
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
            
        finally:
            app.dependency_overrides.clear()

    def test_create_document_type_success_admin(self):
        """Test successful creation of document type by admin user."""
        new_doc_type = {
            "name": "New Test Document Type",
            "slug": "new-test-doc-type",
            "description": "A new test document type",
            "category": "test",
            "industry": "technology",
            "form_schema": {
                "sections": [
                    {
                        "id": "overview",
                        "title": "Overview",
                        "questions": [
                            {
                                "id": "title",
                                "type": "text",
                                "label": "Title",
                                "required": True
                            }
                        ]
                    }
                ]
            },
            "template_structure": {
                "sections": [
                    {
                        "id": "overview",
                        "title": "Overview",
                        "content_type": "form_based",
                        "order": 1
                    }
                ]
            }
        }

        # Mock no existing document type with same slug
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None

        # Create a mock document type that will be returned after creation
        created_doc_type = create_test_document_type(tenant_id=MOCK_TENANT_ID)
        created_doc_type.name = new_doc_type["name"]
        created_doc_type.slug = new_doc_type["slug"]

        self.mock_db.query.return_value = mock_query
        self.mock_db.add = Mock()
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock(side_effect=lambda x: setattr(x, 'id', created_doc_type.id))

        # Mock the DocumentType constructor to return our mock
        with patch('app.api.v1.document_types.DocumentType', return_value=created_doc_type):
            app.dependency_overrides[get_current_user] = lambda: self.mock_admin_user
            app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
            app.dependency_overrides[get_db] = lambda: self.mock_db

            try:
                response = client.post(
                    "/api/v1/documents/types/",
                    json=new_doc_type
                )
                assert response.status_code == 200

                # Verify database operations
                self.mock_db.add.assert_called_once()
                self.mock_db.commit.assert_called_once()
                self.mock_db.refresh.assert_called_once()

            finally:
                app.dependency_overrides.clear()

    def test_create_document_type_forbidden_non_admin(self):
        """Test creation of document type by non-admin user (should fail)."""
        new_doc_type = {
            "name": "New Test Document Type",
            "slug": "new-test-doc-type",
            "description": "A new test document type",
            "category": "test",
            "form_schema": {"sections": []},
            "template_structure": {"sections": []}
        }

        app.dependency_overrides[get_current_user] = lambda: self.mock_user  # Non-admin
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.post(
                "/api/v1/documents/types/",
                json=new_doc_type
            )
            assert response.status_code == 403
            assert "admin" in response.json()["detail"].lower()
            
        finally:
            app.dependency_overrides.clear()

    def test_create_document_type_duplicate_slug(self):
        """Test creation of document type with duplicate slug."""
        new_doc_type = {
            "name": "New Test Document Type",
            "slug": "existing-slug",
            "description": "A new test document type",
            "category": "test",
            "form_schema": {"sections": []},
            "template_structure": {"sections": []}
        }
        
        # Mock existing document type with same slug
        existing_doc_type = create_test_document_type()
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = existing_doc_type
        
        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_admin_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.post(
                "/api/v1/documents/types/",
                json=new_doc_type
            )
            assert response.status_code == 400
            assert "already exists" in response.json()["detail"].lower()
            
        finally:
            app.dependency_overrides.clear()

    def test_create_document_type_invalid_data(self):
        """Test creation of document type with invalid data."""
        invalid_doc_type = {
            "name": "",  # Empty name should fail validation
            "slug": "test-slug",
            "category": "test",
            "form_schema": {"sections": []},
            "template_structure": {"sections": []}
        }

        app.dependency_overrides[get_current_user] = lambda: self.mock_admin_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.post(
                "/api/v1/documents/types/",
                json=invalid_doc_type
            )
            assert response.status_code == 422  # Validation error
            
        finally:
            app.dependency_overrides.clear()

    def test_update_document_type_success(self):
        """Test successful update of document type by admin."""
        doc_type_id = uuid.uuid4()
        existing_doc_type = create_test_document_type(tenant_id=MOCK_TENANT_ID)
        existing_doc_type.id = doc_type_id
        existing_doc_type.is_system_default = False  # Ensure it's not a system default

        update_data = {
            "name": "Updated Document Type",
            "description": "Updated description"
        }

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = existing_doc_type

        self.mock_db.query.return_value = mock_query
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()

        app.dependency_overrides[get_current_user] = lambda: self.mock_admin_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.put(
                f"/api/v1/documents/types/{doc_type_id}",
                json=update_data
            )
            assert response.status_code == 200

            # Verify the attributes were updated
            assert existing_doc_type.name == "Updated Document Type"
            assert existing_doc_type.description == "Updated description"

        finally:
            app.dependency_overrides.clear()

    def test_update_document_type_forbidden_non_admin(self):
        """Test update of document type by non-admin user (should fail)."""
        doc_type_id = uuid.uuid4()
        update_data = {"name": "Updated Name"}

        app.dependency_overrides[get_current_user] = lambda: self.mock_user  # Non-admin
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.put(
                f"/api/v1/documents/types/{doc_type_id}",
                json=update_data
            )
            assert response.status_code == 403

        finally:
            app.dependency_overrides.clear()

    def test_delete_document_type_success(self):
        """Test successful soft deletion of document type."""
        doc_type_id = uuid.uuid4()
        existing_doc_type = create_test_document_type(tenant_id=MOCK_TENANT_ID)
        existing_doc_type.id = doc_type_id
        existing_doc_type.is_system_default = False

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = existing_doc_type

        self.mock_db.query.return_value = mock_query
        self.mock_db.commit = Mock()

        app.dependency_overrides[get_current_user] = lambda: self.mock_admin_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.delete(f"/api/v1/documents/types/{doc_type_id}")
            assert response.status_code == 200
            assert "deleted successfully" in response.json()["message"]

            # Verify the document type was marked as inactive
            assert existing_doc_type.is_active == False

        finally:
            app.dependency_overrides.clear()

    def test_get_form_schema_success(self):
        """Test successful retrieval of form schema."""
        doc_type_id = uuid.uuid4()
        mock_doc_type = create_test_document_type()
        mock_doc_type["id"] = str(doc_type_id)

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_doc_type

        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/{doc_type_id}/form-schema")
            assert response.status_code == 200
            assert "form_schema" in response.json()

        finally:
            app.dependency_overrides.clear()

    def test_get_template_structure_success(self):
        """Test successful retrieval of template structure."""
        doc_type_id = uuid.uuid4()
        mock_doc_type = create_test_document_type()
        mock_doc_type["id"] = str(doc_type_id)

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_doc_type

        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/{doc_type_id}/template")
            assert response.status_code == 200
            assert "template_structure" in response.json()

        finally:
            app.dependency_overrides.clear()

    def test_get_ai_agents_success(self):
        """Test successful retrieval of AI agents configuration."""
        doc_type_id = uuid.uuid4()
        mock_doc_type = create_test_document_type()
        mock_doc_type["id"] = str(doc_type_id)

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_doc_type

        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/{doc_type_id}/agents")
            assert response.status_code == 200
            assert "ai_agents" in response.json()

        finally:
            app.dependency_overrides.clear()

    def test_get_refinement_options_success(self):
        """Test successful retrieval of refinement options."""
        doc_type_id = uuid.uuid4()
        mock_doc_type = create_test_document_type()
        mock_doc_type["id"] = str(doc_type_id)

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_doc_type

        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/{doc_type_id}/refinements")
            assert response.status_code == 200
            assert "refinement_options" in response.json()

        finally:
            app.dependency_overrides.clear()

    def test_get_by_industry_success(self):
        """Test successful retrieval of document types by industry."""
        industry = "technology"
        mock_doc_types = [create_test_document_type()]

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = mock_doc_types

        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/by-industry/{industry}")
            assert response.status_code == 200

        finally:
            app.dependency_overrides.clear()

    def test_get_by_category_success(self):
        """Test successful retrieval of document types by category."""
        category = "product"
        mock_doc_types = [create_test_document_type()]

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = mock_doc_types

        self.mock_db.query.return_value = mock_query

        app.dependency_overrides[get_current_user] = lambda: self.mock_user
        app.dependency_overrides[get_current_tenant] = lambda: self.mock_tenant
        app.dependency_overrides[get_db] = lambda: self.mock_db

        try:
            response = client.get(f"/api/v1/documents/types/by-category/{category}")
            assert response.status_code == 200

        finally:
            app.dependency_overrides.clear()

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
