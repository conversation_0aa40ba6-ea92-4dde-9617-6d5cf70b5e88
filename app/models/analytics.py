"""
Analytics and Insights Models
Tracks metrics, performance data, and aggregated statistics for dashboard and reporting.
"""
from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, JSON, ForeignKey, Float, Date, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum

from app.core.database import Base


class MetricType(str, enum.Enum):
    DOCUMENT_CREATION = "document_creation"
    DOCUMENT_COMPLETION = "document_completion"
    AI_GENERATION = "ai_generation"
    REFINEMENT = "refinement"
    EXPORT = "export"
    FORM_COMPLETION = "form_completion"
    USER_ENGAGEMENT = "user_engagement"
    TEMPLATE_USAGE = "template_usage"


class AggregationPeriod(str, enum.Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class AnalyticsMetric(Base):
    """Core analytics metrics table for aggregated data"""
    __tablename__ = "analytics_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Metric identification
    metric_type = Column(String(50), nullable=False, index=True)
    metric_name = Column(String(100), nullable=False)
    
    # Dimensions for filtering/grouping
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    document_type_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Time dimensions
    date = Column(Date, nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly
    
    # Metric values
    count_value = Column(Integer, default=0)
    sum_value = Column(Float, default=0.0)
    avg_value = Column(Float, default=0.0)
    min_value = Column(Float, nullable=True)
    max_value = Column(Float, nullable=True)
    
    # Additional data
    extra_data = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Composite indexes for common queries
    __table_args__ = (
        Index('idx_analytics_tenant_date', 'tenant_id', 'date'),
        Index('idx_analytics_type_date', 'metric_type', 'date'),
        Index('idx_analytics_doctype_date', 'document_type_id', 'date'),
    )


class DocumentAnalytics(Base):
    """Document-specific analytics and performance metrics"""
    __tablename__ = "document_analytics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Document reference
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False, unique=True)
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Generation metrics
    generation_time_seconds = Column(Float, nullable=True)
    ai_model_used = Column(String(100), nullable=True)
    tokens_used = Column(Integer, nullable=True)
    generation_attempts = Column(Integer, default=1)
    
    # Content metrics
    content_length = Column(Integer, nullable=True)
    word_count = Column(Integer, nullable=True)
    section_count = Column(Integer, nullable=True)
    
    # Engagement metrics
    view_count = Column(Integer, default=0)
    edit_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    
    # Quality metrics
    refinement_count = Column(Integer, default=0)
    export_count = Column(Integer, default=0)
    rating_sum = Column(Integer, default=0)
    rating_count = Column(Integer, default=0)
    
    # Time tracking
    time_to_completion_minutes = Column(Float, nullable=True)
    last_activity_at = Column(DateTime(timezone=True), nullable=True)
    
    # Form completion metrics
    form_completion_time_seconds = Column(Float, nullable=True)
    form_field_count = Column(Integer, nullable=True)
    form_validation_errors = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class TeamPerformanceMetrics(Base):
    """Team-level performance metrics aggregated by time period"""
    __tablename__ = "team_performance_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Team identification
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    period_type = Column(String(20), nullable=False)  # weekly, monthly, quarterly
    
    # Team composition
    active_users = Column(Integer, default=0)
    total_users = Column(Integer, default=0)
    new_users = Column(Integer, default=0)
    
    # Productivity metrics
    documents_created = Column(Integer, default=0)
    documents_completed = Column(Integer, default=0)
    avg_completion_time_hours = Column(Float, nullable=True)
    
    # Collaboration metrics
    documents_shared = Column(Integer, default=0)
    comments_made = Column(Integer, default=0)
    avg_collaboration_score = Column(Float, nullable=True)
    
    # Quality metrics
    refinements_requested = Column(Integer, default=0)
    avg_refinements_per_doc = Column(Float, nullable=True)
    avg_document_rating = Column(Float, nullable=True)
    
    # AI usage metrics
    ai_generations = Column(Integer, default=0)
    ai_success_rate = Column(Float, nullable=True)
    avg_ai_response_time = Column(Float, nullable=True)
    
    # Export metrics
    exports_completed = Column(Integer, default=0)
    most_popular_export_format = Column(String(50), nullable=True)
    
    # Additional metrics
    extra_data = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class AIEffectivenessMetrics(Base):
    """AI performance and effectiveness metrics by document type"""
    __tablename__ = "ai_effectiveness_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Scope identification
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    agent_id = Column(UUID(as_uuid=True), ForeignKey("ai_agents.id"), nullable=True)
    
    # Time period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    period_type = Column(String(20), nullable=False)
    
    # Generation metrics
    total_generations = Column(Integer, default=0)
    successful_generations = Column(Integer, default=0)
    failed_generations = Column(Integer, default=0)
    avg_generation_time_seconds = Column(Float, nullable=True)
    
    # Token usage
    total_tokens_used = Column(Integer, default=0)
    avg_tokens_per_generation = Column(Float, nullable=True)
    
    # Quality metrics
    avg_user_rating = Column(Float, nullable=True)
    refinement_rate = Column(Float, nullable=True)  # % of docs that needed refinement
    avg_refinements_per_doc = Column(Float, nullable=True)
    
    # User satisfaction
    positive_feedback_count = Column(Integer, default=0)
    negative_feedback_count = Column(Integer, default=0)
    satisfaction_score = Column(Float, nullable=True)
    
    # Performance trends
    improvement_rate = Column(Float, nullable=True)  # Month-over-month improvement
    consistency_score = Column(Float, nullable=True)  # How consistent the AI output is
    
    # Model information
    primary_model_used = Column(String(100), nullable=True)
    model_performance_data = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ExportUsageMetrics(Base):
    """Export usage statistics and patterns"""
    __tablename__ = "export_usage_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Scope
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=True)
    
    # Time period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    period_type = Column(String(20), nullable=False)
    
    # Export format metrics
    pdf_exports = Column(Integer, default=0)
    docx_exports = Column(Integer, default=0)
    html_exports = Column(Integer, default=0)
    confluence_exports = Column(Integer, default=0)
    notion_exports = Column(Integer, default=0)
    jira_exports = Column(Integer, default=0)
    custom_exports = Column(Integer, default=0)
    
    # Performance metrics
    total_exports = Column(Integer, default=0)
    successful_exports = Column(Integer, default=0)
    failed_exports = Column(Integer, default=0)
    avg_export_time_seconds = Column(Float, nullable=True)
    
    # Usage patterns
    most_popular_format = Column(String(50), nullable=True)
    peak_usage_hour = Column(Integer, nullable=True)
    avg_file_size_mb = Column(Float, nullable=True)
    
    # Integration metrics
    integration_usage = Column(JSON, default={})  # Usage by integration type
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class FormCompletionMetrics(Base):
    """Form completion analytics by document type"""
    __tablename__ = "form_completion_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Scope
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    
    # Time period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    period_type = Column(String(20), nullable=False)
    
    # Completion metrics
    total_form_starts = Column(Integer, default=0)
    total_form_completions = Column(Integer, default=0)
    completion_rate = Column(Float, nullable=True)
    avg_completion_time_minutes = Column(Float, nullable=True)
    
    # Field-level metrics
    avg_fields_completed = Column(Float, nullable=True)
    most_abandoned_field = Column(String(100), nullable=True)
    validation_error_rate = Column(Float, nullable=True)
    
    # User behavior
    avg_session_duration_minutes = Column(Float, nullable=True)
    bounce_rate = Column(Float, nullable=True)  # % who leave without completing any fields
    return_rate = Column(Float, nullable=True)  # % who come back to complete
    
    # Question-specific metrics
    question_metrics = Column(JSON, default={})  # Per-question completion and error rates
    
    # Device and context
    mobile_completion_rate = Column(Float, nullable=True)
    desktop_completion_rate = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
