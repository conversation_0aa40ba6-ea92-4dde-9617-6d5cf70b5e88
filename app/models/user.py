from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON>, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from app.core.database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    role = Column(String, default="member")  # admin, member, viewer

    # Relationships
    tenant = relationship("Tenant")
    last_login = Column(DateTime(timezone=True), nullable=True)
    preferences = Column(JSON, default={
        "theme": "light",
        "language": "en",
        "notifications": {
            "email_notifications": True,
            "push_notifications": True,
            "marketing_emails": False
        },
        "timezone": "UTC"
    })
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())