"""
AI Agent and Prompt Management Models
"""
from sqlalchemy import <PERSON>umn, String, Text, <PERSON>olean, Integer, DateTime, JSON, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum

from app.core.database import Base


class AgentStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"


class AgentType(str, enum.Enum):
    SYSTEM = "system"
    CUSTOM = "custom"
    SPECIALIZED = "specialized"


class JobStatus(str, enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AIAgent(Base):
    __tablename__ = "ai_agents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    doc_type = Column(String(50), nullable=False, index=True)  # prd, brd, technical_spec, etc.
    
    # Agent configuration
    agent_type = Column(Enum(AgentType), default=AgentType.CUSTOM)
    model_name = Column(String(100), nullable=False)  # claude-3-5-sonnet-20241022, etc.
    system_prompt = Column(Text, nullable=False)
    temperature = Column(Integer, default=30)  # 0-100, stored as int for precision
    max_tokens = Column(Integer, default=4000)
    
    # Capabilities and settings
    capabilities = Column(JSON, default={
        "document_generation": True,
        "refinement": True,
        "follow_up_questions": True,
        "suggestions": True
    })
    
    # Configuration parameters
    parameters = Column(JSON, default={})
    
    # Status and metadata
    status = Column(Enum(AgentStatus), default=AgentStatus.ACTIVE)
    is_system_default = Column(Boolean, default=False)
    version = Column(String(20), default="1.0")
    usage_count = Column(Integer, default=0)
    
    # Tenant and user association
    tenant_id = Column(UUID(as_uuid=True), nullable=True)  # null for system agents
    created_by = Column(UUID(as_uuid=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    prompts = relationship("AIPrompt", back_populates="agent", cascade="all, delete-orphan")
    jobs = relationship("AgentJob", back_populates="agent")


class AIPrompt(Base):
    __tablename__ = "ai_prompts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    doc_type = Column(String(50), nullable=False, index=True)
    
    # Prompt content
    prompt_template = Column(Text, nullable=False)
    variables = Column(JSON, default=[])  # List of variable names used in template
    
    # Prompt metadata
    prompt_type = Column(String(50), nullable=False)  # generation, refinement, follow_up, etc.
    category = Column(String(50), nullable=True)  # business, technical, creative, etc.
    
    # Associated agent (optional)
    agent_id = Column(UUID(as_uuid=True), ForeignKey("ai_agents.id"), nullable=True)
    
    # Status and metadata
    is_active = Column(Boolean, default=True)
    is_system_default = Column(Boolean, default=False)
    version = Column(String(20), default="1.0")
    usage_count = Column(Integer, default=0)
    
    # Tenant and user association
    tenant_id = Column(UUID(as_uuid=True), nullable=True)  # null for system prompts
    created_by = Column(UUID(as_uuid=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    agent = relationship("AIAgent", back_populates="prompts")


class AgentJob(Base):
    __tablename__ = "agent_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Job details
    agent_id = Column(UUID(as_uuid=True), ForeignKey("ai_agents.id"), nullable=False)
    job_type = Column(String(50), nullable=False)  # execute, generate, refine, etc.
    
    # Input and output
    input_data = Column(JSON, nullable=False)
    output_data = Column(JSON, nullable=True)
    
    # Status and progress
    status = Column(Enum(JobStatus), default=JobStatus.PENDING)
    progress_percentage = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    
    # Execution metadata
    execution_time_ms = Column(Integer, nullable=True)
    tokens_used = Column(Integer, nullable=True)
    model_used = Column(String(100), nullable=True)
    
    # Context
    document_id = Column(UUID(as_uuid=True), nullable=True)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    agent = relationship("AIAgent", back_populates="jobs")
