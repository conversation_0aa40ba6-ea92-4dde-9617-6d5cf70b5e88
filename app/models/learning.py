"""
Learning and AI Enhancement models for tracking AI learning patterns, feedback, and profiles.
"""
from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, Float, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from app.core.database import Base


class LearningProfile(Base):
    """Learning profile for a specific document type within a tenant"""
    __tablename__ = "learning_profiles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Document type and tenant context
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Learning configuration
    learning_enabled = Column(Boolean, default=True)
    auto_apply_patterns = Column(Boolean, default=False)
    confidence_threshold = Column(Float, default=0.7)
    
    # Learning statistics
    total_documents_processed = Column(Integer, default=0)
    total_feedback_received = Column(Integer, default=0)
    positive_feedback_count = Column(Integer, default=0)
    negative_feedback_count = Column(Integer, default=0)
    
    # Learning data
    learned_patterns = Column(JSON, default=[])
    user_preferences = Column(JSON, default={})
    performance_metrics = Column(JSON, default={})
    
    # Settings
    settings = Column(JSON, default={
        "pattern_detection_sensitivity": "medium",
        "feedback_weight": 1.0,
        "pattern_expiry_days": 90,
        "max_patterns_stored": 100
    })
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_learning_update = Column(DateTime(timezone=True), nullable=True)


class AIFeedback(Base):
    """User feedback on AI-generated content"""
    __tablename__ = "ai_feedback"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Context
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=True)
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Feedback details
    feedback_type = Column(String, nullable=False)  # quality, accuracy, relevance, style, structure
    rating = Column(Integer, nullable=False)  # 1-5 scale
    sentiment = Column(String, nullable=False)  # positive, negative, neutral
    
    # Content context
    ai_output_section = Column(String, nullable=True)  # which section was rated
    original_prompt = Column(Text, nullable=True)
    ai_generated_content = Column(Text, nullable=True)
    
    # User input
    feedback_text = Column(Text, nullable=True)
    suggested_improvement = Column(Text, nullable=True)
    tags = Column(JSON, default=[])
    
    # Processing status
    processed = Column(Boolean, default=False)
    applied_to_learning = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)


class LearnedPattern(Base):
    """Patterns learned from user behavior and feedback"""
    __tablename__ = "learned_patterns"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Context
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Pattern details
    pattern_type = Column(String, nullable=False)  # content, structure, style, preference
    pattern_name = Column(String, nullable=False)
    pattern_description = Column(Text, nullable=True)
    
    # Pattern data
    pattern_data = Column(JSON, nullable=False)  # The actual pattern rules/data
    trigger_conditions = Column(JSON, default={})  # When to apply this pattern
    
    # Pattern metadata
    confidence_score = Column(Float, default=0.0)  # How confident we are in this pattern
    usage_count = Column(Integer, default=0)  # How many times it's been applied
    success_rate = Column(Float, default=0.0)  # Success rate when applied
    
    # Learning source
    source_feedback_ids = Column(JSON, default=[])  # Feedback that contributed to this pattern
    source_documents = Column(JSON, default=[])  # Documents that contributed to this pattern
    
    # Status
    is_active = Column(Boolean, default=True)
    auto_apply = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_applied_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)


class LearningSession(Base):
    """Track learning sessions and their outcomes"""
    __tablename__ = "learning_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Context
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    triggered_by_user_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Session details
    session_type = Column(String, nullable=False)  # feedback_processing, pattern_discovery, model_update
    status = Column(String, default="processing")  # processing, completed, failed
    
    # Input data
    input_data = Column(JSON, nullable=False)
    processing_parameters = Column(JSON, default={})
    
    # Results
    patterns_discovered = Column(JSON, default=[])
    patterns_updated = Column(JSON, default=[])
    patterns_removed = Column(JSON, default=[])
    
    # Metrics
    processing_time_seconds = Column(Float, nullable=True)
    confidence_improvement = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)


# Relationships
LearningProfile.feedback = relationship("AIFeedback", 
                                      primaryjoin="and_(LearningProfile.document_type_id==AIFeedback.document_type_id, "
                                                 "LearningProfile.tenant_id==AIFeedback.tenant_id)",
                                      foreign_keys=[AIFeedback.document_type_id, AIFeedback.tenant_id])

LearningProfile.patterns = relationship("LearnedPattern",
                                       primaryjoin="and_(LearningProfile.document_type_id==LearnedPattern.document_type_id, "
                                                  "LearningProfile.tenant_id==LearnedPattern.tenant_id)",
                                       foreign_keys=[LearnedPattern.document_type_id, LearnedPattern.tenant_id])
