"""
Export-related database models.
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class ExportJob(Base):
    """Export job tracking"""
    __tablename__ = "export_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    
    # Export details
    export_format = Column(String, nullable=False)  # word, pdf, markdown, etc.
    export_options = Column(JSON, default={})  # Format-specific options
    
    # Job status
    status = Column(String, default="pending")  # pending, processing, completed, failed, cancelled
    progress = Column(Integer, default=0)  # 0-100
    
    # Job metadata
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Timing
    estimated_completion = Column(DateTime(timezone=True), nullable=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Results
    result_file_id = Column(UUID(as_uuid=True), nullable=True)
    result_url = Column(String, nullable=True)  # For external integrations
    error_message = Column(Text, nullable=True)
    
    # Integration-specific data
    integration_data = Column(JSON, default={})  # Store integration-specific IDs, URLs, etc.
    
    # Relationships
    document = relationship("Document", backref="export_jobs")
    export_file = relationship("ExportFile", backref="export_job", uselist=False)


class ExportFile(Base):
    """Exported file storage metadata"""
    __tablename__ = "export_files"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    export_job_id = Column(UUID(as_uuid=True), ForeignKey("export_jobs.id"), nullable=False)
    
    # File details
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=True)  # User-friendly filename
    file_path = Column(String, nullable=False)  # Storage path
    file_size = Column(Integer, nullable=False)  # Size in bytes
    content_type = Column(String, nullable=False)  # MIME type
    file_hash = Column(String, nullable=True)  # For integrity checking
    
    # Access control
    is_public = Column(Boolean, default=False)
    download_token = Column(String, nullable=True)  # For secure downloads
    
    # Usage tracking
    download_count = Column(Integer, default=0)
    last_downloaded_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lifecycle
    expires_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class ExportTemplate(Base):
    """Custom export templates"""
    __tablename__ = "export_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    template_content = Column(Text, nullable=False)
    template_engine = Column(String, default="jinja2")  # jinja2, mustache
    output_format = Column(String, nullable=False)  # html, xml, json, etc.
    
    # Template metadata
    document_types = Column(JSON, default=[])  # Applicable document types
    variables = Column(JSON, default=[])  # Template variables schema
    sample_data = Column(JSON, default={})  # Sample data for testing
    
    # Ownership
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    is_public = Column(Boolean, default=False)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lifecycle
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ExportIntegration(Base):
    """Third-party integration configurations"""
    __tablename__ = "export_integrations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Integration details
    name = Column(String, nullable=False)  # confluence, notion, jira, etc.
    display_name = Column(String, nullable=False)
    integration_type = Column(String, nullable=False)  # publishing, ticketing, storage
    
    # Configuration
    config_schema = Column(JSON, nullable=False)  # JSON schema for configuration
    default_config = Column(JSON, default={})
    
    # Connection details
    base_url = Column(String, nullable=True)  # For self-hosted instances
    auth_type = Column(String, nullable=False)  # token, oauth, basic
    
    # Ownership and access
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Credentials (encrypted)
    credentials = Column(JSON, default={})  # Encrypted credentials
    
    # Status
    is_active = Column(Boolean, default=True)
    last_tested_at = Column(DateTime(timezone=True), nullable=True)
    test_status = Column(String, nullable=True)  # success, failed, pending
    test_error = Column(Text, nullable=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ExportHistory(Base):
    """Export history for analytics and tracking"""
    __tablename__ = "export_history"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    export_job_id = Column(UUID(as_uuid=True), ForeignKey("export_jobs.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Export details
    export_format = Column(String, nullable=False)
    export_options = Column(JSON, default={})
    
    # Results
    status = Column(String, nullable=False)  # completed, failed
    file_size = Column(Integer, nullable=True)
    processing_time = Column(Integer, nullable=True)  # Seconds
    
    # Integration results
    integration_id = Column(String, nullable=True)  # External system ID
    integration_url = Column(String, nullable=True)  # External system URL
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    document = relationship("Document", backref="export_history")
    export_job = relationship("ExportJob", backref="history_entries")


class ExportFormatSupport(Base):
    """Document type to export format support mapping"""
    __tablename__ = "export_format_support"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Mapping
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    export_format = Column(String, nullable=False)
    
    # Support details
    is_supported = Column(Boolean, default=True)
    is_recommended = Column(Boolean, default=False)
    estimated_time_minutes = Column(Integer, default=5)
    
    # Format metadata
    file_extension = Column(String, nullable=False)
    mime_type = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # Configuration
    default_options = Column(JSON, default={})
    required_options = Column(JSON, default=[])
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    document_type = relationship("DocumentType", backref="supported_export_formats")
