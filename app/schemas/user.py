from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Dict, Any
from datetime import datetime
import uuid

class UserBase(BaseModel):
    email: EmailStr
    full_name: str = Field(..., min_length=1, max_length=100)
    is_active: bool = True

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100)
    tenant_id: uuid.UUID

    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserResponse(UserBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    is_superuser: bool
    role: str
    last_login: Optional[datetime]
    preferences: Dict[str, Any]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True

# Supabase-specific schemas
class SupabaseAuthResponse(BaseModel):
    """Response from Supabase authentication operations."""
    access_token: str
    refresh_token: str
    expires_in: int
    token_type: str = "bearer"
    user: Dict[str, Any]

class SupabaseUser(BaseModel):
    """Supabase user information."""
    id: str
    email: str
    email_confirmed_at: Optional[datetime]
    phone: Optional[str]
    phone_confirmed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    user_metadata: Dict[str, Any] = {}
    app_metadata: Dict[str, Any] = {}

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: Optional[int] = None
    refresh_token: Optional[str] = None

class RefreshToken(BaseModel):
    refresh_token: str

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)

    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class MessageResponse(BaseModel):
    message: str
    details: Optional[Dict[str, Any]] = None

class UserProfileUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None

class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str

class UserPreferences(BaseModel):
    theme: str = "light"
    language: str = "en"
    notifications: Dict[str, bool] = {
        "email_notifications": True,
        "push_notifications": True,
        "marketing_emails": False
    }
    timezone: str = "UTC"

class UserPreferencesUpdate(BaseModel):
    theme: Optional[str] = None
    language: Optional[str] = None
    notifications: Optional[Dict[str, bool]] = None
    timezone: Optional[str] = None

# Additional Supabase-specific schemas
class EmailVerificationRequest(BaseModel):
    email: EmailStr

class EmailVerificationResponse(BaseModel):
    message: str
    email: str

class ResendVerificationRequest(BaseModel):
    email: EmailStr

class AuthErrorResponse(BaseModel):
    """Standardized error response for authentication operations."""
    error: str
    error_description: Optional[str] = None
    error_code: Optional[str] = None

class SessionInfo(BaseModel):
    """User session information."""
    user_id: str
    email: str
    is_authenticated: bool
    session_expires_at: Optional[datetime]
    last_activity: Optional[datetime]

class UserLoginRequest(BaseModel):
    """User login request schema."""
    email: EmailStr
    password: str = Field(..., min_length=1)

class UserRegistrationResponse(BaseModel):
    """Response after successful user registration."""
    user: UserResponse
    message: str
    email_confirmation_sent: bool = True

class PasswordChangeRequest(BaseModel):
    """Request to change user password."""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)

    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class AuthHealthCheck(BaseModel):
    """Health check response for auth services."""
    status: str
    supabase_connection: str
    database_connection: str
    timestamp: datetime