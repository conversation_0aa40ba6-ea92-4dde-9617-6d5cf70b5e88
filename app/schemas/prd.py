from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime
import uuid

class PRDFormData(BaseModel):
    question1: str = Field(..., min_length=10, max_length=1000)
    question2: str = Field(..., min_length=10, max_length=1000)
    question3: str = Field(..., min_length=10, max_length=1000)
    technical_constraints: Optional[str] = None
    competitive_analysis: Optional[str] = None
    risks_and_mitigation: Optional[str] = None
    resource_requirements: Optional[str] = None
    user_personas: Optional[str] = None
    acceptance_criteria: Optional[str] = None

class PRDCreate(BaseModel):
    form_data: PRDFormData
    title: Optional[str] = None

class PRDResponse(BaseModel):
    id: uuid.UUID
    title: str
    content: str
    form_data: Dict[str, Any]
    version: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class PRDListResponse(BaseModel):
    id: uuid.UUID
    title: str
    status: str
    created_at: datetime
    
    class Config:
        from_attributes = True