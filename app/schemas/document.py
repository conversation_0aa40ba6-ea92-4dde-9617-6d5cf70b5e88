from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid

class DocumentBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    document_type_id: uuid.UUID

class DocumentGenerate(BaseModel):
    document_type_id: uuid.UUID
    form_data: Dict[str, Any]
    title: Optional[str] = None

class DocumentCreate(DocumentBase):
    content: str
    form_data: Dict[str, Any]
    status: str = "draft"

class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    form_data: Optional[Dict[str, Any]] = None

class DocumentStatusUpdate(BaseModel):
    status: str = Field(..., pattern="^(draft|review|approved|published|archived)$")
    change_summary: Optional[str] = None

class DocumentResponse(DocumentBase):
    id: uuid.UUID
    content: str
    form_data: Dict[str, Any]
    status: str
    version: str
    version_number: int
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    shared_with: List[uuid.UUID]
    permissions: Dict[str, List[uuid.UUID]]
    view_count: int
    last_viewed_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]
    published_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class DocumentListResponse(BaseModel):
    id: uuid.UUID
    title: str
    document_type_id: uuid.UUID
    status: str
    version: str
    view_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class DocumentVersionResponse(BaseModel):
    id: uuid.UUID
    document_id: uuid.UUID
    version: str
    version_number: int
    title: str
    content: str
    form_data: Dict[str, Any]
    status: str
    change_summary: Optional[str]
    created_by: uuid.UUID
    created_at: datetime
    
    class Config:
        from_attributes = True

class DocumentDuplicateRequest(BaseModel):
    title: Optional[str] = None
    copy_permissions: bool = False

class DocumentConvertRequest(BaseModel):
    target_document_type_id: uuid.UUID
    preserve_content: bool = True
    update_form_data: Optional[Dict[str, Any]] = None

class MessageResponse(BaseModel):
    message: str

# Document Generation Workflow Schemas
class FormSubmitRequest(BaseModel):
    document_type_id: uuid.UUID
    form_data: Dict[str, Any]
    title: Optional[str] = None
    save_as_draft: bool = False

class FormValidationRequest(BaseModel):
    document_type_id: uuid.UUID
    form_data: Dict[str, Any]

class FormValidationResponse(BaseModel):
    is_valid: bool
    errors: List[Dict[str, str]] = []
    warnings: List[Dict[str, str]] = []

class FormSchemaResponse(BaseModel):
    document_type_id: uuid.UUID
    form_schema: Dict[str, Any]
    required_fields: List[str]
    optional_fields: List[str]

class FollowUpQuestion(BaseModel):
    id: str
    question: str
    type: str  # text, choice, number, etc.
    options: Optional[List[str]] = None
    required: bool = True
    context: Optional[str] = None

class FollowUpQuestionsRequest(BaseModel):
    context: Optional[str] = None

class FollowUpQuestionsResponse(BaseModel):
    questions: List[FollowUpQuestion]
    session_id: str

class FollowUpAnswersRequest(BaseModel):
    session_id: str
    answers: Dict[str, Any]

class GenerationStatus(BaseModel):
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    message: Optional[str] = None
    estimated_completion: Optional[datetime] = None
    error_details: Optional[str] = None

class RegenerateRequest(BaseModel):
    form_data: Optional[Dict[str, Any]] = None
    followup_answers: Optional[Dict[str, Any]] = None
    regeneration_instructions: Optional[str] = None
    preserve_structure: bool = True

# Document Refinement Schemas
class SectionRefinementRequest(BaseModel):
    section_name: str
    refinement_type: str  # expand, clarify, improve, restructure
    instructions: Optional[str] = None
    target_length: Optional[str] = None  # brief, standard, detailed
    tone: Optional[str] = None  # professional, casual, technical

class ComponentRefinementRequest(BaseModel):
    component_id: str
    refinement_type: str
    instructions: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

class CustomRefinementRequest(BaseModel):
    target_sections: Optional[List[str]] = None  # If None, applies to whole document
    instructions: str
    refinement_type: str = "custom"
    preserve_formatting: bool = True

class RefinementOption(BaseModel):
    id: str
    name: str
    description: str
    type: str  # section, component, global
    applicable_to: List[str]  # section names or component types
    parameters: Optional[Dict[str, Any]] = None

class RefinementOptionsResponse(BaseModel):
    available_refinements: List[RefinementOption]
    document_sections: List[str]
    document_components: List[Dict[str, str]]

class RefinementSuggestion(BaseModel):
    id: str
    type: str  # section, component, global
    target: str  # section name or component id
    suggestion: str
    description: str
    impact: str  # low, medium, high
    estimated_improvement: str

class RefinementSuggestionsResponse(BaseModel):
    suggestions: List[RefinementSuggestion]
    total_suggestions: int

class ApplySuggestionsRequest(BaseModel):
    suggestion_ids: List[str]
    custom_instructions: Optional[str] = None

class RefinementJob(BaseModel):
    job_id: uuid.UUID
    status: str  # pending, processing, completed, failed, cancelled
    progress: int  # 0-100
    refinement_type: str
    target: str
    started_at: datetime
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

class RefinementJobResponse(BaseModel):
    job: RefinementJob
    result: Optional[str] = None  # Refined content if completed


class RefinementJobListResponse(BaseModel):
    jobs: List[RefinementJob]
    total_jobs: int
    page: int
    page_size: int


class RefinementHistoryResponse(BaseModel):
    document_id: uuid.UUID
    total_refinements: int
    successful_refinements: int
    failed_refinements: int
    most_common_refinement_types: List[Dict[str, Any]]
    recent_jobs: List[RefinementJob]
    average_completion_time: Optional[float] = None  # in seconds


# Document Workflow Schemas
class WorkflowStateResponse(BaseModel):
    id: uuid.UUID
    document_id: uuid.UUID
    workflow_template_id: Optional[uuid.UUID]
    current_state: str
    previous_state: Optional[str]
    assigned_to: Optional[uuid.UUID]
    assigned_role: Optional[str]
    due_date: Optional[datetime]
    workflow_data: Dict[str, Any]
    priority: str
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class WorkflowTemplateResponse(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str]
    workflow_steps: List[Dict[str, Any]]
    default_assignments: Dict[str, Any]
    automation_rules: Dict[str, Any]
    document_types: List[uuid.UUID]
    conditions: Dict[str, Any]
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class WorkflowAssignmentResponse(BaseModel):
    id: uuid.UUID
    document_id: uuid.UUID
    workflow_state_id: uuid.UUID
    assigned_to: uuid.UUID
    assigned_by: uuid.UUID
    assignment_type: str
    task_description: Optional[str]
    instructions: Optional[str]
    due_date: Optional[datetime]
    priority: str
    status: str
    completion_notes: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True


class WorkflowCommentResponse(BaseModel):
    id: uuid.UUID
    document_id: uuid.UUID
    workflow_state_id: Optional[uuid.UUID]
    assignment_id: Optional[uuid.UUID]
    comment_text: str
    comment_type: str
    section_reference: Optional[str]
    line_reference: Optional[int]
    context_data: Dict[str, Any]
    is_resolved: bool
    resolution_notes: Optional[str]
    resolved_by: Optional[uuid.UUID]
    resolved_at: Optional[datetime]
    created_by: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# Workflow Request Schemas
class WorkflowStateUpdateRequest(BaseModel):
    new_state: str
    assigned_to: Optional[uuid.UUID] = None
    assigned_role: Optional[str] = None
    due_date: Optional[datetime] = None
    workflow_data: Optional[Dict[str, Any]] = None
    priority: Optional[str] = None
    transition_notes: Optional[str] = None


class WorkflowTemplateCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None
    workflow_steps: List[Dict[str, Any]]
    default_assignments: Optional[Dict[str, Any]] = None
    automation_rules: Optional[Dict[str, Any]] = None
    document_types: Optional[List[uuid.UUID]] = None
    conditions: Optional[Dict[str, Any]] = None
    is_default: bool = False


class WorkflowAssignmentCreateRequest(BaseModel):
    assigned_to: uuid.UUID
    assignment_type: str
    task_description: Optional[str] = None
    instructions: Optional[str] = None
    due_date: Optional[datetime] = None
    priority: str = "normal"


class WorkflowCommentCreateRequest(BaseModel):
    comment_text: str
    comment_type: str = "general"
    section_reference: Optional[str] = None
    line_reference: Optional[int] = None
    context_data: Optional[Dict[str, Any]] = None


class WorkflowAssignmentUpdateRequest(BaseModel):
    status: Optional[str] = None
    completion_notes: Optional[str] = None


class WorkflowCommentUpdateRequest(BaseModel):
    is_resolved: Optional[bool] = None
    resolution_notes: Optional[str] = None
