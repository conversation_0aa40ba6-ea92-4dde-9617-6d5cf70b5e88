"""
Learning and AI Enhancement schemas for API requests and responses.
"""
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid


# Learning Profile Schemas
class LearningProfileBase(BaseModel):
    learning_enabled: bool = True
    auto_apply_patterns: bool = False
    confidence_threshold: float = Field(default=0.7, ge=0.0, le=1.0)


class LearningProfileCreate(LearningProfileBase):
    document_type_id: uuid.UUID


class LearningProfileUpdate(BaseModel):
    learning_enabled: Optional[bool] = None
    auto_apply_patterns: Optional[bool] = None
    confidence_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)
    settings: Optional[Dict[str, Any]] = None


class LearningProfileResponse(LearningProfileBase):
    id: uuid.UUID
    document_type_id: uuid.UUID
    tenant_id: uuid.UUID
    total_documents_processed: int
    total_feedback_received: int
    positive_feedback_count: int
    negative_feedback_count: int
    learned_patterns: List[Dict[str, Any]]
    user_preferences: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    settings: Dict[str, Any]
    created_at: datetime
    updated_at: Optional[datetime]
    last_learning_update: Optional[datetime]

    class Config:
        from_attributes = True


# AI Feedback Schemas
class AIFeedbackCreate(BaseModel):
    document_id: Optional[uuid.UUID] = None
    document_type_id: uuid.UUID
    feedback_type: str = Field(..., pattern="^(quality|accuracy|relevance|style|structure)$")
    rating: int = Field(..., ge=1, le=5)
    sentiment: str = Field(..., pattern="^(positive|negative|neutral)$")
    ai_output_section: Optional[str] = None
    original_prompt: Optional[str] = None
    ai_generated_content: Optional[str] = None
    feedback_text: Optional[str] = None
    suggested_improvement: Optional[str] = None
    tags: List[str] = []


class AIFeedbackResponse(BaseModel):
    id: uuid.UUID
    document_id: Optional[uuid.UUID]
    document_type_id: uuid.UUID
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    feedback_type: str
    rating: int
    sentiment: str
    ai_output_section: Optional[str]
    feedback_text: Optional[str]
    suggested_improvement: Optional[str]
    tags: List[str]
    processed: bool
    applied_to_learning: bool
    created_at: datetime
    processed_at: Optional[datetime]

    class Config:
        from_attributes = True


# Learned Pattern Schemas
class LearnedPatternCreate(BaseModel):
    document_type_id: uuid.UUID
    pattern_type: str = Field(..., pattern="^(content|structure|style|preference)$")
    pattern_name: str = Field(..., min_length=1, max_length=100)
    pattern_description: Optional[str] = None
    pattern_data: Dict[str, Any]
    trigger_conditions: Dict[str, Any] = {}
    auto_apply: bool = False


class LearnedPatternUpdate(BaseModel):
    pattern_name: Optional[str] = Field(None, min_length=1, max_length=100)
    pattern_description: Optional[str] = None
    pattern_data: Optional[Dict[str, Any]] = None
    trigger_conditions: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    auto_apply: Optional[bool] = None


class LearnedPatternResponse(BaseModel):
    id: uuid.UUID
    document_type_id: uuid.UUID
    tenant_id: uuid.UUID
    pattern_type: str
    pattern_name: str
    pattern_description: Optional[str]
    pattern_data: Dict[str, Any]
    trigger_conditions: Dict[str, Any]
    confidence_score: float
    usage_count: int
    success_rate: float
    is_active: bool
    auto_apply: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_applied_at: Optional[datetime]
    expires_at: Optional[datetime]

    class Config:
        from_attributes = True


# Learning Settings Schemas
class LearningSettingsUpdate(BaseModel):
    pattern_detection_sensitivity: Optional[str] = Field(None, pattern="^(low|medium|high)$")
    feedback_weight: Optional[float] = Field(None, ge=0.1, le=2.0)
    pattern_expiry_days: Optional[int] = Field(None, ge=1, le=365)
    max_patterns_stored: Optional[int] = Field(None, ge=10, le=1000)


class LearningSettingsResponse(BaseModel):
    pattern_detection_sensitivity: str
    feedback_weight: float
    pattern_expiry_days: int
    max_patterns_stored: int


# Learning Suggestions Schemas
class LearningSuggestion(BaseModel):
    suggestion_id: str
    suggestion_type: str  # pattern_application, content_improvement, structure_optimization
    title: str
    description: str
    confidence_score: float
    applicable_sections: List[str] = []
    preview_changes: Optional[Dict[str, Any]] = None
    pattern_id: Optional[uuid.UUID] = None


class LearningSuggestionsResponse(BaseModel):
    suggestions: List[LearningSuggestion]
    total_suggestions: int
    document_id: uuid.UUID
    document_type_id: uuid.UUID
    generated_at: datetime


# Reset Learning Data Schema
class ResetLearningRequest(BaseModel):
    reset_patterns: bool = True
    reset_feedback: bool = False
    reset_profile: bool = False
    confirmation: str = Field(..., pattern="^RESET$")


class ResetLearningResponse(BaseModel):
    message: str
    patterns_removed: int
    feedback_removed: int
    profile_reset: bool
    reset_at: datetime


# General Response Schemas
class MessageResponse(BaseModel):
    message: str


class LearningStatsResponse(BaseModel):
    total_patterns: int
    active_patterns: int
    total_feedback: int
    positive_feedback_percentage: float
    learning_accuracy: float
    last_learning_update: Optional[datetime]


# Learning Session Schemas
class LearningSessionCreate(BaseModel):
    document_type_id: uuid.UUID
    session_type: str = Field(..., pattern="^(feedback_processing|pattern_discovery|model_update|manual_training)$")
    input_data: Dict[str, Any]
    processing_parameters: Dict[str, Any] = {}


class LearningSessionUpdate(BaseModel):
    status: Optional[str] = Field(None, pattern="^(processing|completed|failed|cancelled)$")
    processing_parameters: Optional[Dict[str, Any]] = None
    patterns_discovered: Optional[List[str]] = None
    patterns_updated: Optional[List[str]] = None
    patterns_removed: Optional[List[str]] = None
    error_message: Optional[str] = None


class LearningSessionResponse(BaseModel):
    id: uuid.UUID
    document_type_id: uuid.UUID
    tenant_id: uuid.UUID
    triggered_by_user_id: Optional[uuid.UUID]
    session_type: str
    status: str
    input_data: Dict[str, Any]
    processing_parameters: Dict[str, Any]
    patterns_discovered: List[str]
    patterns_updated: List[str]
    patterns_removed: List[str]
    processing_time_seconds: Optional[float]
    confidence_improvement: float
    error_message: Optional[str]
    started_at: datetime
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True


# Learning Analytics Schemas
class PatternEffectivenessMetrics(BaseModel):
    pattern_id: uuid.UUID
    pattern_name: str
    pattern_type: str
    usage_count: int
    success_rate: float
    confidence_score: float
    avg_feedback_rating: float
    last_applied: Optional[datetime]


class FeedbackTrendData(BaseModel):
    date: str
    positive_count: int
    negative_count: int
    neutral_count: int
    avg_rating: float


class LearningProgressMetrics(BaseModel):
    total_patterns: int
    active_patterns: int
    patterns_this_month: int
    total_feedback: int
    feedback_this_month: int
    avg_pattern_confidence: float
    learning_accuracy_trend: List[float]
    top_performing_patterns: List[PatternEffectivenessMetrics]


class LearningAnalyticsResponse(BaseModel):
    document_type_id: uuid.UUID
    tenant_id: uuid.UUID
    progress_metrics: LearningProgressMetrics
    feedback_trends: List[FeedbackTrendData]
    pattern_effectiveness: List[PatternEffectivenessMetrics]
    generated_at: datetime


# Export/Import Schemas
class LearningDataExport(BaseModel):
    export_id: str
    document_type_id: uuid.UUID
    tenant_id: uuid.UUID
    export_timestamp: datetime
    learning_profile: Optional[Dict[str, Any]]
    patterns: List[Dict[str, Any]]
    feedback: List[Dict[str, Any]]
    sessions: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class LearningDataImportRequest(BaseModel):
    import_data: LearningDataExport
    import_options: Dict[str, Any] = {
        "overwrite_existing": False,
        "merge_patterns": True,
        "import_feedback": False,
        "import_sessions": False
    }


class LearningDataImportResponse(BaseModel):
    import_id: str
    status: str
    imported_patterns: int
    imported_feedback: int
    imported_sessions: int
    skipped_items: int
    errors: List[str]
    imported_at: datetime
