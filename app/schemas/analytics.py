"""
Analytics and Insights API Schemas
Request/response models for analytics endpoints and dashboard data.
"""
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, date
import uuid

from app.models.analytics import MetricType, AggregationPeriod


# Base schemas
class DateRangeFilter(BaseModel):
    start_date: date
    end_date: date
    period: Optional[AggregationPeriod] = AggregationPeriod.DAILY


class MetricValue(BaseModel):
    """Single metric value with metadata"""
    value: Union[int, float]
    change: Optional[float] = None  # Percentage change from previous period
    trend: Optional[str] = None  # "up", "down", "stable"
    metadata: Optional[Dict[str, Any]] = None


class ChartDataPoint(BaseModel):
    """Single data point for charts"""
    date: date
    value: Union[int, float]
    label: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ChartData(BaseModel):
    """Chart data with multiple series"""
    title: str
    type: str  # "line", "bar", "pie", "area"
    series: List[Dict[str, Any]]
    labels: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


# Dashboard schemas
class DashboardOverview(BaseModel):
    """Main dashboard overview metrics"""
    total_documents: MetricValue
    documents_this_month: MetricValue
    active_users: MetricValue
    ai_generations: MetricValue
    completion_rate: MetricValue
    avg_generation_time: MetricValue


class DashboardCharts(BaseModel):
    """Dashboard chart data"""
    document_creation_trend: ChartData
    document_type_distribution: ChartData
    user_activity: ChartData
    ai_performance: ChartData
    export_usage: ChartData


class DashboardResponse(BaseModel):
    """Complete dashboard data"""
    overview: DashboardOverview
    charts: DashboardCharts
    recent_activity: List[Dict[str, Any]]
    insights: List[str]
    last_updated: datetime


# Document analytics schemas
class DocumentMetrics(BaseModel):
    """Document-specific metrics"""
    document_id: uuid.UUID
    document_title: str
    document_type: str
    
    # Generation metrics
    generation_time_seconds: Optional[float]
    tokens_used: Optional[int]
    ai_model_used: Optional[str]
    
    # Content metrics
    content_length: Optional[int]
    word_count: Optional[int]
    section_count: Optional[int]
    
    # Engagement metrics
    view_count: int
    edit_count: int
    share_count: int
    refinement_count: int
    export_count: int
    
    # Quality metrics
    avg_rating: Optional[float]
    rating_count: int
    
    # Timestamps
    created_at: datetime
    last_activity_at: Optional[datetime]


class DocumentAnalyticsResponse(BaseModel):
    """Document analytics response"""
    document: DocumentMetrics
    performance_comparison: Dict[str, MetricValue]
    activity_timeline: List[ChartDataPoint]
    refinement_history: List[Dict[str, Any]]
    export_history: List[Dict[str, Any]]


# Document type analytics schemas
class DocumentTypeMetrics(BaseModel):
    """Document type performance metrics"""
    document_type_id: uuid.UUID
    document_type_name: str
    
    # Volume metrics
    total_documents: int
    documents_this_period: int
    avg_documents_per_user: float
    
    # Performance metrics
    avg_generation_time: Optional[float]
    avg_completion_time: Optional[float]
    success_rate: float
    
    # Quality metrics
    avg_rating: Optional[float]
    refinement_rate: float
    
    # Usage metrics
    active_users: int
    total_exports: int
    
    # Trends
    growth_rate: Optional[float]
    performance_trend: Optional[str]


class DocumentTypeAnalyticsResponse(BaseModel):
    """Document type analytics response"""
    metrics: DocumentTypeMetrics
    usage_trends: ChartData
    performance_comparison: Dict[str, MetricValue]
    top_performing_documents: List[DocumentMetrics]
    common_refinements: List[Dict[str, Any]]


# Team performance schemas
class TeamMemberMetrics(BaseModel):
    """Individual team member metrics"""
    user_id: uuid.UUID
    user_name: str
    documents_created: int
    avg_completion_time: Optional[float]
    collaboration_score: float
    ai_usage_rate: float
    last_active: datetime


class TeamPerformanceResponse(BaseModel):
    """Team performance analytics"""
    period_start: date
    period_end: date
    
    # Team overview
    total_members: int
    active_members: int
    new_members: int
    
    # Productivity metrics
    total_documents: int
    avg_documents_per_member: float
    completion_rate: float
    
    # Collaboration metrics
    collaboration_score: float
    documents_shared: int
    comments_made: int
    
    # Performance trends
    productivity_trend: ChartData
    collaboration_trend: ChartData
    
    # Individual performance
    top_performers: List[TeamMemberMetrics]
    team_members: List[TeamMemberMetrics]


# AI effectiveness schemas
class AIModelMetrics(BaseModel):
    """AI model performance metrics"""
    model_name: str
    total_generations: int
    success_rate: float
    avg_generation_time: float
    avg_tokens_used: float
    user_satisfaction: float


class AIEffectivenessResponse(BaseModel):
    """AI effectiveness analytics"""
    document_type: str
    period_start: date
    period_end: date
    
    # Overall metrics
    total_generations: int
    success_rate: float
    avg_user_rating: float
    refinement_rate: float
    
    # Performance trends
    performance_trend: ChartData
    satisfaction_trend: ChartData
    
    # Model comparison
    model_performance: List[AIModelMetrics]
    
    # Quality insights
    common_issues: List[str]
    improvement_suggestions: List[str]
    
    # Token usage
    token_usage_trend: ChartData
    cost_analysis: Dict[str, Any]


# Export usage schemas
class ExportFormatMetrics(BaseModel):
    """Export format usage metrics"""
    format_name: str
    total_exports: int
    success_rate: float
    avg_file_size_mb: float
    avg_export_time: float


class ExportUsageResponse(BaseModel):
    """Export usage analytics"""
    period_start: date
    period_end: date
    
    # Overall metrics
    total_exports: int
    success_rate: float
    most_popular_format: str
    
    # Format breakdown
    format_metrics: List[ExportFormatMetrics]
    format_distribution: ChartData
    
    # Usage patterns
    usage_by_time: ChartData
    usage_by_document_type: ChartData
    
    # Performance metrics
    performance_trends: ChartData
    integration_usage: Dict[str, int]


# Refinement patterns schemas
class RefinementPattern(BaseModel):
    """Common refinement pattern"""
    pattern_type: str
    frequency: int
    avg_improvement_score: float
    common_triggers: List[str]
    success_rate: float


class RefinementPatternsResponse(BaseModel):
    """Refinement patterns analytics"""
    document_type: str
    period_start: date
    period_end: date
    
    # Overall metrics
    total_refinements: int
    avg_refinements_per_doc: float
    refinement_success_rate: float
    
    # Pattern analysis
    common_patterns: List[RefinementPattern]
    refinement_trends: ChartData
    
    # Performance impact
    before_after_metrics: Dict[str, MetricValue]
    user_satisfaction_impact: float
    
    # Recommendations
    optimization_suggestions: List[str]


# Form completion schemas
class FormFieldMetrics(BaseModel):
    """Form field completion metrics"""
    field_name: str
    completion_rate: float
    avg_time_spent: float
    error_rate: float
    abandonment_rate: float


class FormCompletionResponse(BaseModel):
    """Form completion analytics"""
    document_type: str
    period_start: date
    period_end: date
    
    # Overall metrics
    total_starts: int
    total_completions: int
    completion_rate: float
    avg_completion_time: float
    
    # Field-level analysis
    field_metrics: List[FormFieldMetrics]
    completion_funnel: ChartData
    
    # User behavior
    bounce_rate: float
    return_rate: float
    device_breakdown: Dict[str, float]
    
    # Optimization insights
    bottleneck_fields: List[str]
    improvement_opportunities: List[str]


# Common response schemas
class AnalyticsInsight(BaseModel):
    """Analytics insight or recommendation"""
    title: str
    description: str
    impact: str  # "high", "medium", "low"
    action_items: List[str]
    confidence: float


class MessageResponse(BaseModel):
    """Generic message response"""
    message: str
    details: Optional[Dict[str, Any]] = None
