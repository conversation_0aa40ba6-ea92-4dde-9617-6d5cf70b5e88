from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid

class TenantBase(BaseModel):
    name: str
    slug: str
    plan: str = "free"
    theme: str = "default"

class TenantCreate(TenantBase):
    pass

class TenantResponse(TenantBase):
    id: uuid.UUID
    settings: Dict[str, Any]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True

class TenantUpdate(BaseModel):
    name: Optional[str] = None
    slug: Optional[str] = None
    plan: Optional[str] = None
    theme: Optional[str] = None

class TenantSettingsUpdate(BaseModel):
    settings: Dict[str, Any]

class TenantMember(BaseModel):
    id: uuid.UUID
    email: EmailStr
    full_name: str
    role: str = "member"  # admin, member, viewer
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True

class TenantMemberInvite(BaseModel):
    email: EmailStr
    role: str = "member"
    message: Optional[str] = None

class TenantMemberRoleUpdate(BaseModel):
    role: str

class TenantUsageStats(BaseModel):
    total_users: int
    active_users: int
    total_prds: int
    prds_this_month: int
    storage_used_mb: float
    api_calls_this_month: int
    plan_limits: Dict[str, Any]

class MessageResponse(BaseModel):
    message: str