"""
Export-related Pydantic schemas for API requests and responses.
"""
from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from enum import Enum
import uuid


class ExportFormat(str, Enum):
    """Supported export formats"""
    WORD = "word"
    PDF = "pdf"
    MARKDOWN = "markdown"
    CONFLUENCE = "confluence"
    NOTION = "notion"
    JIRA = "jira"
    CUSTOM = "custom"
    OPENAPI = "openapi"
    SWAGGER = "swagger"
    PLANTUML = "plantuml"
    WIREFRAMES = "wireframes"


class ExportStatus(str, Enum):
    """Export job status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DocumentType(str, Enum):
    """Document types for format discovery"""
    PRD = "prd"
    API_DOC = "api_doc"
    ARCHITECTURE = "architecture"
    DESIGN = "design"
    GENERAL = "general"


# Base Export Request
class ExportRequest(BaseModel):
    """Base export request schema"""
    options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Format-specific export options")
    template_id: Optional[str] = Field(None, description="Custom template ID for export")
    include_metadata: bool = Field(True, description="Include document metadata in export")
    
    
# Format-specific Export Requests
class WordExportRequest(ExportRequest):
    """Word document export options"""
    include_toc: bool = Field(True, description="Include table of contents")
    page_orientation: str = Field("portrait", pattern="^(portrait|landscape)$")
    font_family: str = Field("Calibri", description="Font family for the document")
    font_size: int = Field(11, ge=8, le=72, description="Base font size")


class PDFExportRequest(ExportRequest):
    """PDF export options"""
    include_toc: bool = Field(True, description="Include table of contents")
    page_size: str = Field("A4", description="Page size (A4, Letter, etc.)")
    margin_size: str = Field("normal", pattern="^(narrow|normal|wide)$")
    include_headers_footers: bool = Field(True, description="Include headers and footers")


class MarkdownExportRequest(ExportRequest):
    """Markdown export options"""
    include_frontmatter: bool = Field(True, description="Include YAML frontmatter")
    heading_style: str = Field("atx", pattern="^(atx|setext)$", description="Heading style (# or underline)")
    code_block_style: str = Field("fenced", pattern="^(fenced|indented)$")


class ConfluenceExportRequest(ExportRequest):
    """Confluence integration options"""
    space_key: str = Field(..., description="Confluence space key")
    parent_page_id: Optional[str] = Field(None, description="Parent page ID")
    page_title: Optional[str] = Field(None, description="Override page title")
    update_existing: bool = Field(False, description="Update existing page if found")
    confluence_url: HttpUrl = Field(..., description="Confluence instance URL")
    auth_token: str = Field(..., description="Confluence API token")


class NotionExportRequest(ExportRequest):
    """Notion integration options"""
    database_id: Optional[str] = Field(None, description="Notion database ID")
    parent_page_id: Optional[str] = Field(None, description="Parent page ID")
    page_title: Optional[str] = Field(None, description="Override page title")
    notion_token: str = Field(..., description="Notion integration token")


class JiraExportRequest(ExportRequest):
    """JIRA integration options"""
    project_key: str = Field(..., description="JIRA project key")
    issue_type: str = Field("Epic", description="JIRA issue type")
    create_subtasks: bool = Field(True, description="Create subtasks from document sections")
    jira_url: HttpUrl = Field(..., description="JIRA instance URL")
    auth_token: str = Field(..., description="JIRA API token")
    assignee: Optional[str] = Field(None, description="Default assignee for created issues")


class CustomExportRequest(ExportRequest):
    """Custom template export options"""
    template_content: str = Field(..., description="Custom template content")
    template_engine: str = Field("jinja2", pattern="^(jinja2|mustache)$")
    output_format: str = Field("html", description="Output format extension")


# Document-type specific exports
class OpenAPIExportRequest(ExportRequest):
    """OpenAPI specification export options"""
    openapi_version: str = Field("3.0.3", description="OpenAPI specification version")
    include_examples: bool = Field(True, description="Include request/response examples")
    server_urls: List[str] = Field(default_factory=list, description="Server URLs to include")


class SwaggerExportRequest(ExportRequest):
    """Swagger documentation export options"""
    swagger_ui_version: str = Field("latest", description="Swagger UI version")
    theme: str = Field("default", description="Swagger UI theme")
    include_try_it_out: bool = Field(True, description="Enable try-it-out functionality")


class PlantUMLExportRequest(ExportRequest):
    """PlantUML diagram export options"""
    diagram_types: List[str] = Field(default_factory=lambda: ["sequence", "class", "component"], 
                                   description="Types of diagrams to generate")
    output_format: str = Field("png", pattern="^(png|svg|pdf)$")
    include_source: bool = Field(True, description="Include PlantUML source code")


class WireframesExportRequest(ExportRequest):
    """Wireframes export options"""
    wireframe_tool: str = Field("figma", pattern="^(figma|sketch|balsamiq)$")
    fidelity: str = Field("medium", pattern="^(low|medium|high)$")
    include_annotations: bool = Field(True, description="Include design annotations")


# Export Response Schemas
class ExportJobResponse(BaseModel):
    """Export job response"""
    job_id: uuid.UUID = Field(..., description="Unique job identifier")
    document_id: uuid.UUID = Field(..., description="Source document ID")
    export_format: ExportFormat = Field(..., description="Export format")
    status: ExportStatus = Field(..., description="Current job status")
    progress: int = Field(0, ge=0, le=100, description="Progress percentage")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    created_at: datetime = Field(..., description="Job creation timestamp")
    started_at: Optional[datetime] = Field(None, description="Job start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Job completion timestamp")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    result_url: Optional[str] = Field(None, description="Download URL when completed")
    
    class Config:
        from_attributes = True


class ExportHistoryItem(BaseModel):
    """Export history item"""
    job_id: uuid.UUID
    export_format: ExportFormat
    status: ExportStatus
    file_size: Optional[int] = Field(None, description="File size in bytes")
    download_count: int = Field(0, description="Number of times downloaded")
    created_at: datetime
    expires_at: Optional[datetime] = Field(None, description="File expiration time")
    
    class Config:
        from_attributes = True


class ExportHistoryResponse(BaseModel):
    """Export history response"""
    document_id: uuid.UUID
    exports: List[ExportHistoryItem]
    total_count: int
    
    
class ExportFormatInfo(BaseModel):
    """Export format information"""
    format: ExportFormat
    name: str
    description: str
    file_extension: str
    mime_type: str
    supports_options: bool
    estimated_time: str  # e.g., "2-5 minutes"
    

class AvailableFormatsResponse(BaseModel):
    """Available export formats for a document type"""
    document_type: DocumentType
    formats: List[ExportFormatInfo]
    

class ExportDownloadResponse(BaseModel):
    """Export download response"""
    file_id: uuid.UUID
    filename: str
    content_type: str
    file_size: int
    download_url: str
    expires_at: datetime


# Generic response schemas
class MessageResponse(BaseModel):
    """Generic message response"""
    message: str
    details: Optional[Dict[str, Any]] = None
