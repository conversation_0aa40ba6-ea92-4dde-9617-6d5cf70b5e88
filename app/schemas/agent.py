"""
AI Agent and Prompt Management Schemas
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid

from app.models.agent import AgentStatus, AgentType, JobStatus


# Base schemas
class AgentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    doc_type: str = Field(..., min_length=1, max_length=50)


class PromptBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    doc_type: str = Field(..., min_length=1, max_length=50)


# Agent schemas
class AgentCreate(AgentBase):
    agent_type: AgentType = AgentType.CUSTOM
    model_name: str = Field(..., min_length=1, max_length=100)
    system_prompt: str = Field(..., min_length=1)
    temperature: int = Field(30, ge=0, le=100)
    max_tokens: int = Field(4000, ge=100, le=8000)
    capabilities: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0 <= v <= 100:
            raise ValueError('Temperature must be between 0 and 100')
        return v


class AgentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    model_name: Optional[str] = Field(None, min_length=1, max_length=100)
    system_prompt: Optional[str] = Field(None, min_length=1)
    temperature: Optional[int] = Field(None, ge=0, le=100)
    max_tokens: Optional[int] = Field(None, ge=100, le=8000)
    capabilities: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    status: Optional[AgentStatus] = None


class AgentResponse(AgentBase):
    id: uuid.UUID
    agent_type: AgentType
    model_name: str
    system_prompt: str
    temperature: int
    max_tokens: int
    capabilities: Dict[str, Any]
    parameters: Dict[str, Any]
    status: AgentStatus
    is_system_default: bool
    version: str
    usage_count: int
    tenant_id: Optional[uuid.UUID]
    created_by: Optional[uuid.UUID]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class AgentListItem(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str]
    doc_type: str
    agent_type: AgentType
    model_name: str
    status: AgentStatus
    is_system_default: bool
    usage_count: int
    created_at: datetime
    
    class Config:
        from_attributes = True


# Prompt schemas
class PromptCreate(PromptBase):
    prompt_template: str = Field(..., min_length=1)
    variables: List[str] = []
    prompt_type: str = Field(..., min_length=1, max_length=50)
    category: Optional[str] = Field(None, max_length=50)
    agent_id: Optional[uuid.UUID] = None


class PromptUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    prompt_template: Optional[str] = Field(None, min_length=1)
    variables: Optional[List[str]] = None
    prompt_type: Optional[str] = Field(None, min_length=1, max_length=50)
    category: Optional[str] = Field(None, max_length=50)
    agent_id: Optional[uuid.UUID] = None
    is_active: Optional[bool] = None


class PromptResponse(PromptBase):
    id: uuid.UUID
    prompt_template: str
    variables: List[str]
    prompt_type: str
    category: Optional[str]
    agent_id: Optional[uuid.UUID]
    is_active: bool
    is_system_default: bool
    version: str
    usage_count: int
    tenant_id: Optional[uuid.UUID]
    created_by: Optional[uuid.UUID]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class PromptListItem(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str]
    doc_type: str
    prompt_type: str
    category: Optional[str]
    is_active: bool
    is_system_default: bool
    usage_count: int
    created_at: datetime
    
    class Config:
        from_attributes = True


# Agent execution schemas
class AgentExecuteRequest(BaseModel):
    input_data: Dict[str, Any]
    document_id: Optional[uuid.UUID] = None
    job_type: str = Field("execute", max_length=50)


class BatchExecuteRequest(BaseModel):
    execution_requests: List[Dict[str, Any]] = Field(..., min_items=1, max_items=10)

    class Config:
        schema_extra = {
            "example": {
                "execution_requests": [
                    {
                        "agent_id": "123e4567-e89b-12d3-a456-************",
                        "doc_type": "prd",
                        "request": {
                            "input_data": {"prompt": "Generate a PRD for mobile app"},
                            "job_type": "generate"
                        }
                    }
                ]
            }
        }


class AgentJobResponse(BaseModel):
    id: uuid.UUID
    agent_id: uuid.UUID
    job_type: str
    status: JobStatus
    progress_percentage: int
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    execution_time_ms: Optional[int]
    tokens_used: Optional[int]
    model_used: Optional[str]
    document_id: Optional[uuid.UUID]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class BatchExecuteResponse(BaseModel):
    jobs: List[AgentJobResponse]
    total_jobs: int
    successful_jobs: int
    failed_jobs: int


class JobListResponse(BaseModel):
    jobs: List[AgentJobResponse]
    total: int
    limit: int
    offset: int


class ExecutionMetricsResponse(BaseModel):
    total_jobs: int
    completed_jobs: int
    failed_jobs: int
    running_jobs: int
    success_rate: float
    average_execution_time_ms: float
    period_days: int


class AgentCapabilitiesResponse(BaseModel):
    doc_type: str
    available_agents: List[AgentListItem]
    capabilities: Dict[str, Any]
    supported_operations: List[str]


# Common response schemas
class MessageResponse(BaseModel):
    message: str


class AgentListResponse(BaseModel):
    agents: List[AgentListItem]
    total: int


class PromptListResponse(BaseModel):
    prompts: List[PromptListItem]
    total: int
