from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid

class DocumentTypeBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    slug: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = None
    category: str = Field(..., min_length=1, max_length=50)
    industry: Optional[str] = None

class DocumentTypeCreate(DocumentTypeBase):
    form_schema: Dict[str, Any]
    template_structure: Dict[str, Any]
    ai_agents: Optional[Dict[str, Any]] = None
    refinement_options: Optional[Dict[str, Any]] = None

class DocumentTypeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    industry: Optional[str] = None
    form_schema: Optional[Dict[str, Any]] = None
    template_structure: Optional[Dict[str, Any]] = None
    ai_agents: Optional[Dict[str, Any]] = None
    refinement_options: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class DocumentTypeResponse(DocumentTypeBase):
    id: uuid.UUID
    form_schema: Dict[str, Any]
    template_structure: Dict[str, Any]
    ai_agents: Dict[str, Any]
    refinement_options: Dict[str, Any]
    is_system_default: bool
    is_active: bool
    version: str
    usage_count: int
    tenant_id: Optional[uuid.UUID]
    created_by: Optional[uuid.UUID]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class DocumentTypeListResponse(BaseModel):
    id: uuid.UUID
    name: str
    slug: str
    description: Optional[str]
    category: str
    industry: Optional[str]
    is_system_default: bool
    is_active: bool
    usage_count: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class FormSchemaResponse(BaseModel):
    form_schema: Dict[str, Any]

class TemplateStructureResponse(BaseModel):
    template_structure: Dict[str, Any]

class AIAgentsResponse(BaseModel):
    ai_agents: Dict[str, Any]

class RefinementOptionsResponse(BaseModel):
    refinement_options: Dict[str, Any]

class MessageResponse(BaseModel):
    message: str
