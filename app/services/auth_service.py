"""
Authentication service using Supabase Auth.

This service provides a comprehensive authentication layer that integrates
Supabase Auth with the application's user management system.
"""

import logging
from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
import uuid

from gotrue.errors import AuthError
from supabase import Client
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.core.supabase import get_supabase, get_supabase_service, get_auth_client
from app.core.config import settings
from app.core.security import create_access_token, verify_password, get_password_hash
from app.core.auth_exceptions import (
    handle_supabase_auth_error,
    log_auth_event,
    validate_auth_input,
    auth_metrics
)
from app.core.auth_logging import auth_audit_log, AuthLogContext
from app.models.user import User
from app.schemas.user import UserCreate, UserResponse

logger = logging.getLogger(__name__)


class AuthService:
    """
    Comprehensive authentication service using Supabase Auth.

    This service handles all authentication operations including:
    - User registration and login
    - Password reset and email verification
    - Session management
    - User profile management
    """

    def __init__(self):
        self.supabase = get_supabase()
        self.service_client = get_supabase_service()
        self.auth_client = get_auth_client()

    @auth_audit_log
    async def register_user(
        self,
        user_data: UserCreate,
        db: Session,
        ip_address: Optional[str] = None
    ) -> Tuple[UserResponse, Dict[str, Any]]:
        """
        Register a new user with Supabase Auth and local database.

        Args:
            user_data: User registration data
            db: Database session
            ip_address: Client IP address for logging

        Returns:
            Tuple of UserResponse and auth session data

        Raises:
            HTTPException: If registration fails
        """
        # Validate input
        validate_auth_input(email=user_data.email, password=user_data.password)

        with AuthLogContext("user_registration", email=user_data.email, ip_address=ip_address):
            try:
                # Check if user already exists in local database
                existing_user = db.query(User).filter(User.email == user_data.email).first()
                if existing_user:
                    log_auth_event(
                        "registration_attempt",
                        email=user_data.email,
                        success=False,
                        details={"reason": "email_already_exists"}
                    )
                    auth_metrics.increment("registration_failures")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email already registered"
                    )

                # Register user with Supabase Auth
                auth_response = self.auth_client.sign_up({
                    "email": user_data.email,
                    "password": user_data.password,
                    "options": {
                        "data": {
                            "full_name": user_data.full_name,
                            "tenant_id": str(user_data.tenant_id)
                        }
                    }
                })

                if not auth_response.user:
                    log_auth_event(
                        "registration_attempt",
                        email=user_data.email,
                        success=False,
                        details={"reason": "supabase_user_creation_failed"}
                    )
                    auth_metrics.increment("registration_failures")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Failed to create user account"
                    )

                # Create user in local database
                db_user = User(
                    id=uuid.UUID(auth_response.user.id),
                    email=user_data.email,
                    full_name=user_data.full_name,
                    hashed_password=get_password_hash(user_data.password),
                    tenant_id=user_data.tenant_id,
                    is_active=True,
                    is_superuser=False
                )

                db.add(db_user)
                db.commit()
                db.refresh(db_user)

                # Log successful registration
                log_auth_event(
                    "user_registered",
                    email=user_data.email,
                    user_id=str(db_user.id),
                    success=True,
                    details={"tenant_id": str(user_data.tenant_id)}
                )
                auth_metrics.increment("registrations")

                logger.info(f"User registered successfully: {user_data.email}")

                return UserResponse.from_orm(db_user), {
                    "session": auth_response.session,
                    "user": auth_response.user
                }

            except AuthError as e:
                logger.error(f"Supabase auth error during registration: {str(e)}")
                log_auth_event(
                    "registration_attempt",
                    email=user_data.email,
                    success=False,
                    details={"supabase_error": str(e)}
                )
                auth_metrics.increment("registration_failures")
                raise handle_supabase_auth_error(e)
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Unexpected error during registration: {str(e)}")
                log_auth_event(
                    "registration_attempt",
                    email=user_data.email,
                    success=False,
                    details={"unexpected_error": str(e)}
                )
                auth_metrics.increment("registration_failures")
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Registration failed due to server error"
                )

    async def authenticate_user(
        self,
        email: str,
        password: str,
        db: Session
    ) -> Tuple[UserResponse, Dict[str, Any]]:
        """
        Authenticate user with Supabase Auth.

        Args:
            email: User email
            password: User password
            db: Database session

        Returns:
            Tuple of UserResponse and auth session data

        Raises:
            HTTPException: If authentication fails
        """
        try:
            # Authenticate with Supabase
            auth_response = self.auth_client.sign_in_with_password({
                "email": email,
                "password": password
            })

            if not auth_response.user or not auth_response.session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )

            # Get user from local database
            user = db.query(User).filter(User.email == email).first()
            if not user:
                # Create user in local database if doesn't exist
                user = User(
                    id=uuid.UUID(auth_response.user.id),
                    email=email,
                    full_name=auth_response.user.user_metadata.get('full_name', ''),
                    hashed_password=get_password_hash(password),
                    tenant_id=uuid.UUID(auth_response.user.user_metadata.get('tenant_id')),
                    is_active=True,
                    is_superuser=False
                )
                db.add(user)
                db.commit()
                db.refresh(user)

            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Inactive user account"
                )

            # Update last login
            user.last_login = datetime.utcnow()
            db.commit()

            logger.info(f"User authenticated successfully: {email}")

            return UserResponse.from_orm(user), {
                "session": auth_response.session,
                "user": auth_response.user
            }

        except AuthError as e:
            logger.error(f"Supabase auth error during login: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        except Exception as e:
            logger.error(f"Unexpected error during authentication: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication failed due to server error"
            )

    async def logout_user(self, access_token: str) -> Dict[str, str]:
        """
        Logout user by invalidating their session.

        Args:
            access_token: User's access token

        Returns:
            Success message

        Raises:
            HTTPException: If logout fails
        """
        try:
            # Set the session for the auth client
            self.auth_client.set_session(access_token, "")

            # Sign out from Supabase
            self.auth_client.sign_out()

            logger.info("User logged out successfully")
            return {"message": "Successfully logged out"}

        except AuthError as e:
            logger.error(f"Supabase auth error during logout: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Logout failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during logout: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Logout failed due to server error"
            )

    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        Refresh user's access token.

        Args:
            refresh_token: User's refresh token

        Returns:
            New session data

        Raises:
            HTTPException: If token refresh fails
        """
        try:
            auth_response = self.auth_client.refresh_session(refresh_token)

            if not auth_response.session:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )

            logger.info("Token refreshed successfully")
            return {
                "session": auth_response.session,
                "user": auth_response.user
            }

        except AuthError as e:
            logger.error(f"Supabase auth error during token refresh: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        except Exception as e:
            logger.error(f"Unexpected error during token refresh: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Token refresh failed due to server error"
            )

    async def request_password_reset(self, email: str) -> Dict[str, str]:
        """
        Request password reset for user.

        Args:
            email: User's email address

        Returns:
            Success message

        Raises:
            HTTPException: If password reset request fails
        """
        try:
            # Request password reset from Supabase
            self.auth_client.reset_password_email(email)

            logger.info(f"Password reset requested for: {email}")
            return {"message": "If the email exists, a password reset link has been sent"}

        except AuthError as e:
            logger.error(f"Supabase auth error during password reset request: {str(e)}")
            # Don't reveal if email exists for security
            return {"message": "If the email exists, a password reset link has been sent"}
        except Exception as e:
            logger.error(f"Unexpected error during password reset request: {str(e)}")
            return {"message": "If the email exists, a password reset link has been sent"}

    async def reset_password(self, access_token: str, new_password: str) -> Dict[str, str]:
        """
        Reset user's password using access token from reset email.

        Args:
            access_token: Access token from password reset email
            new_password: New password

        Returns:
            Success message

        Raises:
            HTTPException: If password reset fails
        """
        try:
            # Set the session with the reset token
            self.auth_client.set_session(access_token, "")

            # Update password
            self.auth_client.update_user({
                "password": new_password
            })

            logger.info("Password reset successfully")
            return {"message": "Password reset successfully"}

        except AuthError as e:
            logger.error(f"Supabase auth error during password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Password reset failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password reset failed due to server error"
            )

    async def verify_email(self, token: str) -> Dict[str, str]:
        """
        Verify user's email address using verification token.

        Args:
            token: Email verification token

        Returns:
            Success message

        Raises:
            HTTPException: If email verification fails
        """
        try:
            # Verify email with Supabase
            auth_response = self.auth_client.verify_otp({
                "token": token,
                "type": "email"
            })

            if not auth_response.user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid verification token"
                )

            logger.info(f"Email verified successfully for user: {auth_response.user.email}")
            return {"message": "Email verified successfully"}

        except AuthError as e:
            logger.error(f"Supabase auth error during email verification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Email verification failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during email verification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Email verification failed due to server error"
            )

    async def change_password(
        self,
        user_id: str,
        current_password: str,
        new_password: str,
        db: Session
    ) -> Dict[str, str]:
        """
        Change user's password after verifying current password.

        Args:
            user_id: User's ID
            current_password: Current password
            new_password: New password
            db: Database session

        Returns:
            Success message

        Raises:
            HTTPException: If password change fails
        """
        try:
            # Get user from database
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Verify current password
            if not verify_password(current_password, user.hashed_password):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is incorrect"
                )

            # Update password in Supabase (requires admin privileges)
            self.service_client.auth.admin.update_user_by_id(
                user_id,
                {"password": new_password}
            )

            # Update hashed password in local database
            user.hashed_password = get_password_hash(new_password)
            db.commit()

            logger.info(f"Password changed successfully for user: {user.email}")
            return {"message": "Password changed successfully"}

        except AuthError as e:
            logger.error(f"Supabase auth error during password change: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Password change failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during password change: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password change failed due to server error"
            )

    async def get_user_by_token(self, access_token: str) -> Optional[Dict[str, Any]]:
        """
        Get user information from access token.

        Args:
            access_token: User's access token

        Returns:
            User information or None if token is invalid
        """
        try:
            # Get user from Supabase using token
            user_response = self.auth_client.get_user(access_token)

            if user_response.user:
                return {
                    "id": user_response.user.id,
                    "email": user_response.user.email,
                    "user_metadata": user_response.user.user_metadata,
                    "app_metadata": user_response.user.app_metadata,
                    "created_at": user_response.user.created_at,
                    "updated_at": user_response.user.updated_at,
                    "email_confirmed_at": user_response.user.email_confirmed_at
                }

            return None

        except AuthError as e:
            logger.error(f"Supabase auth error getting user by token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting user by token: {str(e)}")
            return None

    async def resend_verification_email(self, email: str) -> Dict[str, str]:
        """
        Resend email verification.

        Args:
            email: User's email address

        Returns:
            Success message
        """
        try:
            self.auth_client.resend(email, "signup")

            logger.info(f"Verification email resent to: {email}")
            return {"message": "Verification email sent"}

        except AuthError as e:
            logger.error(f"Supabase auth error resending verification: {str(e)}")
            return {"message": "Verification email sent"}  # Don't reveal if email exists
        except Exception as e:
            logger.error(f"Unexpected error resending verification: {str(e)}")
            return {"message": "Verification email sent"}


# Create singleton instance
auth_service = AuthService()