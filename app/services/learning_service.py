"""
Learning service for processing AI feedback and updating learning patterns.
Handles background tasks for learning pattern discovery and application.
"""
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.core.database import get_db
from app.models.learning import LearningProfile, AIFeedback, LearnedPattern, LearningSession
from app.models.document_type import DocumentType
from app.services.anthropic_service import anthropic_service


class LearningService:
    """Service for managing learning processes and pattern discovery"""

    def __init__(self):
        self.confidence_threshold = 0.6
        self.min_feedback_for_pattern = 3

    async def process_feedback_for_learning(
        self,
        feedback_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session
    ) -> Dict[str, Any]:
        """
        Process feedback to discover and update learning patterns
        """
        try:
            # Get the feedback
            feedback = db.query(AIFeedback).filter(
                AIFeedback.id == feedback_id,
                AIFeedback.tenant_id == tenant_id
            ).first()

            if not feedback or feedback.processed:
                return {"status": "skipped", "reason": "feedback not found or already processed"}

            # Get learning profile
            profile = db.query(LearningProfile).filter(
                LearningProfile.document_type_id == feedback.document_type_id,
                LearningProfile.tenant_id == tenant_id
            ).first()

            if not profile or not profile.learning_enabled:
                return {"status": "skipped", "reason": "learning disabled"}

            # Analyze feedback for patterns
            patterns_discovered = await self._analyze_feedback_for_patterns(
                feedback, profile, db
            )

            # Update existing patterns based on feedback
            patterns_updated = await self._update_patterns_from_feedback(
                feedback, profile, db
            )

            # Mark feedback as processed
            feedback.processed = True
            feedback.applied_to_learning = True
            feedback.processed_at = datetime.now(timezone.utc)

            # Update profile statistics
            profile.last_learning_update = datetime.now(timezone.utc)

            db.commit()

            return {
                "status": "completed",
                "patterns_discovered": len(patterns_discovered),
                "patterns_updated": len(patterns_updated),
                "feedback_id": str(feedback_id)
            }

        except Exception as e:
            db.rollback()
            return {
                "status": "failed",
                "error": str(e),
                "feedback_id": str(feedback_id)
            }

    async def _analyze_feedback_for_patterns(
        self,
        feedback: AIFeedback,
        profile: LearningProfile,
        db: Session
    ) -> List[Dict[str, Any]]:
        """
        Analyze feedback to discover new patterns
        """
        patterns_discovered = []

        # Get similar feedback for pattern detection
        similar_feedback = db.query(AIFeedback).filter(
            AIFeedback.document_type_id == feedback.document_type_id,
            AIFeedback.tenant_id == feedback.tenant_id,
            AIFeedback.feedback_type == feedback.feedback_type,
            AIFeedback.sentiment == feedback.sentiment,
            AIFeedback.processed == True
        ).limit(10).all()

        if len(similar_feedback) >= self.min_feedback_for_pattern:
            # Use AI to analyze patterns
            pattern_analysis = await self._ai_pattern_discovery(
                feedback, similar_feedback, profile
            )

            for pattern_data in pattern_analysis:
                if pattern_data.get("confidence", 0) >= self.confidence_threshold:
                    # Check if pattern already exists
                    existing_pattern = db.query(LearnedPattern).filter(
                        LearnedPattern.document_type_id == feedback.document_type_id,
                        LearnedPattern.tenant_id == feedback.tenant_id,
                        LearnedPattern.pattern_name == pattern_data["name"]
                    ).first()

                    if not existing_pattern:
                        # Create new pattern
                        new_pattern = LearnedPattern(
                            document_type_id=feedback.document_type_id,
                            tenant_id=feedback.tenant_id,
                            pattern_type=pattern_data["type"],
                            pattern_name=pattern_data["name"],
                            pattern_description=pattern_data["description"],
                            pattern_data=pattern_data["data"],
                            trigger_conditions=pattern_data.get("triggers", {}),
                            confidence_score=pattern_data["confidence"],
                            source_feedback_ids=[str(feedback.id)]
                        )

                        db.add(new_pattern)
                        patterns_discovered.append(pattern_data)

        return patterns_discovered

    async def _update_patterns_from_feedback(
        self,
        feedback: AIFeedback,
        profile: LearningProfile,
        db: Session
    ) -> List[Dict[str, Any]]:
        """
        Update existing patterns based on new feedback
        """
        patterns_updated = []

        # Get applicable patterns
        applicable_patterns = db.query(LearnedPattern).filter(
            LearnedPattern.document_type_id == feedback.document_type_id,
            LearnedPattern.tenant_id == feedback.tenant_id,
            LearnedPattern.is_active == True
        ).all()

        for pattern in applicable_patterns:
            # Check if feedback is relevant to this pattern
            if self._is_feedback_relevant_to_pattern(feedback, pattern):
                # Update pattern confidence and usage stats
                if feedback.sentiment == "positive":
                    pattern.success_rate = min(1.0, pattern.success_rate + 0.1)
                    pattern.confidence_score = min(1.0, pattern.confidence_score + 0.05)
                elif feedback.sentiment == "negative":
                    pattern.success_rate = max(0.0, pattern.success_rate - 0.1)
                    pattern.confidence_score = max(0.0, pattern.confidence_score - 0.05)

                pattern.usage_count += 1
                pattern.updated_at = datetime.now(timezone.utc)

                # Add feedback ID to source list
                source_ids = pattern.source_feedback_ids or []
                if str(feedback.id) not in source_ids:
                    source_ids.append(str(feedback.id))
                    pattern.source_feedback_ids = source_ids

                patterns_updated.append({
                    "pattern_id": str(pattern.id),
                    "pattern_name": pattern.pattern_name,
                    "new_confidence": pattern.confidence_score
                })

        return patterns_updated

    def _is_feedback_relevant_to_pattern(
        self,
        feedback: AIFeedback,
        pattern: LearnedPattern
    ) -> bool:
        """
        Check if feedback is relevant to a specific pattern
        """
        # Simple relevance check based on feedback type and pattern type
        if feedback.feedback_type == "content" and pattern.pattern_type == "content":
            return True
        if feedback.feedback_type == "structure" and pattern.pattern_type == "structure":
            return True
        if feedback.ai_output_section and pattern.pattern_data.get("applicable_sections"):
            return feedback.ai_output_section in pattern.pattern_data["applicable_sections"]

        return False

    async def _ai_pattern_discovery(
        self,
        feedback: AIFeedback,
        similar_feedback: List[AIFeedback],
        profile: LearningProfile
    ) -> List[Dict[str, Any]]:
        """
        Use AI to discover patterns from feedback
        """
        try:
            # Prepare feedback data for AI analysis
            feedback_data = {
                "current_feedback": {
                    "type": feedback.feedback_type,
                    "rating": feedback.rating,
                    "sentiment": feedback.sentiment,
                    "text": feedback.feedback_text,
                    "section": feedback.ai_output_section,
                    "suggested_improvement": feedback.suggested_improvement
                },
                "similar_feedback": [
                    {
                        "type": f.feedback_type,
                        "rating": f.rating,
                        "sentiment": f.sentiment,
                        "text": f.feedback_text,
                        "section": f.ai_output_section
                    }
                    for f in similar_feedback
                ]
            }

            # Use anthropic service to analyze patterns
            prompt = f"""
            Analyze the following feedback data to discover learning patterns:

            Current Feedback: {json.dumps(feedback_data['current_feedback'], indent=2)}

            Similar Historical Feedback: {json.dumps(feedback_data['similar_feedback'], indent=2)}

            Identify patterns that could improve future AI outputs. Return patterns in this JSON format:
            [
              {{
                "name": "Pattern Name",
                "type": "content|structure|style|preference",
                "description": "What this pattern represents",
                "confidence": 0.8,
                "data": {{"specific_rules": "pattern rules"}},
                "triggers": {{"when_to_apply": "conditions"}}
              }}
            ]

            Only return patterns with confidence >= 0.6.
            """

            response = await anthropic_service.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                temperature=0.3,
                messages=[{"role": "user", "content": prompt}]
            )

            response_text = "".join([
                block.text for block in response.content
                if hasattr(block, 'text')
            ])

            # Parse JSON response
            import re
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                patterns = json.loads(json_match.group())
                return patterns

            return []

        except Exception as e:
            print(f"Error in AI pattern discovery: {str(e)}")
            return []

    async def apply_patterns_to_document(
        self,
        document_content: str,
        form_data: Dict[str, Any],
        document_type_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session
    ) -> Dict[str, Any]:
        """
        Apply learned patterns to improve document content
        """
        try:
            # Get applicable patterns
            patterns = db.query(LearnedPattern).filter(
                LearnedPattern.document_type_id == document_type_id,
                LearnedPattern.tenant_id == tenant_id,
                LearnedPattern.is_active == True,
                LearnedPattern.auto_apply == True,
                LearnedPattern.confidence_score >= 0.7
            ).all()

            if not patterns:
                return {"status": "no_patterns", "content": document_content}

            # Apply patterns using AI
            improved_content = await self._apply_patterns_with_ai(
                document_content, form_data, patterns
            )

            return {
                "status": "applied",
                "content": improved_content,
                "patterns_applied": len(patterns)
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "content": document_content
            }

    async def _apply_patterns_with_ai(
        self,
        content: str,
        form_data: Dict[str, Any],
        patterns: List[LearnedPattern]
    ) -> str:
        """
        Use AI to apply patterns to content
        """
        patterns_text = "\n".join([
            f"- {p.pattern_name}: {p.pattern_description}\n  Rules: {json.dumps(p.pattern_data)}"
            for p in patterns
        ])

        prompt = f"""
        Apply the following learned patterns to improve this document:

        PATTERNS TO APPLY:
        {patterns_text}

        ORIGINAL CONTENT:
        {content}

        FORM DATA CONTEXT:
        {json.dumps(form_data, indent=2)}

        Return the improved content with patterns applied. Maintain the original structure and meaning while incorporating the learned improvements.
        """

        response = await anthropic_service.client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=4000,
            temperature=0.3,
            messages=[{"role": "user", "content": prompt}]
        )

        return "".join([
            block.text for block in response.content
            if hasattr(block, 'text')
        ])


# Global instance
learning_service = LearningService()
