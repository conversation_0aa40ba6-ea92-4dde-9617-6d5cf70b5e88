"""
AI Agent Execution Service

This service handles all agent execution operations including:
- Single agent execution
- Batch agent execution  
- Job lifecycle management
- Execution monitoring and metrics
- Error handling and recovery
- Execution strategies and optimization
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.core.database import SessionLocal
from app.models.agent import AIAgent, AgentJob, JobStatus, AgentStatus
from app.models.user import User
from app.models.tenant import Tenant
from app.services.anthropic_service import anthropic_service
from app.schemas.agent import AgentExecuteRequest, AgentJobResponse

logger = logging.getLogger(__name__)


class AgentExecutionService:
    """
    Comprehensive service for AI agent execution and job management.
    """
    
    def __init__(self):
        self.max_concurrent_jobs = 5
        self.job_timeout_seconds = 300  # 5 minutes
        self.retry_attempts = 3
        
    async def execute_agent(
        self,
        agent_id: uuid.UUID,
        doc_type: str,
        request: AgentExecuteRequest,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session
    ) -> Agent<PERSON>ob:
        """
        Execute a single agent with comprehensive error handling.
        
        Args:
            agent_id: UUID of the agent to execute
            doc_type: Document type for the agent
            request: Execution request with input data
            user_id: ID of the requesting user
            tenant_id: ID of the tenant
            db: Database session
            
        Returns:
            AgentJob: Created job instance
            
        Raises:
            ValueError: If agent not found or invalid
            RuntimeError: If execution fails
        """
        # Validate agent
        agent = await self._get_and_validate_agent(agent_id, doc_type, tenant_id, db)
        
        # Create job
        job = AgentJob(
            agent_id=agent_id,
            job_type=request.job_type,
            input_data=request.input_data,
            document_id=request.document_id,
            user_id=user_id,
            tenant_id=tenant_id,
            status=JobStatus.PENDING
        )
        
        db.add(job)
        db.commit()
        db.refresh(job)
        
        logger.info(f"Created agent job {job.id} for agent {agent_id}")
        
        # Start execution asynchronously
        asyncio.create_task(self._execute_job_async(job.id, agent, request.input_data))
        
        return job
    
    async def execute_batch_agents(
        self,
        execution_requests: List[Dict[str, Any]],
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session
    ) -> List[AgentJob]:
        """
        Execute multiple agents in batch with concurrency control.
        
        Args:
            execution_requests: List of execution request dictionaries
            user_id: ID of the requesting user
            tenant_id: ID of the tenant
            db: Database session
            
        Returns:
            List[AgentJob]: List of created job instances
        """
        jobs = []
        
        for req_data in execution_requests:
            try:
                request = AgentExecuteRequest(**req_data['request'])
                job = await self.execute_agent(
                    agent_id=req_data['agent_id'],
                    doc_type=req_data['doc_type'],
                    request=request,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    db=db
                )
                jobs.append(job)
            except Exception as e:
                logger.error(f"Failed to create batch job: {str(e)}")
                # Create failed job for tracking
                failed_job = AgentJob(
                    agent_id=req_data.get('agent_id'),
                    job_type=req_data.get('request', {}).get('job_type', 'unknown'),
                    input_data=req_data.get('request', {}).get('input_data', {}),
                    user_id=user_id,
                    tenant_id=tenant_id,
                    status=JobStatus.FAILED,
                    error_message=str(e)
                )
                db.add(failed_job)
                jobs.append(failed_job)
        
        db.commit()
        logger.info(f"Created {len(jobs)} batch jobs for user {user_id}")
        
        return jobs

    async def get_job_status(
        self,
        job_id: uuid.UUID,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session
    ) -> Optional[AgentJob]:
        """
        Get job status with user/tenant validation.
        
        Args:
            job_id: UUID of the job
            user_id: ID of the requesting user
            tenant_id: ID of the tenant
            db: Database session
            
        Returns:
            AgentJob: Job instance if found and accessible
        """
        job = db.query(AgentJob).filter(
            and_(
                AgentJob.id == job_id,
                AgentJob.user_id == user_id,
                AgentJob.tenant_id == tenant_id
            )
        ).first()
        
        return job

    async def cancel_job(
        self,
        job_id: uuid.UUID,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session
    ) -> bool:
        """
        Cancel a running job.
        
        Args:
            job_id: UUID of the job to cancel
            user_id: ID of the requesting user
            tenant_id: ID of the tenant
            db: Database session
            
        Returns:
            bool: True if job was cancelled successfully
        """
        job = await self.get_job_status(job_id, user_id, tenant_id, db)
        
        if not job:
            return False
            
        if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            return False
            
        # Update job status
        job.status = JobStatus.CANCELLED
        job.completed_at = datetime.now(timezone.utc)
        job.error_message = "Job cancelled by user"
        
        db.commit()
        logger.info(f"Cancelled job {job_id}")
        
        return True

    async def get_user_jobs(
        self,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        db: Session,
        limit: int = 50,
        offset: int = 0,
        status_filter: Optional[JobStatus] = None
    ) -> List[AgentJob]:
        """
        Get jobs for a specific user with pagination and filtering.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            db: Database session
            limit: Maximum number of jobs to return
            offset: Number of jobs to skip
            status_filter: Optional status filter
            
        Returns:
            List[AgentJob]: List of jobs
        """
        query = db.query(AgentJob).filter(
            and_(
                AgentJob.user_id == user_id,
                AgentJob.tenant_id == tenant_id
            )
        )
        
        if status_filter:
            query = query.filter(AgentJob.status == status_filter)
            
        jobs = query.order_by(desc(AgentJob.created_at)).offset(offset).limit(limit).all()
        
        return jobs

    async def _get_and_validate_agent(
        self,
        agent_id: uuid.UUID,
        doc_type: str,
        tenant_id: uuid.UUID,
        db: Session
    ) -> AIAgent:
        """
        Get and validate agent for execution.

        Args:
            agent_id: UUID of the agent
            doc_type: Document type
            tenant_id: ID of the tenant
            db: Database session

        Returns:
            AIAgent: Validated agent instance

        Raises:
            ValueError: If agent not found or invalid
        """
        agent = db.query(AIAgent).filter(
            and_(
                AIAgent.id == agent_id,
                AIAgent.doc_type == doc_type,
                AIAgent.status == AgentStatus.ACTIVE,
                or_(
                    AIAgent.tenant_id == tenant_id,
                    AIAgent.tenant_id.is_(None)  # System agents
                )
            )
        ).first()

        if not agent:
            raise ValueError("Agent not found or not accessible")

        # Check if agent supports the requested operation
        if not agent.capabilities.get("document_generation", False):
            raise ValueError("Agent does not support document generation")

        return agent

    async def _execute_job_async(
        self,
        job_id: uuid.UUID,
        agent: AIAgent,
        input_data: Dict[str, Any]
    ):
        """
        Execute agent job asynchronously with proper error handling.

        Args:
            job_id: UUID of the job
            agent: Agent instance to execute
            input_data: Input data for execution
        """
        db = SessionLocal()

        try:
            job = db.query(AgentJob).filter(AgentJob.id == job_id).first()
            if not job:
                logger.error(f"Job {job_id} not found for execution")
                return

            # Update job status to running
            job.status = JobStatus.RUNNING
            job.started_at = datetime.now(timezone.utc)
            job.progress_percentage = 10
            db.commit()

            logger.info(f"Starting execution of job {job_id} with agent {agent.id}")

            # Prepare execution context
            execution_context = {
                "agent_id": str(agent.id),
                "agent_name": agent.name,
                "model": agent.model_name,
                "temperature": agent.temperature / 100.0,  # Convert to 0-1 range
                "max_tokens": agent.max_tokens,
                "system_prompt": agent.system_prompt,
                **input_data
            }

            job.progress_percentage = 30
            db.commit()

            # Execute with retry logic
            result = await self._execute_with_retry(agent, execution_context, job, db)

            # Update job with results
            job.output_data = {
                "result": result,
                "execution_context": execution_context
            }
            job.status = JobStatus.COMPLETED
            job.completed_at = datetime.now(timezone.utc)
            job.progress_percentage = 100

            # Update agent usage count
            agent.usage_count += 1

            db.commit()
            logger.info(f"Completed execution of job {job_id}")

        except Exception as e:
            logger.error(f"Failed to execute job {job_id}: {str(e)}")

            # Update job with error
            if 'job' in locals():
                job.status = JobStatus.FAILED
                job.error_message = str(e)
                job.completed_at = datetime.now(timezone.utc)
                db.commit()

        finally:
            db.close()

    async def _execute_with_retry(
        self,
        agent: AIAgent,
        execution_context: Dict[str, Any],
        job: AgentJob,
        db: Session
    ) -> str:
        """
        Execute agent with retry logic.

        Args:
            agent: Agent instance
            execution_context: Execution context
            job: Job instance
            db: Database session

        Returns:
            str: Execution result

        Raises:
            RuntimeError: If all retry attempts fail
        """
        last_error = None

        for attempt in range(self.retry_attempts):
            try:
                start_time = datetime.now()

                # Call the anthropic service with agent configuration
                result = await anthropic_service.generate_with_agent_config(
                    prompt=execution_context.get("prompt", ""),
                    model=agent.model_name,
                    temperature=agent.temperature / 100.0,
                    max_tokens=agent.max_tokens,
                    system_prompt=agent.system_prompt,
                    context=execution_context
                )

                end_time = datetime.now()
                execution_time = int((end_time - start_time).total_seconds() * 1000)

                # Update job timing
                job.execution_time_ms = execution_time
                job.model_used = agent.model_name
                job.progress_percentage = 90
                db.commit()

                return result

            except Exception as e:
                last_error = e
                logger.warning(f"Execution attempt {attempt + 1} failed: {str(e)}")

                if attempt < self.retry_attempts - 1:
                    # Wait before retry (exponential backoff)
                    await asyncio.sleep(2 ** attempt)

        raise RuntimeError(f"All {self.retry_attempts} execution attempts failed. Last error: {str(last_error)}")

    async def get_execution_metrics(
        self,
        tenant_id: uuid.UUID,
        db: Session,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get execution metrics for a tenant.

        Args:
            tenant_id: ID of the tenant
            db: Database session
            days: Number of days to look back

        Returns:
            Dict[str, Any]: Execution metrics
        """
        from datetime import timedelta

        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)

        # Get job statistics
        jobs_query = db.query(AgentJob).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                AgentJob.created_at >= cutoff_date
            )
        )

        total_jobs = jobs_query.count()
        completed_jobs = jobs_query.filter(AgentJob.status == JobStatus.COMPLETED).count()
        failed_jobs = jobs_query.filter(AgentJob.status == JobStatus.FAILED).count()
        running_jobs = jobs_query.filter(AgentJob.status == JobStatus.RUNNING).count()

        # Calculate success rate
        success_rate = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0

        # Get average execution time
        completed_jobs_with_time = jobs_query.filter(
            and_(
                AgentJob.status == JobStatus.COMPLETED,
                AgentJob.execution_time_ms.isnot(None)
            )
        ).all()

        avg_execution_time = 0
        if completed_jobs_with_time:
            avg_execution_time = sum(job.execution_time_ms for job in completed_jobs_with_time) / len(completed_jobs_with_time)

        return {
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "running_jobs": running_jobs,
            "success_rate": round(success_rate, 2),
            "average_execution_time_ms": round(avg_execution_time, 2),
            "period_days": days
        }


# Create singleton instance
agent_execution_service = AgentExecutionService()
