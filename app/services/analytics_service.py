"""
Analytics Service Layer
Handles analytics calculations, data aggregation, and metrics computation.
"""
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, asc, text
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date, timedelta
import uuid

from app.models.document import Document, DocumentGenerationSession, DocumentRefinementJob
from app.models.document_type import DocumentType
from app.models.user import User
from app.models.tenant import Tenant
from app.models.export import ExportJob, ExportHistory
from app.models.learning import AIFeedback, LearningProfile
from app.models.agent import AgentJob, AIAgent
from app.models.analytics import (
    AnalyticsMetric, DocumentAnalytics, TeamPerformanceMetrics,
    AIEffectivenessMetrics, ExportUsageMetrics, FormCompletionMetrics
)
from app.models.form_schema import FormValidationLog
from app.schemas.analytics import MetricValue, ChartDataPoint, ChartData


class AnalyticsService:
    """Service for analytics calculations and data aggregation"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_metric_value(
        self, 
        current_value: float, 
        previous_value: Optional[float] = None
    ) -> MetricValue:
        """Calculate metric value with change percentage and trend"""
        change = None
        trend = None
        
        if previous_value is not None and previous_value > 0:
            change = ((current_value - previous_value) / previous_value) * 100
            if change > 5:
                trend = "up"
            elif change < -5:
                trend = "down"
            else:
                trend = "stable"
        
        return MetricValue(
            value=current_value,
            change=change,
            trend=trend
        )
    
    def get_date_range_filter(
        self, 
        start_date: date, 
        end_date: date, 
        comparison_period: bool = False
    ) -> Tuple[date, date]:
        """Get date range, optionally with comparison period"""
        if comparison_period:
            period_length = (end_date - start_date).days
            comparison_start = start_date - timedelta(days=period_length)
            comparison_end = start_date - timedelta(days=1)
            return comparison_start, comparison_end
        return start_date, end_date
    
    def get_dashboard_overview(
        self, 
        tenant_id: uuid.UUID, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, MetricValue]:
        """Calculate dashboard overview metrics"""
        
        # Get comparison period
        comp_start, comp_end = self.get_date_range_filter(start_date, end_date, True)
        
        # Total documents
        total_docs = self.db.query(Document).filter(
            Document.tenant_id == tenant_id
        ).count()
        
        prev_total_docs = self.db.query(Document).filter(
            and_(
                Document.tenant_id == tenant_id,
                Document.created_at <= comp_end
            )
        ).count()
        
        # Documents this period
        docs_this_period = self.db.query(Document).filter(
            and_(
                Document.tenant_id == tenant_id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).count()
        
        docs_prev_period = self.db.query(Document).filter(
            and_(
                Document.tenant_id == tenant_id,
                func.date(Document.created_at) >= comp_start,
                func.date(Document.created_at) <= comp_end
            )
        ).count()
        
        # Active users
        active_users = self.db.query(User).filter(
            and_(
                User.tenant_id == tenant_id,
                User.is_active == True,
                User.last_login >= start_date
            )
        ).count()
        
        prev_active_users = self.db.query(User).filter(
            and_(
                User.tenant_id == tenant_id,
                User.is_active == True,
                User.last_login >= comp_start,
                User.last_login <= comp_end
            )
        ).count()
        
        # AI generations
        ai_generations = self.db.query(AgentJob).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).count()
        
        prev_ai_generations = self.db.query(AgentJob).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                func.date(AgentJob.created_at) >= comp_start,
                func.date(AgentJob.created_at) <= comp_end
            )
        ).count()
        
        # Completion rate
        completed_docs = self.db.query(Document).filter(
            and_(
                Document.tenant_id == tenant_id,
                Document.status.in_(["approved", "published"]),
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).count()
        
        completion_rate = (completed_docs / docs_this_period * 100) if docs_this_period > 0 else 0
        
        prev_completed_docs = self.db.query(Document).filter(
            and_(
                Document.tenant_id == tenant_id,
                Document.status.in_(["approved", "published"]),
                func.date(Document.created_at) >= comp_start,
                func.date(Document.created_at) <= comp_end
            )
        ).count()
        
        prev_completion_rate = (prev_completed_docs / docs_prev_period * 100) if docs_prev_period > 0 else 0
        
        # Average generation time
        avg_gen_time = self.db.query(
            func.avg(AgentJob.execution_time_ms)
        ).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                AgentJob.execution_time_ms.isnot(None),
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).scalar() or 0
        
        prev_avg_gen_time = self.db.query(
            func.avg(AgentJob.execution_time_ms)
        ).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                AgentJob.execution_time_ms.isnot(None),
                func.date(AgentJob.created_at) >= comp_start,
                func.date(AgentJob.created_at) <= comp_end
            )
        ).scalar() or 0
        
        return {
            "total_documents": self.calculate_metric_value(total_docs, prev_total_docs),
            "documents_this_month": self.calculate_metric_value(docs_this_period, docs_prev_period),
            "active_users": self.calculate_metric_value(active_users, prev_active_users),
            "ai_generations": self.calculate_metric_value(ai_generations, prev_ai_generations),
            "completion_rate": self.calculate_metric_value(completion_rate, prev_completion_rate),
            "avg_generation_time": self.calculate_metric_value(avg_gen_time / 1000, prev_avg_gen_time / 1000)  # Convert to seconds
        }
    
    def get_document_creation_trend(
        self, 
        tenant_id: uuid.UUID, 
        start_date: date, 
        end_date: date
    ) -> ChartData:
        """Get document creation trend data"""
        
        # Query daily document creation counts
        results = self.db.query(
            func.date(Document.created_at).label('date'),
            func.count(Document.id).label('count')
        ).filter(
            and_(
                Document.tenant_id == tenant_id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).group_by(
            func.date(Document.created_at)
        ).order_by(
            func.date(Document.created_at)
        ).all()
        
        # Convert to chart data points
        data_points = [
            {"date": result.date.isoformat(), "value": result.count}
            for result in results
        ]
        
        return ChartData(
            title="Document Creation Trend",
            type="line",
            series=[{
                "name": "Documents Created",
                "data": data_points
            }]
        )
    
    def get_document_type_distribution(
        self, 
        tenant_id: uuid.UUID, 
        start_date: date, 
        end_date: date
    ) -> ChartData:
        """Get document type distribution"""
        
        results = self.db.query(
            DocumentType.name.label('doc_type'),
            func.count(Document.id).label('count')
        ).join(
            Document, Document.document_type_id == DocumentType.id
        ).filter(
            and_(
                Document.tenant_id == tenant_id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).group_by(
            DocumentType.name
        ).order_by(
            desc(func.count(Document.id))
        ).all()
        
        data_points = [
            {"name": result.doc_type, "value": result.count}
            for result in results
        ]
        
        return ChartData(
            title="Document Type Distribution",
            type="pie",
            series=[{
                "name": "Document Types",
                "data": data_points
            }]
        )
    
    def get_user_activity_trend(
        self, 
        tenant_id: uuid.UUID, 
        start_date: date, 
        end_date: date
    ) -> ChartData:
        """Get user activity trend"""
        
        # Daily active users (users who created documents)
        results = self.db.query(
            func.date(Document.created_at).label('date'),
            func.count(func.distinct(Document.user_id)).label('active_users')
        ).filter(
            and_(
                Document.tenant_id == tenant_id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).group_by(
            func.date(Document.created_at)
        ).order_by(
            func.date(Document.created_at)
        ).all()
        
        data_points = [
            {"date": result.date.isoformat(), "value": result.active_users}
            for result in results
        ]
        
        return ChartData(
            title="Daily Active Users",
            type="area",
            series=[{
                "name": "Active Users",
                "data": data_points
            }]
        )
    
    def get_ai_performance_metrics(
        self, 
        tenant_id: uuid.UUID, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Get AI performance metrics"""
        
        # Success rate over time
        results = self.db.query(
            func.date(AgentJob.created_at).label('date'),
            func.count(AgentJob.id).label('total'),
            func.sum(
                func.case(
                    (AgentJob.status == 'completed', 1),
                    else_=0
                )
            ).label('successful')
        ).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).group_by(
            func.date(AgentJob.created_at)
        ).order_by(
            func.date(AgentJob.created_at)
        ).all()
        
        success_rate_data = []
        avg_time_data = []
        
        for result in results:
            success_rate = (result.successful / result.total * 100) if result.total > 0 else 0
            success_rate_data.append({
                "date": result.date.isoformat(),
                "value": success_rate
            })
        
        # Average response time
        time_results = self.db.query(
            func.date(AgentJob.created_at).label('date'),
            func.avg(AgentJob.execution_time_ms).label('avg_time')
        ).filter(
            and_(
                AgentJob.tenant_id == tenant_id,
                AgentJob.execution_time_ms.isnot(None),
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).group_by(
            func.date(AgentJob.created_at)
        ).order_by(
            func.date(AgentJob.created_at)
        ).all()
        
        for result in time_results:
            avg_time_data.append({
                "date": result.date.isoformat(),
                "value": result.avg_time / 1000  # Convert to seconds
            })
        
        return {
            "success_rate": success_rate_data,
            "avg_response_time": avg_time_data
        }
    
    def get_export_usage_trend(
        self, 
        tenant_id: uuid.UUID, 
        start_date: date, 
        end_date: date
    ) -> ChartData:
        """Get export usage trend"""
        
        results = self.db.query(
            func.date(ExportJob.created_at).label('date'),
            func.count(ExportJob.id).label('count')
        ).filter(
            and_(
                ExportJob.tenant_id == tenant_id,
                func.date(ExportJob.created_at) >= start_date,
                func.date(ExportJob.created_at) <= end_date
            )
        ).group_by(
            func.date(ExportJob.created_at)
        ).order_by(
            func.date(ExportJob.created_at)
        ).all()
        
        data_points = [
            {"date": result.date.isoformat(), "value": result.count}
            for result in results
        ]
        
        return ChartData(
            title="Export Usage Trend",
            type="bar",
            series=[{
                "name": "Exports",
                "data": data_points
            }]
        )
    
    def get_recent_activity(
        self, 
        tenant_id: uuid.UUID, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get recent activity for dashboard"""
        
        # Recent documents
        recent_docs = self.db.query(Document).filter(
            Document.tenant_id == tenant_id
        ).order_by(
            desc(Document.created_at)
        ).limit(limit).all()
        
        activities = []
        for doc in recent_docs:
            activities.append({
                "type": "document_created",
                "title": f"Document '{doc.title}' created",
                "timestamp": doc.created_at,
                "user_id": doc.user_id,
                "document_id": doc.id
            })
        
        # Sort by timestamp
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return activities[:limit]
    
    def generate_insights(
        self, 
        tenant_id: uuid.UUID, 
        metrics: Dict[str, Any]
    ) -> List[str]:
        """Generate insights based on metrics"""
        
        insights = []
        
        # Document creation insights
        if metrics["documents_this_month"].change and metrics["documents_this_month"].change > 20:
            insights.append("Document creation is up significantly this month - great productivity!")
        elif metrics["documents_this_month"].change and metrics["documents_this_month"].change < -20:
            insights.append("Document creation has decreased this month - consider team engagement initiatives.")
        
        # AI performance insights
        if metrics["avg_generation_time"].value > 30:  # seconds
            insights.append("AI generation times are higher than optimal - consider model optimization.")
        
        # Completion rate insights
        if metrics["completion_rate"].value < 60:
            insights.append("Document completion rate is below 60% - review workflow bottlenecks.")
        elif metrics["completion_rate"].value > 85:
            insights.append("Excellent document completion rate - team is highly efficient!")
        
        return insights
