"""
Custom exceptions and error handling for authentication operations.

This module provides specialized exception classes and error handlers
for Supabase Auth integration and general authentication operations.
"""

from fastapi import HTTPException, status
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class AuthenticationError(HTTPException):
    """Base authentication error."""
    
    def __init__(
        self,
        detail: str = "Authentication failed",
        status_code: int = status.HTTP_401_UNAUTHORIZED,
        headers: Optional[Dict[str, str]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)


class RegistrationError(HTTPException):
    """User registration error."""
    
    def __init__(
        self,
        detail: str = "Registration failed",
        status_code: int = status.HTTP_400_BAD_REQUEST
    ):
        super().__init__(status_code=status_code, detail=detail)


class PasswordError(HTTPException):
    """Password-related error."""
    
    def __init__(
        self,
        detail: str = "Password operation failed",
        status_code: int = status.HTTP_400_BAD_REQUEST
    ):
        super().__init__(status_code=status_code, detail=detail)


class SessionError(HTTPException):
    """Session management error."""
    
    def __init__(
        self,
        detail: str = "Session operation failed",
        status_code: int = status.HTTP_401_UNAUTHORIZED
    ):
        super().__init__(status_code=status_code, detail=detail)


class SupabaseError(HTTPException):
    """Supabase-specific error."""
    
    def __init__(
        self,
        detail: str = "Supabase operation failed",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        supabase_error: Optional[str] = None
    ):
        self.supabase_error = supabase_error
        super().__init__(status_code=status_code, detail=detail)


def handle_supabase_auth_error(error: Exception) -> HTTPException:
    """
    Convert Supabase auth errors to appropriate HTTP exceptions.
    
    Args:
        error: Supabase auth error
        
    Returns:
        HTTPException: Appropriate HTTP exception
    """
    error_message = str(error).lower()
    
    # Map common Supabase errors to appropriate HTTP status codes
    if "invalid login credentials" in error_message:
        return AuthenticationError("Invalid email or password")
    
    elif "email already registered" in error_message or "user already registered" in error_message:
        return RegistrationError("Email is already registered")
    
    elif "invalid email" in error_message:
        return RegistrationError("Invalid email address")
    
    elif "password should be at least" in error_message:
        return PasswordError("Password does not meet minimum requirements")
    
    elif "invalid refresh token" in error_message:
        return SessionError("Invalid or expired refresh token")
    
    elif "email not confirmed" in error_message:
        return AuthenticationError("Email address not verified")
    
    elif "signup is disabled" in error_message:
        return RegistrationError("User registration is currently disabled")
    
    elif "invalid verification token" in error_message:
        return RegistrationError("Invalid or expired verification token")
    
    elif "password reset" in error_message and "invalid" in error_message:
        return PasswordError("Invalid or expired password reset token")
    
    elif "rate limit" in error_message:
        return HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many requests. Please try again later."
        )
    
    elif "network" in error_message or "connection" in error_message:
        return HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service temporarily unavailable"
        )
    
    else:
        # Generic error for unknown Supabase errors
        logger.error(f"Unhandled Supabase error: {error}")
        return SupabaseError(
            detail="Authentication service error",
            supabase_error=str(error)
        )


def log_auth_event(
    event_type: str,
    user_id: Optional[str] = None,
    email: Optional[str] = None,
    success: bool = True,
    details: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None
):
    """
    Log authentication events for monitoring and auditing.
    
    Args:
        event_type: Type of auth event (login, register, logout, etc.)
        user_id: User ID if available
        email: User email if available
        success: Whether the operation was successful
        details: Additional event details
        error: Error message if operation failed
    """
    log_data = {
        "event_type": event_type,
        "success": success,
        "timestamp": "now()",
    }
    
    if user_id:
        log_data["user_id"] = user_id
    
    if email:
        log_data["email"] = email
    
    if details:
        log_data["details"] = details
    
    if error:
        log_data["error"] = error
    
    if success:
        logger.info(f"Auth event: {event_type}", extra=log_data)
    else:
        logger.warning(f"Auth event failed: {event_type}", extra=log_data)


def validate_auth_input(
    email: Optional[str] = None,
    password: Optional[str] = None,
    token: Optional[str] = None
) -> None:
    """
    Validate common authentication input parameters.
    
    Args:
        email: Email address to validate
        password: Password to validate
        token: Token to validate
        
    Raises:
        HTTPException: If validation fails
    """
    if email is not None:
        if not email or "@" not in email:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Invalid email address format"
            )
        
        if len(email) > 254:  # RFC 5321 limit
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Email address too long"
            )
    
    if password is not None:
        if not password:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Password is required"
            )
        
        if len(password) < 8:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Password must be at least 8 characters long"
            )
        
        if len(password) > 100:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Password is too long"
            )
    
    if token is not None:
        if not token:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Token is required"
            )
        
        if len(token) > 2048:  # Reasonable token length limit
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Token is too long"
            )


class AuthMetrics:
    """
    Simple metrics collection for authentication operations.
    
    This class provides basic metrics collection that can be extended
    with proper monitoring tools like Prometheus in production.
    """
    
    def __init__(self):
        self.metrics = {
            "login_attempts": 0,
            "login_successes": 0,
            "login_failures": 0,
            "registrations": 0,
            "password_resets": 0,
            "email_verifications": 0,
            "token_refreshes": 0,
            "logouts": 0
        }
    
    def increment(self, metric: str):
        """Increment a metric counter."""
        if metric in self.metrics:
            self.metrics[metric] += 1
    
    def get_metrics(self) -> Dict[str, int]:
        """Get current metrics."""
        return self.metrics.copy()
    
    def reset_metrics(self):
        """Reset all metrics to zero."""
        for key in self.metrics:
            self.metrics[key] = 0


# Global metrics instance
auth_metrics = AuthMetrics()
