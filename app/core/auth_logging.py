"""
Logging configuration and utilities for authentication operations.

This module provides structured logging for authentication events,
security monitoring, and audit trails.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from functools import wraps
import traceback

from app.core.config import settings


class AuthLogFormatter(logging.Formatter):
    """
    Custom formatter for authentication logs with structured output.
    """
    
    def format(self, record):
        """Format log record with structured data."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }
        
        # Add extra fields if present
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        
        if hasattr(record, 'email'):
            log_data["email"] = record.email
        
        if hasattr(record, 'event_type'):
            log_data["event_type"] = record.event_type
        
        if hasattr(record, 'ip_address'):
            log_data["ip_address"] = record.ip_address
        
        if hasattr(record, 'user_agent'):
            log_data["user_agent"] = record.user_agent
        
        if hasattr(record, 'success'):
            log_data["success"] = record.success
        
        if hasattr(record, 'error_code'):
            log_data["error_code"] = record.error_code
        
        if hasattr(record, 'details'):
            log_data["details"] = record.details
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, default=str)


def setup_auth_logging():
    """
    Set up logging configuration for authentication operations.
    """
    # Create auth logger
    auth_logger = logging.getLogger("auth")
    auth_logger.setLevel(logging.INFO)
    
    # Create handler
    handler = logging.StreamHandler()
    handler.setLevel(logging.INFO)
    
    # Set formatter
    formatter = AuthLogFormatter()
    handler.setFormatter(formatter)
    
    # Add handler to logger
    if not auth_logger.handlers:
        auth_logger.addHandler(handler)
    
    return auth_logger


def log_auth_attempt(
    event_type: str,
    email: Optional[str] = None,
    user_id: Optional[str] = None,
    success: bool = True,
    error_message: Optional[str] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
):
    """
    Log authentication attempt with structured data.
    
    Args:
        event_type: Type of authentication event
        email: User email if available
        user_id: User ID if available
        success: Whether the attempt was successful
        error_message: Error message if failed
        ip_address: Client IP address
        user_agent: Client user agent
        details: Additional details
    """
    logger = logging.getLogger("auth")
    
    extra_data = {
        "event_type": event_type,
        "success": success,
    }
    
    if email:
        extra_data["email"] = email
    
    if user_id:
        extra_data["user_id"] = user_id
    
    if ip_address:
        extra_data["ip_address"] = ip_address
    
    if user_agent:
        extra_data["user_agent"] = user_agent
    
    if details:
        extra_data["details"] = details
    
    if success:
        logger.info(f"Auth success: {event_type}", extra=extra_data)
    else:
        if error_message:
            extra_data["error_message"] = error_message
        logger.warning(f"Auth failure: {event_type}", extra=extra_data)


def log_security_event(
    event_type: str,
    severity: str = "INFO",
    user_id: Optional[str] = None,
    email: Optional[str] = None,
    ip_address: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
):
    """
    Log security-related events for monitoring and alerting.
    
    Args:
        event_type: Type of security event
        severity: Event severity (INFO, WARNING, ERROR, CRITICAL)
        user_id: User ID if available
        email: User email if available
        ip_address: Client IP address
        details: Additional event details
    """
    logger = logging.getLogger("auth.security")
    
    extra_data = {
        "event_type": event_type,
        "severity": severity,
    }
    
    if user_id:
        extra_data["user_id"] = user_id
    
    if email:
        extra_data["email"] = email
    
    if ip_address:
        extra_data["ip_address"] = ip_address
    
    if details:
        extra_data["details"] = details
    
    message = f"Security event: {event_type}"
    
    if severity == "INFO":
        logger.info(message, extra=extra_data)
    elif severity == "WARNING":
        logger.warning(message, extra=extra_data)
    elif severity == "ERROR":
        logger.error(message, extra=extra_data)
    elif severity == "CRITICAL":
        logger.critical(message, extra=extra_data)


def auth_audit_log(func):
    """
    Decorator to automatically log authentication function calls.
    
    This decorator logs function entry, exit, and any exceptions
    for audit trail purposes.
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        logger = logging.getLogger("auth.audit")
        
        # Log function entry
        logger.info(
            f"Auth function called: {func.__name__}",
            extra={
                "function": func.__name__,
                "args_count": len(args),
                "kwargs_keys": list(kwargs.keys())
            }
        )
        
        try:
            # Call the function
            result = await func(*args, **kwargs)
            
            # Log successful completion
            logger.info(
                f"Auth function completed: {func.__name__}",
                extra={
                    "function": func.__name__,
                    "success": True
                }
            )
            
            return result
            
        except Exception as e:
            # Log exception
            logger.error(
                f"Auth function failed: {func.__name__}",
                extra={
                    "function": func.__name__,
                    "success": False,
                    "error": str(e),
                    "exception_type": type(e).__name__
                },
                exc_info=True
            )
            raise
    
    return wrapper


class AuthLogContext:
    """
    Context manager for adding consistent logging context to auth operations.
    """
    
    def __init__(
        self,
        operation: str,
        user_id: Optional[str] = None,
        email: Optional[str] = None,
        ip_address: Optional[str] = None
    ):
        self.operation = operation
        self.user_id = user_id
        self.email = email
        self.ip_address = ip_address
        self.logger = logging.getLogger("auth")
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.utcnow()
        
        extra_data = {
            "operation": self.operation,
            "phase": "start"
        }
        
        if self.user_id:
            extra_data["user_id"] = self.user_id
        
        if self.email:
            extra_data["email"] = self.email
        
        if self.ip_address:
            extra_data["ip_address"] = self.ip_address
        
        self.logger.info(f"Starting auth operation: {self.operation}", extra=extra_data)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.utcnow()
        duration = (end_time - self.start_time).total_seconds()
        
        extra_data = {
            "operation": self.operation,
            "phase": "end",
            "duration_seconds": duration,
            "success": exc_type is None
        }
        
        if self.user_id:
            extra_data["user_id"] = self.user_id
        
        if self.email:
            extra_data["email"] = self.email
        
        if self.ip_address:
            extra_data["ip_address"] = self.ip_address
        
        if exc_type:
            extra_data["error"] = str(exc_val)
            extra_data["exception_type"] = exc_type.__name__
            self.logger.error(f"Auth operation failed: {self.operation}", extra=extra_data)
        else:
            self.logger.info(f"Auth operation completed: {self.operation}", extra=extra_data)


# Initialize auth logging
auth_logger = setup_auth_logging()
