"""
Supabase client configuration and initialization.

This module provides centralized Supabase client management for authentication
and database operations throughout the application.
"""

from supabase import create_client, Client
from gotrue import SyncGoTrueClient
from functools import lru_cache
import logging
from typing import Optional

from app.core.config import settings

logger = logging.getLogger(__name__)


class SupabaseClient:
    """
    Singleton Supabase client wrapper for managing connections and operations.
    
    This class provides both anonymous and service-level access to Supabase,
    with proper error handling and connection management.
    """
    
    _instance: Optional['SupabaseClient'] = None
    _client: Optional[Client] = None
    _service_client: Optional[Client] = None
    
    def __new__(cls) -> 'SupabaseClient':
        """Ensure singleton pattern for Supabase client."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize Supabase clients if not already done."""
        if self._client is None:
            self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize both anonymous and service Supabase clients."""
        try:
            # Anonymous client for public operations (sign up, sign in, etc.)
            self._client = create_client(
                supabase_url=settings.SUPABASE_URL,
                supabase_key=settings.SUPABASE_ANON_KEY
            )
            
            # Service client for admin operations (user management, etc.)
            self._service_client = create_client(
                supabase_url=settings.SUPABASE_URL,
                supabase_key=settings.SUPABASE_SERVICE_KEY
            )
            
            logger.info("Supabase clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase clients: {str(e)}")
            raise
    
    @property
    def client(self) -> Client:
        """Get the anonymous Supabase client."""
        if self._client is None:
            self._initialize_clients()
        return self._client
    
    @property
    def service_client(self) -> Client:
        """Get the service Supabase client for admin operations."""
        if self._service_client is None:
            self._initialize_clients()
        return self._service_client
    
    def get_auth_client(self, use_service_key: bool = False) -> SyncGoTrueClient:
        """
        Get the GoTrue auth client.
        
        Args:
            use_service_key: If True, use service client for admin operations
            
        Returns:
            SyncGoTrueClient: The authentication client
        """
        if use_service_key:
            return self.service_client.auth
        return self.client.auth
    
    def health_check(self) -> dict:
        """
        Perform a health check on Supabase connection.
        
        Returns:
            dict: Health check results
        """
        try:
            # Test anonymous client
            response = self.client.table('users').select('id').limit(1).execute()
            
            return {
                "status": "healthy",
                "anonymous_client": "connected",
                "service_client": "connected",
                "timestamp": "now()"
            }
        except Exception as e:
            logger.error(f"Supabase health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "now()"
            }


@lru_cache()
def get_supabase_client() -> SupabaseClient:
    """
    Get cached Supabase client instance.
    
    Returns:
        SupabaseClient: Singleton Supabase client instance
    """
    return SupabaseClient()


# Convenience functions for common operations
def get_supabase() -> Client:
    """Get the anonymous Supabase client."""
    return get_supabase_client().client


def get_supabase_service() -> Client:
    """Get the service Supabase client."""
    return get_supabase_client().service_client


def get_auth_client(use_service_key: bool = False) -> SyncGoTrueClient:
    """Get the GoTrue auth client."""
    return get_supabase_client().get_auth_client(use_service_key)
