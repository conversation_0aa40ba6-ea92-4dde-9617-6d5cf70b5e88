from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.core.config import settings
from app.api.v1 import (
    auth, users, tenants, prd, document_types, documents, form_schemas, form_questions,
    analytics_dashboard, analytics_documents, analytics_team_ai, analytics_specialized
)
from app.api.v1 import ai_prompts
from app.api.v1.learning import router as learning_router

app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["auth"])
app.include_router(users.router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])
app.include_router(tenants.router, prefix=f"{settings.API_V1_STR}/tenants", tags=["tenants"])
app.include_router(prd.router, prefix=f"{settings.API_V1_STR}/prd", tags=["prd"])
app.include_router(document_types.router, prefix=f"{settings.API_V1_STR}/document-types", tags=["document-types"])
app.include_router(documents.router, prefix=f"{settings.API_V1_STR}/documents", tags=["documents"])

# Form schema routers
app.include_router(form_schemas.router, prefix=f"{settings.API_V1_STR}/form-schemas", tags=["form-schemas"])
app.include_router(form_questions.router, prefix=f"{settings.API_V1_STR}/form-schemas", tags=["form-questions"])

# AI Agent and Prompt Management routers (modular structure)
app.include_router(ai_prompts.router, prefix=f"{settings.API_V1_STR}/ai-prompts", tags=["ai-prompts"])

# Analytics and Insights routers
app.include_router(analytics_dashboard.router, prefix=f"{settings.API_V1_STR}/analytics", tags=["analytics-dashboard"])
app.include_router(analytics_documents.router, prefix=f"{settings.API_V1_STR}/analytics", tags=["analytics-documents"])
app.include_router(analytics_team_ai.router, prefix=f"{settings.API_V1_STR}/analytics", tags=["analytics-team-ai"])
app.include_router(analytics_specialized.router, prefix=f"{settings.API_V1_STR}/analytics", tags=["analytics-specialized"])

# Learning and AI Enhancement router
app.include_router(learning_router, prefix=f"{settings.API_V1_STR}/learning", tags=["learning-ai"])

# Mount static files (create directory if it doesn't exist)
import os
if not os.path.exists("static"):
    os.makedirs("static")
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.APP_NAME}"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.get("/auth/confirm")
async def email_confirmation_page():
    """Serve the email confirmation page."""
    return FileResponse("static/email-confirmation.html")