"""
Document utility functions shared across document API modules.
"""
from typing import Dict, Any, List
import re

from app.models.document_type import DocumentType


def validate_form_data(form_data: Dict[str, Any], document_type: DocumentType) -> Dict[str, Any]:
    """
    Validate form data against document type schema
    """
    errors = []
    warnings = []

    schema = document_type.form_schema

    if "fields" in schema:
        for field in schema["fields"]:
            field_name = field["name"]
            field_type = field.get("type", "text")
            required = field.get("required", False)

            # Check required fields
            if required and (field_name not in form_data or not form_data[field_name]):
                errors.append({
                    "field": field_name,
                    "message": f"Field '{field_name}' is required"
                })
                continue

            # Skip validation if field is not provided and not required
            if field_name not in form_data:
                continue

            value = form_data[field_name]

            # Type validation
            if field_type == "number" and not isinstance(value, (int, float)):
                try:
                    float(value)
                except (ValueError, TypeError):
                    errors.append({
                        "field": field_name,
                        "message": f"Field '{field_name}' must be a number"
                    })

            elif field_type == "email" and value:
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, str(value)):
                    errors.append({
                        "field": field_name,
                        "message": f"Field '{field_name}' must be a valid email address"
                    })

            elif field_type == "choice" and value:
                choices = field.get("choices", [])
                if choices and value not in choices:
                    errors.append({
                        "field": field_name,
                        "message": f"Field '{field_name}' must be one of: {', '.join(choices)}"
                    })

            # Length validation
            min_length = field.get("min_length")
            max_length = field.get("max_length")

            if min_length and len(str(value)) < min_length:
                errors.append({
                    "field": field_name,
                    "message": f"Field '{field_name}' must be at least {min_length} characters"
                })

            if max_length and len(str(value)) > max_length:
                errors.append({
                    "field": field_name,
                    "message": f"Field '{field_name}' must be no more than {max_length} characters"
                })

    return {
        "is_valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings
    }


def extract_section_content(content: str, section_name: str) -> str:
    """
    Extract content of a specific section from document
    """
    # Try different section header patterns
    patterns = [
        rf"#{1,6}\s*{re.escape(section_name)}\s*\n(.*?)(?=\n#{1,6}\s|\Z)",
        rf"^{re.escape(section_name)}\s*\n[=-]+\s*\n(.*?)(?=\n\w+\s*\n[=-]+|\Z)",
        rf"^{re.escape(section_name)}:\s*\n(.*?)(?=\n\w+:|\Z)"
    ]

    for pattern in patterns:
        match = re.search(pattern, content, re.MULTILINE | re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

    return ""


def extract_component_content(content: str, component_id: str) -> str:
    """
    Extract content of a specific component from document
    """
    # Try to find component by ID or marker
    patterns = [
        rf"<!--\s*{re.escape(component_id)}\s*-->(.*?)<!--\s*/\s*{re.escape(component_id)}\s*-->",
        rf"\[{re.escape(component_id)}\](.*?)\[/{re.escape(component_id)}\]",
        rf"<{re.escape(component_id)}>(.*?)</{re.escape(component_id)}>"
    ]

    for pattern in patterns:
        match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

    return ""


def extract_document_sections(content: str) -> List[str]:
    """
    Extract all section names from document
    """
    sections = []

    # Find markdown headers
    header_matches = re.findall(r'^#{1,6}\s*(.+?)$', content, re.MULTILINE)
    sections.extend([match.strip() for match in header_matches])

    # Find underlined headers
    underline_matches = re.findall(r'^(.+?)\s*\n[=-]+\s*$', content, re.MULTILINE)
    sections.extend([match.strip() for match in underline_matches])

    # Find colon-style headers
    colon_matches = re.findall(r'^(.+?):\s*$', content, re.MULTILINE)
    sections.extend([match.strip() for match in colon_matches])

    # Remove duplicates and return
    return list(set(sections))


def extract_document_components(content: str) -> List[Dict[str, str]]:
    """
    Extract all components from document
    """
    components = []

    # Find HTML-style components
    html_matches = re.findall(r'<!--\s*(\w+)\s*-->', content)
    for match in html_matches:
        components.append({"id": match, "type": "html_comment"})

    # Find bracket-style components
    bracket_matches = re.findall(r'\[(\w+)\]', content)
    for match in bracket_matches:
        components.append({"id": match, "type": "bracket"})

    # Find XML-style components
    xml_matches = re.findall(r'<(\w+)>', content)
    for match in xml_matches:
        components.append({"id": match, "type": "xml"})

    # Remove duplicates
    seen = set()
    unique_components = []
    for comp in components:
        comp_key = f"{comp['id']}_{comp['type']}"
        if comp_key not in seen:
            seen.add(comp_key)
            unique_components.append(comp)

    return unique_components
