"""
AI Agent Execution Monitoring API endpoints.

This module provides comprehensive monitoring and analytics for AI agent execution:
- Real-time execution monitoring
- Performance metrics and analytics
- Job status dashboards
- System health monitoring
- Usage statistics and trends
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIAgent, AgentJob, JobStatus, AgentStatus
from app.services.agent_execution_service import agent_execution_service

router = APIRouter()


@router.get("/dashboard")
async def get_monitoring_dashboard(
    days: int = Query(7, ge=1, le=90),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get comprehensive monitoring dashboard data for the tenant.
    """
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # Get basic execution metrics
    metrics = await agent_execution_service.get_execution_metrics(
        tenant_id=current_tenant.id,
        db=db,
        days=days
    )
    
    # Get job status distribution
    status_distribution = db.query(
        AgentJob.status,
        func.count(AgentJob.id).label('count')
    ).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.created_at >= cutoff_date
        )
    ).group_by(AgentJob.status).all()
    
    # Get top performing agents
    top_agents = db.query(
        AIAgent.id,
        AIAgent.name,
        AIAgent.doc_type,
        func.count(AgentJob.id).label('job_count'),
        func.avg(AgentJob.execution_time_ms).label('avg_execution_time'),
        func.sum(
            func.case(
                (AgentJob.status == JobStatus.COMPLETED, 1),
                else_=0
            )
        ).label('success_count')
    ).join(
        AgentJob, AIAgent.id == AgentJob.agent_id
    ).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.created_at >= cutoff_date
        )
    ).group_by(
        AIAgent.id, AIAgent.name, AIAgent.doc_type
    ).order_by(
        desc('job_count')
    ).limit(10).all()
    
    # Get daily job trends
    daily_trends = db.query(
        func.date(AgentJob.created_at).label('date'),
        func.count(AgentJob.id).label('total_jobs'),
        func.sum(
            func.case(
                (AgentJob.status == JobStatus.COMPLETED, 1),
                else_=0
            )
        ).label('completed_jobs'),
        func.sum(
            func.case(
                (AgentJob.status == JobStatus.FAILED, 1),
                else_=0
            )
        ).label('failed_jobs')
    ).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.created_at >= cutoff_date
        )
    ).group_by(
        func.date(AgentJob.created_at)
    ).order_by(
        asc('date')
    ).all()
    
    # Get current running jobs
    running_jobs = db.query(AgentJob).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.status == JobStatus.RUNNING
        )
    ).count()
    
    # Get pending jobs
    pending_jobs = db.query(AgentJob).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.status == JobStatus.PENDING
        )
    ).count()
    
    return {
        "metrics": metrics,
        "status_distribution": [
            {"status": item.status, "count": item.count}
            for item in status_distribution
        ],
        "top_agents": [
            {
                "id": str(agent.id),
                "name": agent.name,
                "doc_type": agent.doc_type,
                "job_count": agent.job_count,
                "avg_execution_time_ms": float(agent.avg_execution_time or 0),
                "success_rate": round((agent.success_count / agent.job_count * 100) if agent.job_count > 0 else 0, 2)
            }
            for agent in top_agents
        ],
        "daily_trends": [
            {
                "date": trend.date.isoformat(),
                "total_jobs": trend.total_jobs,
                "completed_jobs": trend.completed_jobs,
                "failed_jobs": trend.failed_jobs,
                "success_rate": round((trend.completed_jobs / trend.total_jobs * 100) if trend.total_jobs > 0 else 0, 2)
            }
            for trend in daily_trends
        ],
        "current_status": {
            "running_jobs": running_jobs,
            "pending_jobs": pending_jobs,
            "total_active": running_jobs + pending_jobs
        }
    }


@router.get("/agents/{agent_id}/performance")
async def get_agent_performance(
    agent_id: uuid.UUID,
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get detailed performance metrics for a specific agent.
    """
    # Verify agent access
    agent = db.query(AIAgent).filter(
        and_(
            AIAgent.id == agent_id,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    ).first()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found or not accessible"
        )
    
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # Get job statistics
    jobs_query = db.query(AgentJob).filter(
        and_(
            AgentJob.agent_id == agent_id,
            AgentJob.created_at >= cutoff_date
        )
    )
    
    total_jobs = jobs_query.count()
    completed_jobs = jobs_query.filter(AgentJob.status == JobStatus.COMPLETED).count()
    failed_jobs = jobs_query.filter(AgentJob.status == JobStatus.FAILED).count()
    
    # Get execution time statistics
    execution_times = jobs_query.filter(
        and_(
            AgentJob.status == JobStatus.COMPLETED,
            AgentJob.execution_time_ms.isnot(None)
        )
    ).with_entities(AgentJob.execution_time_ms).all()
    
    execution_stats = {}
    if execution_times:
        times = [t[0] for t in execution_times]
        execution_stats = {
            "min_ms": min(times),
            "max_ms": max(times),
            "avg_ms": sum(times) / len(times),
            "median_ms": sorted(times)[len(times) // 2]
        }
    
    # Get recent job history
    recent_jobs = jobs_query.order_by(desc(AgentJob.created_at)).limit(20).all()
    
    # Get error patterns
    error_patterns = db.query(
        AgentJob.error_message,
        func.count(AgentJob.id).label('count')
    ).filter(
        and_(
            AgentJob.agent_id == agent_id,
            AgentJob.status == JobStatus.FAILED,
            AgentJob.created_at >= cutoff_date,
            AgentJob.error_message.isnot(None)
        )
    ).group_by(AgentJob.error_message).order_by(desc('count')).limit(10).all()
    
    return {
        "agent": {
            "id": str(agent.id),
            "name": agent.name,
            "doc_type": agent.doc_type,
            "status": agent.status,
            "usage_count": agent.usage_count
        },
        "performance": {
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "success_rate": round((completed_jobs / total_jobs * 100) if total_jobs > 0 else 0, 2),
            "execution_stats": execution_stats
        },
        "recent_jobs": [
            {
                "id": str(job.id),
                "status": job.status,
                "job_type": job.job_type,
                "execution_time_ms": job.execution_time_ms,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "error_message": job.error_message
            }
            for job in recent_jobs
        ],
        "error_patterns": [
            {
                "error_message": error.error_message,
                "count": error.count
            }
            for error in error_patterns
        ]
    }


@router.get("/system-health")
async def get_system_health(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get system health status for AI agent execution.
    """
    # Get current system status
    current_time = datetime.now(timezone.utc)
    one_hour_ago = current_time - timedelta(hours=1)
    
    # Check for stuck jobs (running for more than 1 hour)
    stuck_jobs = db.query(AgentJob).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.status == JobStatus.RUNNING,
            AgentJob.started_at < one_hour_ago
        )
    ).count()
    
    # Get recent error rate
    recent_jobs = db.query(AgentJob).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.created_at >= one_hour_ago
        )
    ).count()
    
    recent_failures = db.query(AgentJob).filter(
        and_(
            AgentJob.tenant_id == current_tenant.id,
            AgentJob.status == JobStatus.FAILED,
            AgentJob.created_at >= one_hour_ago
        )
    ).count()
    
    error_rate = (recent_failures / recent_jobs * 100) if recent_jobs > 0 else 0
    
    # Get active agents count
    active_agents = db.query(AIAgent).filter(
        and_(
            AIAgent.status == AgentStatus.ACTIVE,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    ).count()
    
    # Determine overall health status
    health_status = "healthy"
    issues = []
    
    if stuck_jobs > 0:
        health_status = "warning"
        issues.append(f"{stuck_jobs} jobs running for more than 1 hour")
    
    if error_rate > 20:  # More than 20% error rate
        health_status = "critical" if health_status != "critical" else health_status
        issues.append(f"High error rate: {error_rate:.1f}%")
    elif error_rate > 10:  # More than 10% error rate
        health_status = "warning" if health_status == "healthy" else health_status
        issues.append(f"Elevated error rate: {error_rate:.1f}%")
    
    return {
        "status": health_status,
        "timestamp": current_time.isoformat(),
        "metrics": {
            "active_agents": active_agents,
            "stuck_jobs": stuck_jobs,
            "recent_jobs": recent_jobs,
            "recent_failures": recent_failures,
            "error_rate": round(error_rate, 2)
        },
        "issues": issues
    }
