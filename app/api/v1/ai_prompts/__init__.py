"""
Modular AI Prompts and Agents API package.

This package contains organized AI prompts and agents endpoints split into logical modules:
- agents: AI agent CRUD operations and management
- prompts: AI prompt CRUD operations and template management  
- execution: Agent job execution and status tracking
- capabilities: Agent capabilities and system integrations

Following the established pattern of keeping files small and logically organized.
"""

from fastapi import APIRouter

# Import all sub-routers
from .agents import router as agents_router
from .prompts import router as prompts_router
from .execution import router as execution_router
from .capabilities import router as capabilities_router
from .monitoring import router as monitoring_router

# Main AI prompts router that combines all sub-routers
router = APIRouter()

# Include sub-routers with appropriate prefixes
router.include_router(agents_router, prefix="/agents", tags=["ai-agents"])
router.include_router(prompts_router, prefix="/prompts", tags=["ai-prompts"])
router.include_router(execution_router, prefix="/execution", tags=["ai-execution"])
router.include_router(capabilities_router, prefix="/capabilities", tags=["ai-capabilities"])
router.include_router(monitoring_router, prefix="/monitoring", tags=["ai-monitoring"])
