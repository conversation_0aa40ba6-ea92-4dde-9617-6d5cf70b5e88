"""
AI Agent Execution API endpoints.

This module handles agent job execution, status tracking, and result management.
Focuses on async execution, job lifecycle management, and proper error handling.
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Dict, Any, List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIAgent, AgentJob, JobStatus, AgentStatus
from app.schemas.agent import (
    AgentExecuteRequest,
    AgentJobResponse,
    BatchExecuteRequest,
    BatchExecuteResponse,
    JobListResponse,
    ExecutionMetricsResponse,
    MessageResponse
)
from app.services.agent_execution_service import agent_execution_service

router = APIRouter()


# Note: Background execution is now handled by the agent_execution_service


@router.post("/{doc_type}/{agent_id}/execute", response_model=AgentJobResponse)
async def execute_agent(
    doc_type: str,
    agent_id: uuid.UUID,
    request: AgentExecuteRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Execute a specific agent using the enhanced execution service.
    """
    try:
        job = await agent_execution_service.execute_agent(
            agent_id=agent_id,
            doc_type=doc_type,
            request=request,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            db=db
        )

        return AgentJobResponse.from_orm(job)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute agent: {str(e)}"
        )


@router.get("/jobs/{job_id}", response_model=AgentJobResponse)
async def get_agent_job_status(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get agent job status using the execution service.
    """
    job = await agent_execution_service.get_job_status(
        job_id=job_id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        db=db
    )

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )

    return AgentJobResponse.from_orm(job)


@router.post("/jobs/{job_id}/cancel", response_model=MessageResponse)
async def cancel_agent_job(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Cancel an agent job using the execution service.
    """
    success = await agent_execution_service.cancel_job(
        job_id=job_id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        db=db
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job not found or cannot be cancelled"
        )

    return MessageResponse(message="Job cancelled successfully")


@router.post("/batch-execute", response_model=BatchExecuteResponse)
async def execute_batch_agents(
    request: BatchExecuteRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Execute multiple agents in batch with concurrency control.
    """
    try:
        jobs = await agent_execution_service.execute_batch_agents(
            execution_requests=request.execution_requests,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            db=db
        )

        # Count successful and failed jobs
        successful_jobs = sum(1 for job in jobs if job.status != JobStatus.FAILED)
        failed_jobs = len(jobs) - successful_jobs

        return BatchExecuteResponse(
            jobs=[AgentJobResponse.from_orm(job) for job in jobs],
            total_jobs=len(jobs),
            successful_jobs=successful_jobs,
            failed_jobs=failed_jobs
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute batch agents: {str(e)}"
        )


@router.get("/jobs", response_model=JobListResponse)
async def get_user_jobs(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[JobStatus] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get jobs for the current user with pagination and filtering.
    """
    jobs = await agent_execution_service.get_user_jobs(
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        db=db,
        limit=limit,
        offset=offset,
        status_filter=status_filter
    )

    # Get total count for pagination
    total_query = db.query(AgentJob).filter(
        and_(
            AgentJob.user_id == current_user.id,
            AgentJob.tenant_id == current_tenant.id
        )
    )

    if status_filter:
        total_query = total_query.filter(AgentJob.status == status_filter)

    total = total_query.count()

    return JobListResponse(
        jobs=[AgentJobResponse.from_orm(job) for job in jobs],
        total=total,
        limit=limit,
        offset=offset
    )


@router.get("/metrics", response_model=ExecutionMetricsResponse)
async def get_execution_metrics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get execution metrics for the current tenant.
    """
    metrics = await agent_execution_service.get_execution_metrics(
        tenant_id=current_tenant.id,
        db=db,
        days=days
    )

    return ExecutionMetricsResponse(**metrics)
