"""
AI Agent Capabilities API endpoints.

This module handles agent capabilities discovery, supported operations mapping,
and system integrations. Focuses on capability aggregation and operation validation.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Dict, Any, List, Optional
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIAgent, AgentStatus
from app.schemas.agent import (
    AgentCapabilitiesResponse,
    AgentListItem,
    MessageResponse
)

router = APIRouter()


def map_capabilities_to_operations(capabilities: Dict[str, Any]) -> List[str]:
    """
    Map agent capabilities to supported operations
    """
    operations = set()
    
    for capability, enabled in capabilities.items():
        if enabled:
            # Map capabilities to operations
            if capability == "document_generation":
                operations.add("generate")
                operations.add("execute")
            elif capability == "refinement":
                operations.add("refine")
            elif capability == "follow_up_questions":
                operations.add("follow_up")
            elif capability == "suggestions":
                operations.add("suggest")
            elif capability == "analysis":
                operations.add("analyze")
            elif capability == "validation":
                operations.add("validate")
    
    return list(operations)


@router.get("/{doc_type}", response_model=AgentCapabilitiesResponse)
async def get_agent_capabilities(
    doc_type: str,
    include_inactive: bool = Query(False, description="Include inactive agents"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get agent capabilities for a document type
    """
    # Build query for agents
    query = db.query(AIAgent).filter(
        and_(
            AIAgent.doc_type == doc_type,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    )
    
    # Filter by status unless including inactive
    if not include_inactive:
        query = query.filter(AIAgent.status == AgentStatus.ACTIVE)
    
    agents = query.all()

    if not agents:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No agents found for this document type"
        )

    # Aggregate capabilities
    all_capabilities = {}
    supported_operations = set()

    for agent in agents:
        # Merge capabilities
        for capability, enabled in agent.capabilities.items():
            if enabled:
                all_capabilities[capability] = True

        # Add operations for this agent
        agent_operations = map_capabilities_to_operations(agent.capabilities)
        supported_operations.update(agent_operations)

    return AgentCapabilitiesResponse(
        doc_type=doc_type,
        available_agents=[AgentListItem.from_orm(agent) for agent in agents],
        capabilities=all_capabilities,
        supported_operations=list(supported_operations)
    )


@router.get("/{doc_type}/operations", response_model=Dict[str, List[str]])
async def get_supported_operations(
    doc_type: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get supported operations for a document type with agent details
    """
    # Get all active agents for the document type
    agents = db.query(AIAgent).filter(
        and_(
            AIAgent.doc_type == doc_type,
            AIAgent.status == AgentStatus.ACTIVE,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    ).all()

    if not agents:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No agents found for this document type"
        )

    # Map operations to agents that support them
    operations_map = {}
    
    for agent in agents:
        agent_operations = map_capabilities_to_operations(agent.capabilities)
        
        for operation in agent_operations:
            if operation not in operations_map:
                operations_map[operation] = []
            operations_map[operation].append(agent.name)

    return operations_map


@router.get("/{doc_type}/validate/{operation}")
async def validate_operation_support(
    doc_type: str,
    operation: str,
    agent_id: Optional[uuid.UUID] = Query(None, description="Specific agent to validate"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Validate if an operation is supported for a document type
    """
    query = db.query(AIAgent).filter(
        and_(
            AIAgent.doc_type == doc_type,
            AIAgent.status == AgentStatus.ACTIVE,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    )
    
    # Filter by specific agent if provided
    if agent_id:
        query = query.filter(AIAgent.id == agent_id)
    
    agents = query.all()

    if not agents:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No agents found for validation"
        )

    # Check if any agent supports the operation
    supporting_agents = []
    
    for agent in agents:
        agent_operations = map_capabilities_to_operations(agent.capabilities)
        if operation in agent_operations:
            supporting_agents.append({
                "id": str(agent.id),
                "name": agent.name,
                "model": agent.model_name
            })

    return {
        "operation": operation,
        "doc_type": doc_type,
        "supported": len(supporting_agents) > 0,
        "supporting_agents": supporting_agents
    }
