"""
Form Questions Conditional Logic API endpoints.
Handles conditional logic rules, dependencies, and dynamic form behavior.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any, Union
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, ConditionalRule
from app.schemas.form_schema import (
    ConditionalLogic,
    ConditionalLogicResponse,
    ConditionalRuleResponse,
    ConditionalOperator,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for conditional logic management"""
    return True


def get_form_schema_or_404(doc_type: str, tenant_id: uuid.UUID, db: Session) -> FormSchema:
    """Get form schema or raise 404"""
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == tenant_id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    return schema


def evaluate_condition(operator: str, field_value: Any, condition_value: Any) -> bool:
    """Evaluate a conditional logic rule"""
    try:
        if operator == ConditionalOperator.EQUALS:
            return field_value == condition_value
        elif operator == ConditionalOperator.NOT_EQUALS:
            return field_value != condition_value
        elif operator == ConditionalOperator.GREATER_THAN:
            return float(field_value) > float(condition_value)
        elif operator == ConditionalOperator.LESS_THAN:
            return float(field_value) < float(condition_value)
        elif operator == ConditionalOperator.CONTAINS:
            return str(condition_value).lower() in str(field_value).lower()
        elif operator == ConditionalOperator.NOT_CONTAINS:
            return str(condition_value).lower() not in str(field_value).lower()
        elif operator == ConditionalOperator.IN:
            if isinstance(condition_value, list):
                return field_value in condition_value
            return field_value == condition_value
        elif operator == ConditionalOperator.NOT_IN:
            if isinstance(condition_value, list):
                return field_value not in condition_value
            return field_value != condition_value
        elif operator == ConditionalOperator.IS_EMPTY:
            return field_value is None or field_value == ""
        elif operator == ConditionalOperator.IS_NOT_EMPTY:
            return field_value is not None and field_value != ""
        else:
            return False
    except (ValueError, TypeError):
        return False


@router.get("/{doc_type}/conditional", response_model=ConditionalLogicResponse)
async def get_conditional_logic(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get all conditional logic rules for a document type
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get all conditional rules for this schema
    rules = db.query(ConditionalRule).filter(
        ConditionalRule.form_schema_id == schema.id,
        ConditionalRule.is_active == True
    ).all()
    
    # Convert to response format
    rule_responses = []
    for rule in rules:
        # Get the question to get the question_id
        question = db.query(FormQuestion).filter(FormQuestion.id == rule.question_id).first()
        if question:
            rule_response = ConditionalRuleResponse(
                id=rule.id,
                question_id=question.question_id,
                condition_question_id=rule.condition_question_id,
                operator=rule.operator,
                value=rule.condition_value,
                action=rule.action,
                target_question_ids=rule.target_question_ids,
                is_active=rule.is_active
            )
            rule_responses.append(rule_response)
    
    return ConditionalLogicResponse(
        document_type=doc_type,
        rules=rule_responses,
        total_rules=len(rule_responses)
    )


@router.post("/{doc_type}/conditional/evaluate")
async def evaluate_conditional_logic(
    doc_type: str,
    form_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Evaluate conditional logic rules against form data and return actions to take
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get all conditional rules for this schema
    rules = db.query(ConditionalRule).filter(
        ConditionalRule.form_schema_id == schema.id,
        ConditionalRule.is_active == True
    ).all()
    
    # Get question lookup for question_id to database question mapping
    questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).all()
    question_lookup = {q.question_id: q for q in questions}
    
    actions = []
    
    for rule in rules:
        # Get the condition field value
        condition_value = form_data.get(rule.condition_question_id)
        
        # Evaluate the condition
        if evaluate_condition(rule.operator, condition_value, rule.condition_value):
            # Get the question that this rule applies to
            question = db.query(FormQuestion).filter(FormQuestion.id == rule.question_id).first()
            if question:
                action = {
                    "rule_id": str(rule.id),
                    "question_id": question.question_id,
                    "action": rule.action,
                    "target_question_ids": rule.target_question_ids,
                    "condition_met": True
                }
                actions.append(action)
    
    return {
        "document_type": doc_type,
        "actions": actions,
        "total_actions": len(actions)
    }


@router.post("/{doc_type}/questions/{q_id}/conditional", response_model=ConditionalRuleResponse)
async def add_conditional_rule(
    doc_type: str,
    q_id: uuid.UUID,
    conditional_logic: ConditionalLogic,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Add conditional logic rule to a question (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get question
    question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id
    ).first()
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    
    # Validate that condition question exists
    condition_question = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.question_id == conditional_logic.condition_question_id,
        FormQuestion.is_active == True
    ).first()
    
    if not condition_question:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Condition question '{conditional_logic.condition_question_id}' not found"
        )
    
    # Create conditional rule
    rule = ConditionalRule(
        form_schema_id=schema.id,
        question_id=question.id,
        condition_question_id=conditional_logic.condition_question_id,
        operator=conditional_logic.operator,
        condition_value=conditional_logic.value,
        action=conditional_logic.action,
        target_question_ids=conditional_logic.target_question_ids,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(rule)
    db.commit()
    db.refresh(rule)
    
    return ConditionalRuleResponse(
        id=rule.id,
        question_id=question.question_id,
        condition_question_id=rule.condition_question_id,
        operator=rule.operator,
        value=rule.condition_value,
        action=rule.action,
        target_question_ids=rule.target_question_ids,
        is_active=rule.is_active
    )


@router.put("/{doc_type}/conditional/{rule_id}", response_model=ConditionalRuleResponse)
async def update_conditional_rule(
    doc_type: str,
    rule_id: uuid.UUID,
    conditional_logic: ConditionalLogic,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update conditional logic rule (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get rule
    rule = db.query(ConditionalRule).filter(
        ConditionalRule.id == rule_id,
        ConditionalRule.form_schema_id == schema.id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conditional rule not found"
        )
    
    # Update rule
    rule.condition_question_id = conditional_logic.condition_question_id
    rule.operator = conditional_logic.operator
    rule.condition_value = conditional_logic.value
    rule.action = conditional_logic.action
    rule.target_question_ids = conditional_logic.target_question_ids
    rule.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(rule)
    
    # Get question for response
    question = db.query(FormQuestion).filter(FormQuestion.id == rule.question_id).first()
    
    return ConditionalRuleResponse(
        id=rule.id,
        question_id=question.question_id if question else "",
        condition_question_id=rule.condition_question_id,
        operator=rule.operator,
        value=rule.condition_value,
        action=rule.action,
        target_question_ids=rule.target_question_ids,
        is_active=rule.is_active
    )


@router.delete("/{doc_type}/conditional/{rule_id}", response_model=MessageResponse)
async def delete_conditional_rule(
    doc_type: str,
    rule_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete conditional logic rule (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get rule
    rule = db.query(ConditionalRule).filter(
        ConditionalRule.id == rule_id,
        ConditionalRule.form_schema_id == schema.id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Conditional rule not found"
        )
    
    # Soft delete by marking as inactive
    rule.is_active = False
    rule.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    
    return MessageResponse(
        message="Conditional rule deleted successfully",
        details={"rule_id": str(rule_id)}
    )
