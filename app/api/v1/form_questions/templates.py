"""
Form Questions Templates API endpoints.
Handles reusable question templates, categories, and template-based question creation.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, QuestionTemplate
from app.schemas.form_schema import (
    QuestionCreate,
    QuestionResponse,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for template management"""
    return True


def get_form_schema_or_404(doc_type: str, tenant_id: uuid.UUID, db: Session) -> FormSchema:
    """Get form schema or raise 404"""
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == tenant_id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    return schema


# Template schemas
from pydantic import BaseModel, Field


class QuestionTemplateCreate(BaseModel):
    """Create question template request"""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    category: Optional[str] = Field(None, max_length=100)
    question_type: str = Field(...)
    template_data: Dict[str, Any] = Field(...)
    default_validation_rules: List[Dict[str, Any]] = Field(default_factory=list)
    document_types: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)


class QuestionTemplateUpdate(BaseModel):
    """Update question template request"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    category: Optional[str] = Field(None, max_length=100)
    template_data: Optional[Dict[str, Any]] = Field(None)
    default_validation_rules: Optional[List[Dict[str, Any]]] = Field(None)
    document_types: Optional[List[str]] = Field(None)
    tags: Optional[List[str]] = Field(None)
    is_active: Optional[bool] = Field(None)


class QuestionTemplateResponse(BaseModel):
    """Question template response"""
    id: uuid.UUID
    name: str
    description: Optional[str]
    category: Optional[str]
    question_type: str
    template_data: Dict[str, Any]
    default_validation_rules: List[Dict[str, Any]]
    document_types: List[str]
    tags: List[str]
    usage_count: int
    last_used_at: Optional[datetime]
    is_active: bool
    created_by: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


@router.get("/templates", response_model=List[QuestionTemplateResponse])
async def get_question_templates(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    category: Optional[str] = Query(None, description="Filter by category"),
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    document_type: Optional[str] = Query(None, description="Filter by applicable document type"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get question templates with filtering and pagination
    """
    # Build query
    query = db.query(QuestionTemplate).filter(
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.is_active == True
    )
    
    # Apply filters
    if category:
        query = query.filter(QuestionTemplate.category == category)
    
    if question_type:
        query = query.filter(QuestionTemplate.question_type == question_type)
    
    if document_type:
        query = query.filter(QuestionTemplate.document_types.contains([document_type]))
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                QuestionTemplate.name.ilike(search_term),
                QuestionTemplate.description.ilike(search_term)
            )
        )
    
    # Apply ordering and pagination
    templates = query.order_by(
        QuestionTemplate.category.nulls_last(),
        QuestionTemplate.usage_count.desc(),
        QuestionTemplate.name
    ).offset(offset).limit(limit).all()
    
    return templates


@router.get("/templates/{template_id}", response_model=QuestionTemplateResponse)
async def get_question_template(
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific question template by ID
    """
    template = db.query(QuestionTemplate).filter(
        QuestionTemplate.id == template_id,
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question template not found"
        )
    
    return template


@router.post("/templates", response_model=QuestionTemplateResponse)
async def create_question_template(
    template_data: QuestionTemplateCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create new question template (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Check if template name already exists
    existing_template = db.query(QuestionTemplate).filter(
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.name == template_data.name
    ).first()
    
    if existing_template:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Template with name '{template_data.name}' already exists"
        )
    
    # Create template
    template = QuestionTemplate(
        name=template_data.name,
        description=template_data.description,
        category=template_data.category,
        question_type=template_data.question_type,
        template_data=template_data.template_data,
        default_validation_rules=template_data.default_validation_rules,
        document_types=template_data.document_types,
        tags=template_data.tags,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(template)
    db.commit()
    db.refresh(template)
    
    return template


@router.put("/templates/{template_id}", response_model=QuestionTemplateResponse)
async def update_question_template(
    template_id: uuid.UUID,
    template_data: QuestionTemplateUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update question template (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get template
    template = db.query(QuestionTemplate).filter(
        QuestionTemplate.id == template_id,
        QuestionTemplate.tenant_id == current_tenant.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question template not found"
        )
    
    # Update fields
    update_data = template_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    template.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(template)
    
    return template


@router.delete("/templates/{template_id}", response_model=MessageResponse)
async def delete_question_template(
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete question template (admin only) - soft delete
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get template
    template = db.query(QuestionTemplate).filter(
        QuestionTemplate.id == template_id,
        QuestionTemplate.tenant_id == current_tenant.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question template not found"
        )
    
    # Soft delete
    template.is_active = False
    template.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    
    return MessageResponse(
        message="Question template deleted successfully",
        details={"template_id": str(template_id)}
    )


@router.post("/{doc_type}/questions/from-template/{template_id}", response_model=QuestionResponse)
async def create_question_from_template(
    doc_type: str,
    template_id: uuid.UUID,
    question_id: str = Query(..., description="Unique question ID for the new question"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create a question from a template (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get template
    template = db.query(QuestionTemplate).filter(
        QuestionTemplate.id == template_id,
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question template not found"
        )
    
    # Check if question ID already exists
    existing_question = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.question_id == question_id
    ).first()
    
    if existing_question:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Question with ID '{question_id}' already exists"
        )
    
    # Create question from template
    template_data = template.template_data
    question = FormQuestion(
        form_schema_id=schema.id,
        question_id=question_id,
        question_text=template_data.get("question_text", ""),
        question_type=template.question_type,
        description=template_data.get("description"),
        placeholder=template_data.get("placeholder"),
        options=template_data.get("options", []),
        validation_rules=template.default_validation_rules,
        question_metadata=template_data.get("metadata", {}),
        is_required=template_data.get("is_required", False),
        order_index=template_data.get("order_index", 0),
        section=template_data.get("section"),
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(question)
    
    # Update template usage
    template.usage_count += 1
    template.last_used_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(question)
    
    return question


@router.get("/templates/categories")
async def get_template_categories(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get all template categories
    """
    categories = db.query(QuestionTemplate.category).filter(
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.is_active == True,
        QuestionTemplate.category.isnot(None)
    ).distinct().all()
    
    return {"categories": [cat[0] for cat in categories if cat[0]]}


@router.get("/templates/stats")
async def get_template_stats(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get template usage statistics (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get template statistics
    total_templates = db.query(QuestionTemplate).filter(
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.is_active == True
    ).count()
    
    most_used = db.query(QuestionTemplate).filter(
        QuestionTemplate.tenant_id == current_tenant.id,
        QuestionTemplate.is_active == True
    ).order_by(QuestionTemplate.usage_count.desc()).limit(5).all()
    
    return {
        "total_templates": total_templates,
        "most_used_templates": most_used
    }
