"""
Form Questions Validation API endpoints.
Handles question validation logic, rules management, and form data validation.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any, Union
import uuid
import re
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, FormValidationLog
from app.schemas.form_schema import (
    FormValidationRequest,
    FormValidationResponse,
    ValidationError,
    ValidationRuleSchema,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for validation management"""
    return True


def get_form_schema_or_404(doc_type: str, tenant_id: uuid.UUID, db: Session) -> FormSchema:
    """Get form schema or raise 404"""
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == tenant_id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    return schema


def validate_question_value(question: FormQuestion, value: Any) -> List[ValidationError]:
    """Validate a single question value against its validation rules"""
    errors = []
    
    # Skip validation if value is None/empty and question is not required
    if (value is None or value == "") and not question.is_required:
        return errors
    
    # Required field validation
    if question.is_required and (value is None or value == ""):
        errors.append(ValidationError(
            question_id=question.question_id,
            error_type="required",
            message="This field is required",
            value=value
        ))
        return errors  # Don't continue with other validations if required field is empty
    
    # Apply validation rules
    for rule_data in question.validation_rules:
        rule = ValidationRuleSchema(**rule_data)
        error = validate_rule(question, value, rule)
        if error:
            errors.append(error)
    
    return errors


def validate_rule(question: FormQuestion, value: Any, rule: ValidationRuleSchema) -> Optional[ValidationError]:
    """Validate a single rule against a value"""
    try:
        if rule.rule_type == "min_length" and isinstance(value, str):
            if len(value) < rule.value:
                return ValidationError(
                    question_id=question.question_id,
                    error_type="min_length",
                    message=rule.message or f"Minimum length is {rule.value} characters",
                    value=value
                )
        
        elif rule.rule_type == "max_length" and isinstance(value, str):
            if len(value) > rule.value:
                return ValidationError(
                    question_id=question.question_id,
                    error_type="max_length",
                    message=rule.message or f"Maximum length is {rule.value} characters",
                    value=value
                )
        
        elif rule.rule_type == "min_value" and isinstance(value, (int, float)):
            if value < rule.value:
                return ValidationError(
                    question_id=question.question_id,
                    error_type="min_value",
                    message=rule.message or f"Minimum value is {rule.value}",
                    value=value
                )
        
        elif rule.rule_type == "max_value" and isinstance(value, (int, float)):
            if value > rule.value:
                return ValidationError(
                    question_id=question.question_id,
                    error_type="max_value",
                    message=rule.message or f"Maximum value is {rule.value}",
                    value=value
                )
        
        elif rule.rule_type == "pattern" and isinstance(value, str):
            if not re.match(rule.value, value):
                return ValidationError(
                    question_id=question.question_id,
                    error_type="pattern",
                    message=rule.message or "Value does not match required pattern",
                    value=value
                )
        
        elif rule.rule_type == "email_format" and isinstance(value, str):
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, value):
                return ValidationError(
                    question_id=question.question_id,
                    error_type="email_format",
                    message=rule.message or "Please enter a valid email address",
                    value=value
                )
        
        elif rule.rule_type == "url_format" and isinstance(value, str):
            url_pattern = r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$'
            if not re.match(url_pattern, value):
                return ValidationError(
                    question_id=question.question_id,
                    error_type="url_format",
                    message=rule.message or "Please enter a valid URL",
                    value=value
                )
        
        # Add more validation rules as needed
        
    except Exception as e:
        return ValidationError(
            question_id=question.question_id,
            error_type="validation_error",
            message=f"Validation error: {str(e)}",
            value=value
        )
    
    return None


@router.post("/{doc_type}/validate", response_model=FormValidationResponse)
async def validate_form_data(
    doc_type: str,
    validation_request: FormValidationRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Validate form data against schema and question rules
    """
    start_time = datetime.now(timezone.utc)
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get all questions for this schema
    questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).all()
    
    # Create question lookup
    question_lookup = {q.question_id: q for q in questions}
    
    errors = []
    warnings = []
    
    # Validate each question
    for question in questions:
        value = validation_request.form_data.get(question.question_id)
        question_errors = validate_question_value(question, value)
        errors.extend(question_errors)
    
    # Check for unknown fields
    for field_id in validation_request.form_data.keys():
        if field_id not in question_lookup:
            warnings.append(ValidationError(
                question_id=field_id,
                error_type="unknown_field",
                message=f"Unknown field: {field_id}",
                value=validation_request.form_data[field_id]
            ))
    
    # Calculate validation time
    end_time = datetime.now(timezone.utc)
    validation_time_ms = int((end_time - start_time).total_seconds() * 1000)
    
    # Log validation attempt
    validation_log = FormValidationLog(
        form_schema_id=schema.id,
        form_data=validation_request.form_data,
        validation_result={
            "errors": [error.dict() for error in errors],
            "warnings": [warning.dict() for warning in warnings]
        },
        is_valid=len(errors) == 0,
        error_count=len(errors),
        warning_count=len(warnings),
        validation_time_ms=validation_time_ms,
        tenant_id=current_tenant.id
    )
    
    db.add(validation_log)
    db.commit()
    
    return FormValidationResponse(
        is_valid=len(errors) == 0,
        errors=errors,
        warnings=warnings,
        validation_time_ms=validation_time_ms,
        document_type=doc_type
    )


@router.get("/{doc_type}/validation-rules", response_model=Dict[str, List[ValidationRuleSchema]])
async def get_validation_rules(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get all validation rules for questions in a document type
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get all questions with validation rules
    questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).all()
    
    validation_rules = {}
    for question in questions:
        if question.validation_rules:
            validation_rules[question.question_id] = [
                ValidationRuleSchema(**rule) for rule in question.validation_rules
            ]
    
    return validation_rules


@router.get("/{doc_type}/validation-logs")
async def get_validation_logs(
    doc_type: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    is_valid: Optional[bool] = Query(None, description="Filter by validation result"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get validation logs for a document type (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Build query
    query = db.query(FormValidationLog).filter(
        FormValidationLog.form_schema_id == schema.id
    )
    
    if is_valid is not None:
        query = query.filter(FormValidationLog.is_valid == is_valid)
    
    # Get total count
    total_count = query.count()
    
    # Apply ordering and pagination
    logs = query.order_by(FormValidationLog.created_at.desc()).offset(offset).limit(limit).all()
    
    return {
        "logs": logs,
        "total_count": total_count,
        "document_type": doc_type
    }
