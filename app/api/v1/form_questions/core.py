"""
Form Questions Core API endpoints.
Handles basic CRUD operations for form questions with proper authentication and authorization.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_, func
from typing import List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, ConditionalRule
from app.schemas.form_schema import (
    QuestionCreate,
    QuestionUpdate,
    QuestionResponse,
    QuestionListResponse,
    MessageResponse,
    BulkQuestionOperation,
    BulkOperationResponse,
    QuestionOrderUpdate
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for question management"""
    # In a real implementation, check user roles/permissions
    return True


def get_form_schema_or_404(doc_type: str, tenant_id: uuid.UUID, db: Session) -> FormSchema:
    """Get form schema or raise 404"""
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == tenant_id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    return schema


@router.get("/{doc_type}/questions", response_model=QuestionListResponse)
async def get_questions(
    doc_type: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    section: Optional[str] = Query(None, description="Filter by section"),
    search: Optional[str] = Query(None, description="Search in question text"),
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    is_required: Optional[bool] = Query(None, description="Filter by required status"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get questions for document type with filtering and pagination
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Build query
    query = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    )
    
    # Apply filters
    if section:
        query = query.filter(FormQuestion.section == section)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                FormQuestion.question_text.ilike(search_term),
                FormQuestion.description.ilike(search_term),
                FormQuestion.question_id.ilike(search_term)
            )
        )
    
    if question_type:
        query = query.filter(FormQuestion.question_type == question_type)
        
    if is_required is not None:
        query = query.filter(FormQuestion.is_required == is_required)
    
    # Get total count
    total_count = query.count()
    
    # Apply ordering and pagination
    questions = query.order_by(
        FormQuestion.section.nulls_last(),
        FormQuestion.order_index,
        FormQuestion.created_at
    ).offset(offset).limit(limit).all()
    
    # Get unique sections
    sections = db.query(FormQuestion.section).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True,
        FormQuestion.section.isnot(None)
    ).distinct().all()
    section_list = [s[0] for s in sections if s[0]]
    
    return QuestionListResponse(
        questions=questions,
        total_count=total_count,
        document_type=doc_type,
        sections=section_list
    )


@router.get("/{doc_type}/questions/{q_id}", response_model=QuestionResponse)
async def get_question(
    doc_type: str,
    q_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific question by ID
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get question
    question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).first()
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    
    return question


@router.post("/{doc_type}/questions", response_model=QuestionResponse)
async def create_question(
    doc_type: str,
    question_data: QuestionCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create new question (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Check if question ID already exists
    existing_question = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.question_id == question_data.question_id
    ).first()
    
    if existing_question:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Question with ID '{question_data.question_id}' already exists"
        )
    
    # Create question
    question = FormQuestion(
        form_schema_id=schema.id,
        question_id=question_data.question_id,
        question_text=question_data.question_text,
        question_type=question_data.question_type,
        description=question_data.description,
        placeholder=question_data.placeholder,
        options=[opt.dict() for opt in question_data.options],
        validation_rules=[rule.dict() for rule in question_data.validation_rules],
        question_metadata=question_data.metadata,
        is_required=question_data.is_required,
        order_index=question_data.order_index,
        section=question_data.section,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(question)
    db.flush()  # Get the question ID
    
    # Create conditional logic rules
    for logic in question_data.conditional_logic:
        rule = ConditionalRule(
            form_schema_id=schema.id,
            question_id=question.id,
            condition_question_id=logic.condition_question_id,
            operator=logic.operator,
            condition_value=logic.value,
            action=logic.action,
            target_question_ids=logic.target_question_ids,
            created_by=current_user.id,
            tenant_id=current_tenant.id
        )
        db.add(rule)
    
    db.commit()
    db.refresh(question)

    return question


@router.put("/{doc_type}/questions/{q_id}", response_model=QuestionResponse)
async def update_question(
    doc_type: str,
    q_id: uuid.UUID,
    question_data: QuestionUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update question (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )

    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)

    # Get question
    question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id
    ).first()

    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )

    # Update fields
    update_data = question_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "options" and value is not None:
            setattr(question, field, [opt.dict() for opt in value])
        elif field == "validation_rules" and value is not None:
            setattr(question, field, [rule.dict() for rule in value])
        elif field == "conditional_logic" and value is not None:
            # Update conditional logic rules
            # First, delete existing rules
            db.query(ConditionalRule).filter(
                ConditionalRule.question_id == question.id
            ).delete()

            # Create new rules
            for logic in value:
                rule = ConditionalRule(
                    form_schema_id=schema.id,
                    question_id=question.id,
                    condition_question_id=logic.condition_question_id,
                    operator=logic.operator,
                    condition_value=logic.value,
                    action=logic.action,
                    target_question_ids=logic.target_question_ids,
                    created_by=current_user.id,
                    tenant_id=current_tenant.id
                )
                db.add(rule)
        else:
            setattr(question, field, value)

    question.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(question)

    return question


@router.delete("/{doc_type}/questions/{q_id}", response_model=MessageResponse)
async def delete_question(
    doc_type: str,
    q_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete question (admin only) - soft delete by marking as inactive
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )

    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)

    # Get question
    question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id
    ).first()

    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )

    # Soft delete by marking as inactive
    question.is_active = False
    question.updated_at = datetime.now(timezone.utc)

    # Also deactivate related conditional rules
    db.query(ConditionalRule).filter(
        ConditionalRule.question_id == question.id
    ).update({"is_active": False})

    db.commit()

    return MessageResponse(
        message="Question deleted successfully",
        details={"question_id": str(q_id)}
    )


@router.post("/{doc_type}/questions/{q_id}/duplicate", response_model=QuestionResponse)
async def duplicate_question(
    doc_type: str,
    q_id: uuid.UUID,
    new_question_id: str = Query(..., description="New question ID for the duplicate"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Duplicate an existing question (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )

    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)

    # Get original question
    original_question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).first()

    if not original_question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )

    # Check if new question ID already exists
    existing_question = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.question_id == new_question_id
    ).first()

    if existing_question:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Question with ID '{new_question_id}' already exists"
        )

    # Create duplicate question
    duplicate_question = FormQuestion(
        form_schema_id=schema.id,
        question_id=new_question_id,
        question_text=f"{original_question.question_text} (Copy)",
        question_type=original_question.question_type,
        description=original_question.description,
        placeholder=original_question.placeholder,
        options=original_question.options,
        validation_rules=original_question.validation_rules,
        question_metadata=original_question.question_metadata,
        is_required=original_question.is_required,
        order_index=original_question.order_index + 1,
        section=original_question.section,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )

    db.add(duplicate_question)
    db.commit()
    db.refresh(duplicate_question)

    return duplicate_question


@router.post("/{doc_type}/questions/bulk", response_model=BulkOperationResponse)
async def bulk_question_operations(
    doc_type: str,
    operation: BulkQuestionOperation,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Perform bulk operations on questions (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )

    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)

    successful = 0
    failed = 0
    errors = []

    for question_id in operation.question_ids:
        try:
            question = db.query(FormQuestion).filter(
                FormQuestion.form_schema_id == schema.id,
                FormQuestion.question_id == question_id
            ).first()

            if not question:
                errors.append({
                    "question_id": question_id,
                    "error": "Question not found"
                })
                failed += 1
                continue

            if operation.operation == "delete":
                question.is_active = False
                question.updated_at = datetime.now(timezone.utc)
            elif operation.operation == "activate":
                question.is_active = True
                question.updated_at = datetime.now(timezone.utc)
            elif operation.operation == "deactivate":
                question.is_active = False
                question.updated_at = datetime.now(timezone.utc)
            elif operation.operation == "update" and operation.data:
                for field, value in operation.data.items():
                    if hasattr(question, field):
                        setattr(question, field, value)
                question.updated_at = datetime.now(timezone.utc)

            successful += 1

        except Exception as e:
            errors.append({
                "question_id": question_id,
                "error": str(e)
            })
            failed += 1

    db.commit()

    return BulkOperationResponse(
        operation=operation.operation,
        total_requested=len(operation.question_ids),
        successful=successful,
        failed=failed,
        errors=errors
    )


@router.put("/{doc_type}/questions/order", response_model=MessageResponse)
async def update_question_order(
    doc_type: str,
    order_update: QuestionOrderUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update question display order (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )

    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)

    # Update question orders
    updated_count = 0
    for order_item in order_update.question_orders:
        question_id = order_item.get("question_id")
        order_index = order_item.get("order_index")

        if question_id and order_index is not None:
            question = db.query(FormQuestion).filter(
                FormQuestion.form_schema_id == schema.id,
                FormQuestion.question_id == question_id
            ).first()

            if question:
                question.order_index = order_index
                question.updated_at = datetime.now(timezone.utc)
                updated_count += 1

    db.commit()

    return MessageResponse(
        message="Question order updated successfully",
        details={"updated_count": updated_count}
    )


@router.get("/{doc_type}/questions/sections")
async def get_question_sections(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get all sections used in questions for a document type
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)

    # Get unique sections with question counts
    sections = db.query(
        FormQuestion.section,
        func.count(FormQuestion.id).label('question_count')
    ).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True,
        FormQuestion.section.isnot(None)
    ).group_by(FormQuestion.section).all()

    return {
        "document_type": doc_type,
        "sections": [
            {
                "name": section[0],
                "question_count": section[1]
            }
            for section in sections
        ]
    }
