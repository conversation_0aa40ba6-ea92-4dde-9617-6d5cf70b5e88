"""
Form Questions Analytics API endpoints.
Handles question usage analytics, validation metrics, and performance insights.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_, case
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timezone, timedelta
from enum import Enum

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, FormValidationLog, ConditionalRule
from app.schemas.form_schema import MessageResponse

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for analytics"""
    return True


def get_form_schema_or_404(doc_type: str, tenant_id: uuid.UUID, db: Session) -> FormSchema:
    """Get form schema or raise 404"""
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == tenant_id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    return schema


class AnalyticsPeriod(str, Enum):
    """Analytics time periods"""
    LAST_7_DAYS = "last_7_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    LAST_YEAR = "last_year"


def get_period_start_date(period: AnalyticsPeriod) -> datetime:
    """Get start date for analytics period"""
    now = datetime.now(timezone.utc)
    
    if period == AnalyticsPeriod.LAST_7_DAYS:
        return now - timedelta(days=7)
    elif period == AnalyticsPeriod.LAST_30_DAYS:
        return now - timedelta(days=30)
    elif period == AnalyticsPeriod.LAST_90_DAYS:
        return now - timedelta(days=90)
    elif period == AnalyticsPeriod.LAST_YEAR:
        return now - timedelta(days=365)
    else:
        return now - timedelta(days=30)  # Default to 30 days


@router.get("/{doc_type}/analytics/overview")
async def get_questions_overview(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get overview analytics for questions in a document type
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get basic question statistics
    total_questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).count()
    
    required_questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True,
        FormQuestion.is_required == True
    ).count()
    
    # Get question types distribution
    question_types = db.query(
        FormQuestion.question_type,
        func.count(FormQuestion.id).label('count')
    ).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).group_by(FormQuestion.question_type).all()
    
    # Get sections distribution
    sections = db.query(
        FormQuestion.section,
        func.count(FormQuestion.id).label('count')
    ).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True,
        FormQuestion.section.isnot(None)
    ).group_by(FormQuestion.section).all()
    
    # Get conditional rules count
    conditional_rules = db.query(ConditionalRule).filter(
        ConditionalRule.form_schema_id == schema.id,
        ConditionalRule.is_active == True
    ).count()
    
    return {
        "document_type": doc_type,
        "total_questions": total_questions,
        "required_questions": required_questions,
        "optional_questions": total_questions - required_questions,
        "conditional_rules": conditional_rules,
        "question_types": [{"type": qt[0], "count": qt[1]} for qt in question_types],
        "sections": [{"section": s[0], "count": s[1]} for s in sections]
    }


@router.get("/{doc_type}/analytics/validation")
async def get_validation_analytics(
    doc_type: str,
    period: AnalyticsPeriod = Query(AnalyticsPeriod.LAST_30_DAYS),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get validation analytics for a document type
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get period start date
    start_date = get_period_start_date(period)
    
    # Get validation statistics
    total_validations = db.query(FormValidationLog).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date
    ).count()
    
    successful_validations = db.query(FormValidationLog).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date,
        FormValidationLog.is_valid == True
    ).count()
    
    failed_validations = total_validations - successful_validations
    
    # Get average validation time
    avg_validation_time = db.query(
        func.avg(FormValidationLog.validation_time_ms)
    ).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date,
        FormValidationLog.validation_time_ms.isnot(None)
    ).scalar()
    
    # Get validation trends (daily)
    validation_trends = db.query(
        func.date(FormValidationLog.created_at).label('date'),
        func.count(FormValidationLog.id).label('total'),
        func.sum(case((FormValidationLog.is_valid == True, 1), else_=0)).label('successful'),
        func.sum(case((FormValidationLog.is_valid == False, 1), else_=0)).label('failed')
    ).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date
    ).group_by(func.date(FormValidationLog.created_at)).order_by('date').all()
    
    # Get most common validation errors
    error_stats = db.query(FormValidationLog).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date,
        FormValidationLog.is_valid == False
    ).all()
    
    # Process error statistics
    error_counts = {}
    for log in error_stats:
        if log.validation_result and 'errors' in log.validation_result:
            for error in log.validation_result['errors']:
                error_type = error.get('error_type', 'unknown')
                question_id = error.get('question_id', 'unknown')
                key = f"{question_id}:{error_type}"
                error_counts[key] = error_counts.get(key, 0) + 1
    
    # Sort errors by frequency
    top_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    
    return {
        "document_type": doc_type,
        "period": period,
        "summary": {
            "total_validations": total_validations,
            "successful_validations": successful_validations,
            "failed_validations": failed_validations,
            "success_rate": (successful_validations / total_validations * 100) if total_validations > 0 else 0,
            "avg_validation_time_ms": round(avg_validation_time, 2) if avg_validation_time else 0
        },
        "trends": [
            {
                "date": str(trend.date),
                "total": trend.total,
                "successful": trend.successful,
                "failed": trend.failed,
                "success_rate": (trend.successful / trend.total * 100) if trend.total > 0 else 0
            }
            for trend in validation_trends
        ],
        "top_errors": [
            {
                "question_error": error[0],
                "count": error[1]
            }
            for error in top_errors
        ]
    }


@router.get("/{doc_type}/analytics/questions")
async def get_question_analytics(
    doc_type: str,
    period: AnalyticsPeriod = Query(AnalyticsPeriod.LAST_30_DAYS),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get individual question analytics
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get period start date
    start_date = get_period_start_date(period)
    
    # Get all questions
    questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).all()
    
    question_analytics = []
    
    for question in questions:
        # Get validation logs that contain this question
        validation_logs = db.query(FormValidationLog).filter(
            FormValidationLog.form_schema_id == schema.id,
            FormValidationLog.created_at >= start_date
        ).all()
        
        # Analyze this question's performance
        total_submissions = 0
        error_count = 0
        warning_count = 0
        
        for log in validation_logs:
            # Check if this question was in the form data
            if log.form_data and question.question_id in log.form_data:
                total_submissions += 1
                
                # Check for errors related to this question
                if log.validation_result and 'errors' in log.validation_result:
                    for error in log.validation_result['errors']:
                        if error.get('question_id') == question.question_id:
                            error_count += 1
                
                # Check for warnings related to this question
                if log.validation_result and 'warnings' in log.validation_result:
                    for warning in log.validation_result['warnings']:
                        if warning.get('question_id') == question.question_id:
                            warning_count += 1
        
        # Calculate success rate
        success_rate = ((total_submissions - error_count) / total_submissions * 100) if total_submissions > 0 else 0
        
        question_analytics.append({
            "question_id": question.question_id,
            "question_text": question.question_text,
            "question_type": question.question_type,
            "is_required": question.is_required,
            "section": question.section,
            "total_submissions": total_submissions,
            "error_count": error_count,
            "warning_count": warning_count,
            "success_rate": round(success_rate, 2)
        })
    
    # Sort by error rate (highest first)
    question_analytics.sort(key=lambda x: x['error_count'], reverse=True)
    
    return {
        "document_type": doc_type,
        "period": period,
        "questions": question_analytics
    }


@router.get("/{doc_type}/analytics/performance")
async def get_performance_analytics(
    doc_type: str,
    period: AnalyticsPeriod = Query(AnalyticsPeriod.LAST_30_DAYS),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get form performance analytics
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get period start date
    start_date = get_period_start_date(period)
    
    # Get performance metrics
    performance_stats = db.query(
        func.count(FormValidationLog.id).label('total_validations'),
        func.avg(FormValidationLog.validation_time_ms).label('avg_time'),
        func.min(FormValidationLog.validation_time_ms).label('min_time'),
        func.max(FormValidationLog.validation_time_ms).label('max_time'),
        func.percentile_cont(0.5).within_group(FormValidationLog.validation_time_ms).label('median_time'),
        func.percentile_cont(0.95).within_group(FormValidationLog.validation_time_ms).label('p95_time')
    ).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date,
        FormValidationLog.validation_time_ms.isnot(None)
    ).first()
    
    # Get hourly performance trends
    hourly_trends = db.query(
        func.extract('hour', FormValidationLog.created_at).label('hour'),
        func.count(FormValidationLog.id).label('count'),
        func.avg(FormValidationLog.validation_time_ms).label('avg_time')
    ).filter(
        FormValidationLog.form_schema_id == schema.id,
        FormValidationLog.created_at >= start_date,
        FormValidationLog.validation_time_ms.isnot(None)
    ).group_by(func.extract('hour', FormValidationLog.created_at)).order_by('hour').all()
    
    return {
        "document_type": doc_type,
        "period": period,
        "performance_summary": {
            "total_validations": performance_stats.total_validations or 0,
            "avg_validation_time_ms": round(performance_stats.avg_time, 2) if performance_stats.avg_time else 0,
            "min_validation_time_ms": performance_stats.min_time or 0,
            "max_validation_time_ms": performance_stats.max_time or 0,
            "median_validation_time_ms": round(performance_stats.median_time, 2) if performance_stats.median_time else 0,
            "p95_validation_time_ms": round(performance_stats.p95_time, 2) if performance_stats.p95_time else 0
        },
        "hourly_trends": [
            {
                "hour": int(trend.hour),
                "validation_count": trend.count,
                "avg_time_ms": round(trend.avg_time, 2) if trend.avg_time else 0
            }
            for trend in hourly_trends
        ]
    }


@router.get("/analytics/summary")
async def get_tenant_analytics_summary(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get tenant-wide form questions analytics summary
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get all schemas for tenant
    schemas = db.query(FormSchema).filter(
        FormSchema.tenant_id == current_tenant.id,
        FormSchema.is_active == True
    ).all()
    
    total_schemas = len(schemas)
    total_questions = 0
    total_validations = 0
    
    schema_stats = []
    
    for schema in schemas:
        # Count questions for this schema
        question_count = db.query(FormQuestion).filter(
            FormQuestion.form_schema_id == schema.id,
            FormQuestion.is_active == True
        ).count()
        
        # Count validations for this schema (last 30 days)
        start_date = datetime.now(timezone.utc) - timedelta(days=30)
        validation_count = db.query(FormValidationLog).filter(
            FormValidationLog.form_schema_id == schema.id,
            FormValidationLog.created_at >= start_date
        ).count()
        
        total_questions += question_count
        total_validations += validation_count
        
        schema_stats.append({
            "document_type": schema.document_type,
            "question_count": question_count,
            "validation_count": validation_count
        })
    
    return {
        "tenant_id": str(current_tenant.id),
        "summary": {
            "total_schemas": total_schemas,
            "total_questions": total_questions,
            "total_validations_last_30_days": total_validations
        },
        "schema_stats": schema_stats
    }
