"""
Form Questions API Module

This module provides comprehensive form question management APIs with modular structure:
- Core CRUD operations for questions
- Validation logic and rules
- Conditional logic management
- Question templates
- Analytics and usage tracking

All endpoints require proper authentication and follow tenant isolation.
"""

from fastapi import APIRouter

# Import all modular routers
from .core import router as core_router
from .validation import router as validation_router
from .conditional import router as conditional_router
from .templates import router as templates_router
from .analytics import router as analytics_router

# Create main router that combines all sub-routers
router = APIRouter()

# Include all sub-routers with appropriate prefixes and tags
router.include_router(
    core_router,
    tags=["Form Questions - Core"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    validation_router,
    prefix="/validation",
    tags=["Form Questions - Validation"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    conditional_router,
    prefix="/conditional",
    tags=["Form Questions - Conditional Logic"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    templates_router,
    prefix="/templates",
    tags=["Form Questions - Templates"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    analytics_router,
    prefix="/analytics",
    tags=["Form Questions - Analytics"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

__all__ = [
    "router",
    "core_router",
    "validation_router",
    "conditional_router",
    "templates_router",
    "analytics_router"
]
