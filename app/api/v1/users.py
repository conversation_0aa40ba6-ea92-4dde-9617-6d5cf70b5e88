from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import verify_password, get_password_hash
from app.api.deps import get_current_user
from app.models.user import User
from app.schemas.user import (
    UserResponse,
    UserProfileUpdate,
    ChangePasswordRequest,
    UserPreferences,
    UserPreferencesUpdate,
    MessageResponse
)

router = APIRouter()

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information
    """
    return current_user

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_user)
):
    """
    Get user profile
    """
    return current_user

@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    profile_update: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update user profile
    """
    # Check if email is being updated and if it already exists
    if profile_update.email and profile_update.email != current_user.email:
        existing_user = db.query(User).filter(
            User.email == profile_update.email,
            User.id != current_user.id
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        current_user.email = profile_update.email

    if profile_update.full_name:
        current_user.full_name = profile_update.full_name

    db.commit()
    db.refresh(current_user)

    return current_user

@router.put("/password", response_model=MessageResponse)
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Change user password
    """
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )

    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()

    return {"message": "Password updated successfully"}

@router.get("/preferences", response_model=UserPreferences)
async def get_user_preferences(
    current_user: User = Depends(get_current_user)
):
    """
    Get user preferences
    """
    preferences = current_user.preferences or {
        "theme": "light",
        "language": "en",
        "notifications": {
            "email_notifications": True,
            "push_notifications": True,
            "marketing_emails": False
        },
        "timezone": "UTC"
    }
    return UserPreferences(**preferences)

@router.put("/preferences", response_model=UserPreferences)
async def update_user_preferences(
    preferences_update: UserPreferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update user preferences
    """
    # Get current preferences or default
    current_preferences = current_user.preferences or {
        "theme": "light",
        "language": "en",
        "notifications": {
            "email_notifications": True,
            "push_notifications": True,
            "marketing_emails": False
        },
        "timezone": "UTC"
    }

    # Update only provided fields
    if preferences_update.theme is not None:
        current_preferences["theme"] = preferences_update.theme
    if preferences_update.language is not None:
        current_preferences["language"] = preferences_update.language
    if preferences_update.notifications is not None:
        current_preferences["notifications"].update(preferences_update.notifications)
    if preferences_update.timezone is not None:
        current_preferences["timezone"] = preferences_update.timezone

    current_user.preferences = current_preferences
    db.commit()
    db.refresh(current_user)

    return UserPreferences(**current_preferences)

@router.delete("/account", response_model=MessageResponse)
async def delete_user_account(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete user account
    Note: This is a soft delete - marks user as inactive
    For hard delete, you would need to handle cascading deletes
    for related data (PRDs, etc.)
    """
    # Soft delete - mark user as inactive
    current_user.is_active = False
    db.commit()

    # TODO: For complete account deletion, you might want to:
    # - Delete or anonymize related data (PRDs, etc.)
    # - Send confirmation email
    # - Add a grace period before permanent deletion
    # - Handle tenant cleanup if user is the only admin

    return {"message": "Account has been deactivated successfully"}