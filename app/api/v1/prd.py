from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.schemas.prd import PRDCreate, PRDResponse, PRDListResponse
from app.models.prd import PRD
from app.models.user import User
from app.models.tenant import Tenant
from app.services.anthropic_service import anthropic_service

router = APIRouter()

@router.post("/generate", response_model=PRDResponse)
async def generate_prd(
    prd_data: PRDCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    try:
        # Generate PRD content using Anthropic
        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name
        }
        
        content = await anthropic_service.generate_prd(
            prd_data.form_data, 
            user_context
        )
        
        # Extract title from content or use provided title
        title = prd_data.title or prd_data.form_data.question1[:50] + "..."
        
        # Save to database
        db_prd = PRD(
            title=title,
            content=content,
            form_data=prd_data.form_data.dict(),
            user_id=current_user.id,
            tenant_id=current_tenant.id
        )
        
        db.add(db_prd)
        db.commit()
        db.refresh(db_prd)
        
        return db_prd
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate PRD: {str(e)}"
        )

@router.get("/", response_model=List[PRDListResponse])
async def list_prds(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    prds = db.query(PRD).filter(
        PRD.tenant_id == current_tenant.id
    ).order_by(PRD.created_at.desc()).all()
    
    return prds

@router.get("/{prd_id}", response_model=PRDResponse)
async def get_prd(
    prd_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    prd = db.query(PRD).filter(
        PRD.id == prd_id,
        PRD.tenant_id == current_tenant.id
    ).first()
    
    if not prd:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="PRD not found"
        )
    
    return prd

@router.delete("/{prd_id}")
async def delete_prd(
    prd_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    prd = db.query(PRD).filter(
        PRD.id == prd_id,
        PRD.tenant_id == current_tenant.id
    ).first()
    
    if not prd:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="PRD not found"
        )
    
    db.delete(prd)
    db.commit()
    
    return {"message": "PRD deleted successfully"}