"""
Document refinement API endpoints.
Handles document refinement operations: section refinement, component refinement, suggestions, custom refinement.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import Dict, List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document, DocumentRefinementJob
from app.models.document_type import DocumentType
from app.schemas.document import (
    SectionRefinementRequest,
    ComponentRefinementRequest,
    CustomRefinementRequest,
    RefinementOptionsResponse,
    RefinementSuggestionsResponse,
    ApplySuggestionsRequest,
    RefinementJobResponse,
    RefinementJobListResponse,
    RefinementHistoryResponse,
    RefinementJob,
    MessageResponse
)
from app.services.anthropic_service import anthropic_service
from app.api.v1.document_utils import (
    extract_section_content,
    extract_component_content,
    extract_document_sections,
    extract_document_components
)

router = APIRouter()


def _convert_job_to_schema(job: DocumentRefinementJob) -> RefinementJob:
    """Convert DocumentRefinementJob model to RefinementJob schema"""
    return RefinementJob(
        job_id=job.id,
        status=job.status,
        progress=job.progress,
        refinement_type=job.refinement_type,
        target=job.target,
        started_at=job.started_at,
        estimated_completion=job.estimated_completion,
        error_message=job.error_message
    )


@router.post("/{doc_id}/refine/section/{section_name}", response_model=RefinementJobResponse)
async def refine_section(
    doc_id: uuid.UUID,
    section_name: str,
    refinement_request: SectionRefinementRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Refine specific section of document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to refine document"
        )

    try:
        # Create refinement job
        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="section",
            target=section_name,
            refinement_type=refinement_request.refinement_type,
            instructions=refinement_request.instructions,
            parameters={
                "target_length": refinement_request.target_length,
                "tone": refinement_request.tone
            },
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        # Extract section content
        section_content = extract_section_content(document.content, section_name)
        if not section_content:
            job.status = "failed"
            job.error_message = f"Section '{section_name}' not found in document"
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Section '{section_name}' not found in document"
            )

        job.original_content = section_content

        # Perform refinement using AI
        refined_content = await anthropic_service.refine_section(
            section_content,
            refinement_request.refinement_type,
            refinement_request.instructions,
            {
                "target_length": refinement_request.target_length,
                "tone": refinement_request.tone,
                "section_name": section_name
            }
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)

        db.commit()

        return RefinementJobResponse(
            job=_convert_job_to_schema(job),
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refine section: {str(e)}"
        )


@router.post("/{doc_id}/refine/component/{component_id}", response_model=RefinementJobResponse)
async def refine_component(
    doc_id: uuid.UUID,
    component_id: str,
    refinement_request: ComponentRefinementRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Refine specific component of document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to refine document"
        )

    try:
        # Create refinement job
        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="component",
            target=component_id,
            refinement_type=refinement_request.refinement_type,
            instructions=refinement_request.instructions,
            parameters=refinement_request.parameters or {},
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        # Extract component content
        component_content = extract_component_content(document.content, component_id)
        if not component_content:
            job.status = "failed"
            job.error_message = f"Component '{component_id}' not found in document"
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Component '{component_id}' not found in document"
            )

        job.original_content = component_content

        # Perform refinement using AI
        refined_content = await anthropic_service.refine_component(
            component_content,
            refinement_request.refinement_type,
            refinement_request.instructions,
            refinement_request.parameters or {}
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)

        db.commit()

        return RefinementJobResponse(
            job=_convert_job_to_schema(job),
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to refine component: {str(e)}"
        )


@router.get("/{doc_id}/refinement-options", response_model=RefinementOptionsResponse)
async def get_refinement_options(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get available refinement options for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Get document type for specific refinement options
    document_type = db.query(DocumentType).filter(
        DocumentType.id == document.document_type_id
    ).first()

    # Extract sections and components from document
    sections = extract_document_sections(document.content)
    components = extract_document_components(document.content)

    # Get available refinements from document type
    available_refinements = []
    if document_type and document_type.refinement_options:
        refinement_config = document_type.refinement_options

        # Add section refinements
        for refinement in refinement_config.get("section_refinements", []):
            available_refinements.append({
                "id": refinement["id"],
                "name": refinement["name"],
                "description": refinement["description"],
                "type": "section",
                "applicable_to": sections,
                "parameters": refinement.get("parameters", {})
            })

        # Add component refinements
        for refinement in refinement_config.get("component_refinements", []):
            available_refinements.append({
                "id": refinement["id"],
                "name": refinement["name"],
                "description": refinement["description"],
                "type": "component",
                "applicable_to": [comp["id"] for comp in components],
                "parameters": refinement.get("parameters", {})
            })

        # Add global refinements
        for refinement in refinement_config.get("global_refinements", []):
            available_refinements.append({
                "id": refinement["id"],
                "name": refinement["name"],
                "description": refinement["description"],
                "type": "global",
                "applicable_to": ["document"],
                "parameters": refinement.get("parameters", {})
            })

    # Add default refinements if none configured
    if not available_refinements:
        available_refinements = [
            {
                "id": "expand",
                "name": "Expand Content",
                "description": "Add more detail and examples",
                "type": "section",
                "applicable_to": sections,
                "parameters": {}
            },
            {
                "id": "clarify",
                "name": "Clarify Content",
                "description": "Make content clearer and easier to understand",
                "type": "section",
                "applicable_to": sections,
                "parameters": {}
            },
            {
                "id": "improve_tone",
                "name": "Improve Tone",
                "description": "Adjust tone and style",
                "type": "global",
                "applicable_to": ["document"],
                "parameters": {"tone_options": ["professional", "casual", "technical"]}
            }
        ]

    return RefinementOptionsResponse(
        available_refinements=available_refinements,
        document_sections=sections,
        document_components=components
    )


@router.post("/{doc_id}/refine/custom", response_model=RefinementJobResponse)
async def custom_refinement(
    doc_id: uuid.UUID,
    refinement_request: CustomRefinementRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Apply custom refinement to document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to refine document"
        )

    try:
        # Create refinement job
        target = ",".join(refinement_request.target_sections) if refinement_request.target_sections else "document"

        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="custom",
            target=target,
            refinement_type=refinement_request.refinement_type,
            instructions=refinement_request.instructions,
            parameters={
                "target_sections": refinement_request.target_sections,
                "preserve_formatting": refinement_request.preserve_formatting
            },
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        job.original_content = document.content

        # Perform custom refinement using AI
        refined_content = await anthropic_service.custom_refinement(
            document.content,
            refinement_request.instructions,
            {
                "target_sections": refinement_request.target_sections,
                "preserve_formatting": refinement_request.preserve_formatting,
                "document_type": document.document_type_id
            }
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)

        db.commit()

        return RefinementJobResponse(
            job=_convert_job_to_schema(job),
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply custom refinement: {str(e)}"
        )


@router.get("/{doc_id}/refinement-suggestions", response_model=RefinementSuggestionsResponse)
async def get_refinement_suggestions(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get AI-generated refinement suggestions
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    try:
        # Generate suggestions using AI
        suggestions = await anthropic_service.generate_refinement_suggestions(
            document.content,
            document.form_data,
            {
                "document_type": document.document_type_id,
                "current_status": document.status
            }
        )

        return RefinementSuggestionsResponse(
            suggestions=suggestions,
            total_suggestions=len(suggestions)
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate suggestions: {str(e)}"
        )


@router.post("/{doc_id}/apply-suggestions", response_model=RefinementJobResponse)
async def apply_suggestions(
    doc_id: uuid.UUID,
    apply_request: ApplySuggestionsRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Apply selected refinement suggestions
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to apply suggestions"
        )

    try:
        # Create refinement job
        job = DocumentRefinementJob(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            job_type="suggestions",
            target="document",
            refinement_type="apply_suggestions",
            instructions=apply_request.custom_instructions,
            parameters={
                "suggestion_ids": apply_request.suggestion_ids
            },
            status="processing"
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        job.original_content = document.content

        # Apply suggestions using AI
        refined_content = await anthropic_service.apply_suggestions(
            document.content,
            apply_request.suggestion_ids,
            apply_request.custom_instructions
        )

        job.refined_content = refined_content
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)

        db.commit()

        return RefinementJobResponse(
            job=_convert_job_to_schema(job),
            result=refined_content
        )

    except Exception as e:
        if 'job' in locals():
            job.status = "failed"
            job.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply suggestions: {str(e)}"
        )


@router.get("/{doc_id}/refinement-status/{job_id}", response_model=RefinementJobResponse)
async def get_refinement_status(
    doc_id: uuid.UUID,
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Check refinement job progress
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    job = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.id == job_id,
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.tenant_id == current_tenant.id
    ).first()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Refinement job not found"
        )

    return RefinementJobResponse(
        job=_convert_job_to_schema(job),
        result=job.refined_content if job.status == "completed" else None
    )


@router.post("/{doc_id}/refinement/cancel/{job_id}", response_model=MessageResponse)
async def cancel_refinement(
    doc_id: uuid.UUID,
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Cancel refinement job
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    job = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.id == job_id,
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.user_id == current_user.id
    ).first()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Refinement job not found"
        )

    if job.status in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel job with status: {job.status}"
        )

    job.status = "cancelled"
    job.cancelled_at = datetime.now(timezone.utc)
    db.commit()

    return {"message": "Refinement job cancelled successfully"}


@router.get("/{doc_id}/refinement-jobs", response_model=RefinementJobListResponse)
async def list_refinement_jobs(
    doc_id: uuid.UUID,
    page: int = 1,
    page_size: int = 20,
    status_filter: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List refinement jobs for a document with pagination and filtering
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Build query
    query = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.tenant_id == current_tenant.id
    )

    # Apply status filter if provided
    if status_filter:
        query = query.filter(DocumentRefinementJob.status == status_filter)

    # Get total count
    total_jobs = query.count()

    # Apply pagination
    offset = (page - 1) * page_size
    jobs = query.order_by(desc(DocumentRefinementJob.started_at)).offset(offset).limit(page_size).all()

    # Convert to schema
    job_schemas = [_convert_job_to_schema(job) for job in jobs]

    return RefinementJobListResponse(
        jobs=job_schemas,
        total_jobs=total_jobs,
        page=page,
        page_size=page_size
    )


@router.get("/{doc_id}/refinement-history", response_model=RefinementHistoryResponse)
async def get_refinement_history(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get refinement history and analytics for a document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Get all refinement jobs for this document
    all_jobs = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.tenant_id == current_tenant.id
    ).all()

    # Calculate statistics
    total_refinements = len(all_jobs)
    successful_refinements = len([job for job in all_jobs if job.status == "completed"])
    failed_refinements = len([job for job in all_jobs if job.status == "failed"])

    # Get most common refinement types
    refinement_type_counts = {}
    for job in all_jobs:
        refinement_type = job.refinement_type
        refinement_type_counts[refinement_type] = refinement_type_counts.get(refinement_type, 0) + 1

    most_common_refinement_types = [
        {"refinement_type": rt, "count": count}
        for rt, count in sorted(refinement_type_counts.items(), key=lambda x: x[1], reverse=True)
    ]

    # Get recent jobs (last 10)
    recent_jobs = db.query(DocumentRefinementJob).filter(
        DocumentRefinementJob.document_id == doc_id,
        DocumentRefinementJob.tenant_id == current_tenant.id
    ).order_by(desc(DocumentRefinementJob.started_at)).limit(10).all()

    recent_job_schemas = [_convert_job_to_schema(job) for job in recent_jobs]

    # Calculate average completion time for completed jobs
    completed_jobs = [job for job in all_jobs if job.status == "completed" and job.completed_at]
    average_completion_time = None
    if completed_jobs:
        total_time = sum([
            (job.completed_at - job.started_at).total_seconds()
            for job in completed_jobs
        ])
        average_completion_time = total_time / len(completed_jobs)

    return RefinementHistoryResponse(
        document_id=doc_id,
        total_refinements=total_refinements,
        successful_refinements=successful_refinements,
        failed_refinements=failed_refinements,
        most_common_refinement_types=most_common_refinement_types,
        recent_jobs=recent_job_schemas,
        average_completion_time=average_completion_time
    )
