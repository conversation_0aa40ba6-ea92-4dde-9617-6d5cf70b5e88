from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from typing import List, Optional
import uuid
import logging

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document_type import DocumentType
from app.schemas.document_type import (
    DocumentTypeResponse,
    DocumentTypeListResponse,
    DocumentTypeCreate,
    DocumentTypeUpdate,
    FormSchemaResponse,
    TemplateStructureResponse,
    AIAgentsResponse,
    RefinementOptionsResponse,
    MessageResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[DocumentTypeListResponse])
async def list_document_types(
    category: Optional[str] = Query(None, description="Filter by category"),
    industry: Optional[str] = Query(None, description="Filter by industry"),
    active_only: bool = Query(True, description="Show only active document types"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List available document types for the current tenant.

    Returns both system default document types and tenant-specific custom types.
    """
    try:
        logger.info(f"Listing document types for tenant {current_tenant.id}, user {current_user.id}")

        query = db.query(DocumentType).filter(
            or_(
                DocumentType.is_system_default == True,
                DocumentType.tenant_id == current_tenant.id
            )
        )

        if active_only:
            query = query.filter(DocumentType.is_active == True)

        if category:
            query = query.filter(DocumentType.category == category)

        if industry:
            query = query.filter(DocumentType.industry == industry)

        document_types = query.order_by(
            DocumentType.is_system_default.desc(),
            DocumentType.usage_count.desc(),
            DocumentType.name
        ).all()

        logger.info(f"Found {len(document_types)} document types")
        return document_types

    except Exception as e:
        logger.error(f"Error listing document types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document types"
        )

@router.get("/{type_id}", response_model=DocumentTypeResponse)
async def get_document_type(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific document type details
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        )
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    return document_type

@router.post("/", response_model=DocumentTypeResponse)
async def create_document_type(
    document_type_data: DocumentTypeCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create custom document type (admin only).

    Only tenant administrators can create custom document types.
    System default document types cannot be created through this endpoint.
    """
    try:
        logger.info(f"Creating document type '{document_type_data.name}' for tenant {current_tenant.id}")

        # Check if user has admin role
        if current_user.role != "admin":
            logger.warning(f"Non-admin user {current_user.id} attempted to create document type")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only tenant admins can create custom document types"
            )

        # Check if slug already exists
        existing_type = db.query(DocumentType).filter(
            DocumentType.slug == document_type_data.slug,
            or_(
                DocumentType.is_system_default == True,
                DocumentType.tenant_id == current_tenant.id
            )
        ).first()

        if existing_type:
            logger.warning(f"Slug '{document_type_data.slug}' already exists")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Document type with this slug already exists"
            )
    
        # Set default AI agents if not provided
        ai_agents = document_type_data.ai_agents or {
            "primary_agent": "claude-3-5-sonnet-20241022",
            "refinement_agents": [],
            "specialized_agents": {}
        }

        # Set default refinement options if not provided
        refinement_options = document_type_data.refinement_options or {
            "tone_options": ["professional", "casual", "technical"],
            "length_options": ["brief", "standard", "detailed"],
            "focus_areas": [],
            "custom_refinements": []
        }

        document_type = DocumentType(
            name=document_type_data.name,
            slug=document_type_data.slug,
            description=document_type_data.description,
            category=document_type_data.category,
            industry=document_type_data.industry,
            form_schema=document_type_data.form_schema,
            template_structure=document_type_data.template_structure,
            ai_agents=ai_agents,
            refinement_options=refinement_options,
            is_system_default=False,
            tenant_id=current_tenant.id,
            created_by=current_user.id
        )

        db.add(document_type)
        db.commit()
        db.refresh(document_type)

        logger.info(f"Successfully created document type {document_type.id}")
        return document_type

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating document type: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create document type"
        )

@router.put("/{type_id}", response_model=DocumentTypeResponse)
async def update_document_type(
    type_id: uuid.UUID,
    document_type_update: DocumentTypeUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update document type (admin only)
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can update document types"
        )

    # Find document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        DocumentType.tenant_id == current_tenant.id,
        DocumentType.is_system_default == False
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found or cannot be modified"
        )

    # Update fields
    update_data = document_type_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(document_type, field, value)

    db.commit()
    db.refresh(document_type)

    return document_type

@router.delete("/{type_id}", response_model=MessageResponse)
async def delete_document_type(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete document type (admin only)
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can delete document types"
        )

    # Find document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        DocumentType.tenant_id == current_tenant.id,
        DocumentType.is_system_default == False
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found or cannot be deleted"
        )

    # Soft delete - mark as inactive
    document_type.is_active = False
    db.commit()

    return {"message": "Document type deleted successfully"}

@router.get("/{type_id}/form-schema", response_model=FormSchemaResponse)
async def get_document_type_form_schema(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get form questions for document type
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        )
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    return {"form_schema": document_type.form_schema}

@router.get("/{type_id}/template", response_model=TemplateStructureResponse)
async def get_document_type_template(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get document template structure
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        )
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    return {"template_structure": document_type.template_structure}

@router.get("/{type_id}/agents", response_model=AIAgentsResponse)
async def get_document_type_agents(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get AI agents for document type
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        )
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    return {"ai_agents": document_type.ai_agents}

@router.get("/{type_id}/refinements", response_model=RefinementOptionsResponse)
async def get_document_type_refinements(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get available refinement options
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        )
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    return {"refinement_options": document_type.refinement_options}

@router.get("/by-industry/{industry}", response_model=List[DocumentTypeListResponse])
async def get_document_types_by_industry(
    industry: str,
    active_only: bool = Query(True, description="Show only active document types"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get document types for specific industry
    """
    query = db.query(DocumentType).filter(
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        ),
        or_(
            DocumentType.industry == industry,
            DocumentType.industry.is_(None)  # Include generic types
        )
    )

    if active_only:
        query = query.filter(DocumentType.is_active == True)

    document_types = query.order_by(
        DocumentType.is_system_default.desc(),
        DocumentType.usage_count.desc(),
        DocumentType.name
    ).all()

    return document_types

@router.get("/by-category/{category}", response_model=List[DocumentTypeListResponse])
async def get_document_types_by_category(
    category: str,
    industry: Optional[str] = Query(None, description="Filter by industry"),
    active_only: bool = Query(True, description="Show only active document types"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get document types by category
    """
    query = db.query(DocumentType).filter(
        or_(
            DocumentType.is_system_default == True,
            DocumentType.tenant_id == current_tenant.id
        ),
        DocumentType.category == category
    )

    if industry:
        query = query.filter(
            or_(
                DocumentType.industry == industry,
                DocumentType.industry.is_(None)
            )
        )

    if active_only:
        query = query.filter(DocumentType.is_active == True)

    document_types = query.order_by(
        DocumentType.is_system_default.desc(),
        DocumentType.usage_count.desc(),
        DocumentType.name
    ).all()

    return document_types
