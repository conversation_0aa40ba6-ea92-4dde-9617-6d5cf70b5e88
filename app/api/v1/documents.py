"""
Main documents API router that combines all document-related endpoints.
This module imports and includes sub-routers from specialized modules.
"""
from fastapi import APIRouter

from app.api.v1.document_crud import router as crud_router
from app.api.v1.document_workflow import router as workflow_router
from app.api.v1.document_refinement import router as refinement_router
from app.api.v1.document_types import router as document_types_router
from app.api.v1.learning import router as learning_router
from app.api.v1.export_core import router as export_core_router
from app.api.v1.export_integrations import router as export_integrations_router
from app.api.v1.export_jobs import router as export_jobs_router
from app.api.v1.templates import router as templates_router
from app.api.v1.components import router as components_router

router = APIRouter()

# Include sub-routers
router.include_router(crud_router, tags=["documents-crud"])
router.include_router(workflow_router, tags=["documents-workflow"])
router.include_router(refinement_router, tags=["documents-refinement"])
router.include_router(document_types_router, prefix="/types", tags=["document-types"])
router.include_router(learning_router, prefix="/learning", tags=["learning-ai"])

# Export sub-routers
router.include_router(export_core_router, tags=["documents-export-core"])
router.include_router(export_integrations_router, tags=["documents-export-integrations"])
router.include_router(export_jobs_router, prefix="/export", tags=["documents-export-jobs"])

# Template and component sub-routers
router.include_router(templates_router, prefix="/templates", tags=["documents-templates"])
router.include_router(components_router, prefix="/library", tags=["documents-components"])
