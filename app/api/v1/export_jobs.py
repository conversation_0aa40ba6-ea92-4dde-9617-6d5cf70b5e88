"""
Export job management API endpoints.
Handles job status tracking, file downloads, export history, and format discovery.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Response
from fastapi.responses import FileResponse, RedirectResponse
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
import uuid
import os
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.document_type import DocumentType
from app.models.export import ExportJob, ExportFile, ExportHistory, ExportFormatSupport
from app.schemas.export import (
    ExportJobResponse,
    ExportHistoryResponse,
    ExportHistoryItem,
    AvailableFormatsResponse,
    ExportFormatInfo,
    ExportDownloadResponse,
    DocumentType as DocumentTypeEnum,
    ExportFormat,
    MessageResponse
)

router = APIRouter()


@router.get("/status/{job_id}", response_model=ExportJobResponse)
async def get_export_status(
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Check export job status
    """
    export_job = db.query(ExportJob).filter(
        ExportJob.id == job_id,
        ExportJob.tenant_id == current_tenant.id
    ).first()
    
    if not export_job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Export job not found"
        )
    
    return export_job


@router.get("/download/{file_id}")
async def download_exported_file(
    file_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Download exported file
    """
    # Get export file
    export_file = db.query(ExportFile).join(ExportJob).filter(
        ExportFile.id == file_id,
        ExportJob.tenant_id == current_tenant.id,
        ExportFile.deleted_at.is_(None)
    ).first()
    
    if not export_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Export file not found"
        )
    
    # Check if file has expired
    if export_file.expires_at and export_file.expires_at < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_410_GONE,
            detail="Export file has expired"
        )
    
    # Update download tracking
    export_file.download_count += 1
    export_file.last_downloaded_at = datetime.now(timezone.utc)
    db.commit()
    
    # For demo purposes, return a redirect or file info
    # In production, this would serve the actual file
    if export_file.export_job.result_url:
        # For integration exports, redirect to external URL
        return RedirectResponse(url=export_file.export_job.result_url)
    else:
        # For file exports, return file info (in production, would return actual file)
        return ExportDownloadResponse(
            file_id=export_file.id,
            filename=export_file.original_filename or export_file.filename,
            content_type=export_file.content_type,
            file_size=export_file.file_size,
            download_url=f"/api/v1/export/download/{file_id}",
            expires_at=export_file.expires_at
        )


@router.get("/history/{doc_id}", response_model=ExportHistoryResponse)
async def get_export_history(
    doc_id: uuid.UUID,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get export history for a document
    """
    # Verify document access
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Get export jobs for this document
    export_jobs = db.query(ExportJob).filter(
        ExportJob.document_id == doc_id,
        ExportJob.tenant_id == current_tenant.id
    ).order_by(desc(ExportJob.created_at)).offset(offset).limit(limit).all()
    
    # Convert to history items
    history_items = []
    for job in export_jobs:
        file_size = None
        if job.export_file:
            file_size = job.export_file.file_size
        
        history_item = ExportHistoryItem(
            job_id=job.id,
            export_format=ExportFormat(job.export_format),
            status=job.status,
            file_size=file_size,
            download_count=job.export_file.download_count if job.export_file else 0,
            created_at=job.created_at,
            expires_at=job.export_file.expires_at if job.export_file else None
        )
        history_items.append(history_item)
    
    # Get total count
    total_count = db.query(ExportJob).filter(
        ExportJob.document_id == doc_id,
        ExportJob.tenant_id == current_tenant.id
    ).count()
    
    return ExportHistoryResponse(
        document_id=doc_id,
        exports=history_items,
        total_count=total_count
    )


@router.get("/formats/{doc_type}", response_model=AvailableFormatsResponse)
async def get_available_export_formats(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get available export formats for a document type
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.name.ilike(f"%{doc_type}%"),
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        # Return default formats if document type not found
        formats = _get_default_export_formats()
    else:
        # Get supported formats from database
        format_supports = db.query(ExportFormatSupport).filter(
            ExportFormatSupport.document_type_id == document_type.id,
            ExportFormatSupport.is_supported == True
        ).all()
        
        if format_supports:
            formats = [
                ExportFormatInfo(
                    format=ExportFormat(fs.export_format),
                    name=fs.export_format.title(),
                    description=fs.description or f"Export as {fs.export_format}",
                    file_extension=fs.file_extension,
                    mime_type=fs.mime_type,
                    supports_options=bool(fs.default_options),
                    estimated_time=f"{fs.estimated_time_minutes} minutes"
                )
                for fs in format_supports
            ]
        else:
            formats = _get_default_export_formats()
    
    # Map document type string to enum
    doc_type_enum = DocumentTypeEnum.GENERAL
    if "prd" in doc_type.lower():
        doc_type_enum = DocumentTypeEnum.PRD
    elif "api" in doc_type.lower():
        doc_type_enum = DocumentTypeEnum.API_DOC
    elif "architecture" in doc_type.lower():
        doc_type_enum = DocumentTypeEnum.ARCHITECTURE
    elif "design" in doc_type.lower():
        doc_type_enum = DocumentTypeEnum.DESIGN
    
    return AvailableFormatsResponse(
        document_type=doc_type_enum,
        formats=formats
    )


def _get_default_export_formats() -> List[ExportFormatInfo]:
    """Get default export formats when no specific configuration exists"""
    return [
        ExportFormatInfo(
            format=ExportFormat.WORD,
            name="Microsoft Word",
            description="Export as Word document (.docx)",
            file_extension="docx",
            mime_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            supports_options=True,
            estimated_time="2-3 minutes"
        ),
        ExportFormatInfo(
            format=ExportFormat.PDF,
            name="PDF Document",
            description="Export as PDF document",
            file_extension="pdf",
            mime_type="application/pdf",
            supports_options=True,
            estimated_time="3-5 minutes"
        ),
        ExportFormatInfo(
            format=ExportFormat.MARKDOWN,
            name="Markdown",
            description="Export as Markdown file",
            file_extension="md",
            mime_type="text/markdown",
            supports_options=True,
            estimated_time="1-2 minutes"
        ),
        ExportFormatInfo(
            format=ExportFormat.CONFLUENCE,
            name="Confluence",
            description="Publish to Confluence",
            file_extension="",
            mime_type="text/html",
            supports_options=True,
            estimated_time="5-10 minutes"
        ),
        ExportFormatInfo(
            format=ExportFormat.NOTION,
            name="Notion",
            description="Export to Notion",
            file_extension="",
            mime_type="text/html",
            supports_options=True,
            estimated_time="3-7 minutes"
        )
    ]


@router.delete("/jobs/{job_id}", response_model=MessageResponse)
async def cancel_export_job(
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Cancel a pending or processing export job
    """
    export_job = db.query(ExportJob).filter(
        ExportJob.id == job_id,
        ExportJob.tenant_id == current_tenant.id
    ).first()
    
    if not export_job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Export job not found"
        )
    
    if export_job.status in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel job with status: {export_job.status}"
        )
    
    # Cancel the job
    export_job.status = "cancelled"
    export_job.cancelled_at = datetime.now(timezone.utc)
    db.commit()
    
    return MessageResponse(
        message="Export job cancelled successfully",
        details={"job_id": str(job_id)}
    )


@router.delete("/files/{file_id}", response_model=MessageResponse)
async def delete_export_file(
    file_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete an exported file
    """
    export_file = db.query(ExportFile).join(ExportJob).filter(
        ExportFile.id == file_id,
        ExportJob.tenant_id == current_tenant.id,
        ExportFile.deleted_at.is_(None)
    ).first()
    
    if not export_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Export file not found"
        )
    
    # Mark file as deleted (soft delete)
    export_file.deleted_at = datetime.now(timezone.utc)
    db.commit()
    
    return MessageResponse(
        message="Export file deleted successfully",
        details={"file_id": str(file_id)}
    )
