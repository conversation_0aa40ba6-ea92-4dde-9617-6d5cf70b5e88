"""
Analytics Dashboard API endpoints.
Provides comprehensive dashboard data with overview metrics, charts, and insights.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, date, timedelta

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.schemas.analytics import (
    DashboardResponse,
    DashboardOverview,
    DashboardCharts,
    DateRangeFilter
)
from app.services.analytics_service import AnalyticsService

router = APIRouter()


def check_analytics_permissions(user: User) -> bool:
    """Check if user has permissions to view analytics"""
    # In a real implementation, check user roles/permissions
    # For now, allow all authenticated users to view analytics
    return True


@router.get("/dashboard", response_model=DashboardResponse)
async def get_dashboard_data(
    start_date: Optional[date] = Query(None, description="Start date for analytics period"),
    end_date: Optional[date] = Query(None, description="End date for analytics period"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get comprehensive dashboard analytics data
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days if no date range provided
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Validate date range
    if start_date > end_date:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Start date must be before end date"
        )
    
    if (end_date - start_date).days > 365:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Date range cannot exceed 365 days"
        )
    
    analytics_service = AnalyticsService(db)
    
    try:
        # Get overview metrics
        overview_metrics = analytics_service.get_dashboard_overview(
            current_tenant.id, start_date, end_date
        )
        
        overview = DashboardOverview(
            total_documents=overview_metrics["total_documents"],
            documents_this_month=overview_metrics["documents_this_month"],
            active_users=overview_metrics["active_users"],
            ai_generations=overview_metrics["ai_generations"],
            completion_rate=overview_metrics["completion_rate"],
            avg_generation_time=overview_metrics["avg_generation_time"]
        )
        
        # Get chart data
        document_creation_trend = analytics_service.get_document_creation_trend(
            current_tenant.id, start_date, end_date
        )
        
        document_type_distribution = analytics_service.get_document_type_distribution(
            current_tenant.id, start_date, end_date
        )
        
        user_activity = analytics_service.get_user_activity_trend(
            current_tenant.id, start_date, end_date
        )
        
        ai_performance_data = analytics_service.get_ai_performance_metrics(
            current_tenant.id, start_date, end_date
        )
        
        # Create AI performance chart
        ai_performance_chart = {
            "title": "AI Performance",
            "type": "line",
            "series": [
                {
                    "name": "Success Rate (%)",
                    "data": ai_performance_data["success_rate"]
                },
                {
                    "name": "Avg Response Time (s)",
                    "data": ai_performance_data["avg_response_time"]
                }
            ]
        }
        
        export_usage = analytics_service.get_export_usage_trend(
            current_tenant.id, start_date, end_date
        )
        
        charts = DashboardCharts(
            document_creation_trend=document_creation_trend,
            document_type_distribution=document_type_distribution,
            user_activity=user_activity,
            ai_performance=ai_performance_chart,
            export_usage=export_usage
        )
        
        # Get recent activity
        recent_activity = analytics_service.get_recent_activity(
            current_tenant.id, limit=10
        )
        
        # Generate insights
        insights = analytics_service.generate_insights(
            current_tenant.id, overview_metrics
        )
        
        return DashboardResponse(
            overview=overview,
            charts=charts,
            recent_activity=recent_activity,
            insights=insights,
            last_updated=datetime.now()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate dashboard data: {str(e)}"
        )


@router.get("/dashboard/overview")
async def get_dashboard_overview_only(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get only the dashboard overview metrics (faster endpoint)
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    analytics_service = AnalyticsService(db)
    
    try:
        overview_metrics = analytics_service.get_dashboard_overview(
            current_tenant.id, start_date, end_date
        )
        
        return DashboardOverview(
            total_documents=overview_metrics["total_documents"],
            documents_this_month=overview_metrics["documents_this_month"],
            active_users=overview_metrics["active_users"],
            ai_generations=overview_metrics["ai_generations"],
            completion_rate=overview_metrics["completion_rate"],
            avg_generation_time=overview_metrics["avg_generation_time"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate overview metrics: {str(e)}"
        )


@router.get("/dashboard/charts")
async def get_dashboard_charts_only(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    chart_type: Optional[str] = Query(None, description="Specific chart type to fetch"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get only the dashboard charts data (for dynamic loading)
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    analytics_service = AnalyticsService(db)
    
    try:
        charts_data = {}
        
        # If specific chart requested, return only that chart
        if chart_type:
            if chart_type == "document_creation":
                charts_data["document_creation_trend"] = analytics_service.get_document_creation_trend(
                    current_tenant.id, start_date, end_date
                )
            elif chart_type == "document_types":
                charts_data["document_type_distribution"] = analytics_service.get_document_type_distribution(
                    current_tenant.id, start_date, end_date
                )
            elif chart_type == "user_activity":
                charts_data["user_activity"] = analytics_service.get_user_activity_trend(
                    current_tenant.id, start_date, end_date
                )
            elif chart_type == "ai_performance":
                ai_data = analytics_service.get_ai_performance_metrics(
                    current_tenant.id, start_date, end_date
                )
                charts_data["ai_performance"] = {
                    "title": "AI Performance",
                    "type": "line",
                    "series": [
                        {"name": "Success Rate (%)", "data": ai_data["success_rate"]},
                        {"name": "Avg Response Time (s)", "data": ai_data["avg_response_time"]}
                    ]
                }
            elif chart_type == "export_usage":
                charts_data["export_usage"] = analytics_service.get_export_usage_trend(
                    current_tenant.id, start_date, end_date
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unknown chart type: {chart_type}"
                )
        else:
            # Return all charts
            document_creation_trend = analytics_service.get_document_creation_trend(
                current_tenant.id, start_date, end_date
            )
            
            document_type_distribution = analytics_service.get_document_type_distribution(
                current_tenant.id, start_date, end_date
            )
            
            user_activity = analytics_service.get_user_activity_trend(
                current_tenant.id, start_date, end_date
            )
            
            ai_performance_data = analytics_service.get_ai_performance_metrics(
                current_tenant.id, start_date, end_date
            )
            
            export_usage = analytics_service.get_export_usage_trend(
                current_tenant.id, start_date, end_date
            )
            
            charts_data = {
                "document_creation_trend": document_creation_trend,
                "document_type_distribution": document_type_distribution,
                "user_activity": user_activity,
                "ai_performance": {
                    "title": "AI Performance",
                    "type": "line",
                    "series": [
                        {"name": "Success Rate (%)", "data": ai_performance_data["success_rate"]},
                        {"name": "Avg Response Time (s)", "data": ai_performance_data["avg_response_time"]}
                    ]
                },
                "export_usage": export_usage
            }
        
        return charts_data
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate charts data: {str(e)}"
        )


@router.get("/dashboard/insights")
async def get_dashboard_insights(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get AI-generated insights for the dashboard
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    analytics_service = AnalyticsService(db)
    
    try:
        # Get overview metrics for insight generation
        overview_metrics = analytics_service.get_dashboard_overview(
            current_tenant.id, start_date, end_date
        )
        
        # Generate insights
        insights = analytics_service.generate_insights(
            current_tenant.id, overview_metrics
        )
        
        return {
            "insights": insights,
            "generated_at": datetime.now(),
            "period": {
                "start_date": start_date,
                "end_date": end_date
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate insights: {str(e)}"
        )
