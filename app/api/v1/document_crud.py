"""
Document CRUD operations API endpoints.
Handles basic document operations: create, read, update, delete, duplicate, status updates, history, restore, convert.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document, DocumentVersion
from app.models.document_type import DocumentType
from app.schemas.document import (
    DocumentResponse,
    DocumentListResponse,
    DocumentGenerate,
    DocumentUpdate,
    DocumentStatusUpdate,
    DocumentVersionResponse,
    DocumentDuplicateRequest,
    DocumentConvertRequest,
    MessageResponse
)
from app.services.anthropic_service import anthropic_service

router = APIRouter()


@router.post("/generate", response_model=DocumentResponse)
async def generate_document(
    document_data: DocumentGenerate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Generate new document (any type)
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == document_data.document_type_id,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    try:
        # Generate document content using AI
        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name,
            "document_type": document_type.name
        }
        
        # Use the document type's template and AI configuration
        content = await anthropic_service.generate_document(
            document_data.form_data,
            document_type,
            user_context
        )
        
        # Extract title from content or use provided title
        title = document_data.title or f"New {document_type.name}"
        
        # Create document
        document = Document(
            title=title,
            content=content,
            document_type_id=document_data.document_type_id,
            form_data=document_data.form_data,
            user_id=current_user.id,
            tenant_id=current_tenant.id
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        # Create initial version
        version = DocumentVersion(
            document_id=document.id,
            version="1.0",
            version_number=1,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Initial version",
            created_by=current_user.id
        )
        
        db.add(version)
        
        # Update document type usage count
        document_type.usage_count += 1
        
        db.commit()
        
        return document
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate document: {str(e)}"
        )


@router.get("/", response_model=List[DocumentListResponse])
async def list_documents(
    document_type_id: Optional[uuid.UUID] = Query(None, description="Filter by document type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, le=100, description="Number of documents to return"),
    offset: int = Query(0, description="Number of documents to skip"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List user's documents
    """
    query = db.query(Document).filter(
        Document.tenant_id == current_tenant.id
    )
    
    # Filter by document type if specified
    if document_type_id:
        query = query.filter(Document.document_type_id == document_type_id)
    
    # Filter by status if specified
    if status:
        query = query.filter(Document.status == status)
    
    # Apply pagination and ordering
    documents = query.order_by(desc(Document.updated_at)).offset(offset).limit(limit).all()
    
    return documents


@router.get("/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Update view count and last viewed
    document.view_count += 1
    document.last_viewed_at = datetime.now(timezone.utc)
    db.commit()

    return document


@router.put("/{doc_id}", response_model=DocumentResponse)
async def update_document(
    doc_id: uuid.UUID,
    document_update: DocumentUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update document content
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check if user has edit permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to edit this document"
        )

    # Store original values for version history
    original_title = document.title
    original_content = document.content
    original_form_data = document.form_data

    # Update document
    update_data = document_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(document, field, value)

    # Increment version if content changed
    content_changed = (
        document_update.content and document_update.content != original_content
    )

    if content_changed:
        # Create new version
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        # Create version record
        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Content updated",
            created_by=current_user.id
        )

        db.add(version)

    db.commit()
    db.refresh(document)

    return document


@router.delete("/{doc_id}", response_model=MessageResponse)
async def delete_document(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check if user has permission to delete
    if (document.user_id != current_user.id and current_user.role != "admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to delete this document"
        )

    # Soft delete - archive the document
    document.status = "archived"
    db.commit()

    return {"message": "Document deleted successfully"}


@router.post("/{doc_id}/duplicate", response_model=DocumentResponse)
async def duplicate_document(
    doc_id: uuid.UUID,
    duplicate_request: DocumentDuplicateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Duplicate existing document
    """
    original_document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not original_document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check if user has view permissions
    if (original_document.user_id != current_user.id and
        current_user.id not in original_document.permissions.get("can_view", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to duplicate this document"
        )

    # Create duplicate
    new_title = duplicate_request.title or f"Copy of {original_document.title}"

    duplicate = Document(
        title=new_title,
        content=original_document.content,
        document_type_id=original_document.document_type_id,
        form_data=original_document.form_data,
        status="draft",
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        permissions=original_document.permissions if duplicate_request.copy_permissions else {}
    )

    db.add(duplicate)
    db.commit()
    db.refresh(duplicate)

    # Create initial version for duplicate
    version = DocumentVersion(
        document_id=duplicate.id,
        version="1.0",
        version_number=1,
        title=duplicate.title,
        content=duplicate.content,
        form_data=duplicate.form_data,
        status=duplicate.status,
        change_summary=f"Duplicated from document {original_document.id}",
        created_by=current_user.id
    )

    db.add(version)
    db.commit()

    return duplicate


@router.put("/{doc_id}/status", response_model=DocumentResponse)
async def update_document_status(
    doc_id: uuid.UUID,
    status_update: DocumentStatusUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update document status
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update document status"
        )

    old_status = document.status
    document.status = status_update.status

    # Set published_at if status is being set to published
    if status_update.status == "published" and old_status != "published":
        document.published_at = datetime.now(timezone.utc)

    # Create version record for status change
    latest_version = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).first()

    new_version_number = (latest_version.version_number + 1) if latest_version else 1
    new_version = f"{new_version_number}.0"

    document.version = new_version
    document.version_number = new_version_number

    version = DocumentVersion(
        document_id=document.id,
        version=new_version,
        version_number=new_version_number,
        title=document.title,
        content=document.content,
        form_data=document.form_data,
        status=document.status,
        change_summary=status_update.change_summary or f"Status changed from {old_status} to {status_update.status}",
        created_by=current_user.id
    )

    db.add(version)
    db.commit()
    db.refresh(document)

    return document


@router.get("/{doc_id}/history", response_model=List[DocumentVersionResponse])
async def get_document_history(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get document version history
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_view", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view document history"
        )

    versions = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).all()

    return versions


@router.post("/{doc_id}/restore/{version}", response_model=DocumentResponse)
async def restore_document_version(
    doc_id: uuid.UUID,
    version: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Restore previous version
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to restore document version"
        )

    # Find the version to restore
    version_to_restore = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id,
        DocumentVersion.version == version
    ).first()

    if not version_to_restore:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Version not found"
        )

    # Restore the document to the specified version
    document.title = version_to_restore.title
    document.content = version_to_restore.content
    document.form_data = version_to_restore.form_data
    document.status = version_to_restore.status

    # Create new version record for the restore
    latest_version = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).first()

    new_version_number = (latest_version.version_number + 1) if latest_version else 1
    new_version = f"{new_version_number}.0"

    document.version = new_version
    document.version_number = new_version_number

    restore_version = DocumentVersion(
        document_id=document.id,
        version=new_version,
        version_number=new_version_number,
        title=document.title,
        content=document.content,
        form_data=document.form_data,
        status=document.status,
        change_summary=f"Restored to version {version}",
        created_by=current_user.id
    )

    db.add(restore_version)
    db.commit()
    db.refresh(document)

    return document


@router.post("/{doc_id}/convert/{target_type}", response_model=DocumentResponse)
async def convert_document_type(
    doc_id: uuid.UUID,
    target_type: uuid.UUID,
    convert_request: DocumentConvertRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Convert to different document type
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to convert document"
        )

    # Get target document type
    target_document_type = db.query(DocumentType).filter(
        DocumentType.id == target_type,
        DocumentType.is_active == True
    ).first()

    if not target_document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Target document type not found"
        )

    # Update document type
    old_type_id = document.document_type_id
    document.document_type_id = target_type

    # Update form data if provided
    if convert_request.update_form_data:
        document.form_data = convert_request.update_form_data

    # Optionally regenerate content based on new type
    if not convert_request.preserve_content:
        try:
            user_context = {
                "tenant_name": current_tenant.name,
                "user_name": current_user.full_name,
                "document_type": target_document_type.name
            }

            document.content = await anthropic_service.generate_document(
                document.form_data,
                target_document_type,
                user_context
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to regenerate content: {str(e)}"
            )

    # Create version record for conversion
    latest_version = db.query(DocumentVersion).filter(
        DocumentVersion.document_id == doc_id
    ).order_by(desc(DocumentVersion.version_number)).first()

    new_version_number = (latest_version.version_number + 1) if latest_version else 1
    new_version = f"{new_version_number}.0"

    document.version = new_version
    document.version_number = new_version_number

    version = DocumentVersion(
        document_id=document.id,
        version=new_version,
        version_number=new_version_number,
        title=document.title,
        content=document.content,
        form_data=document.form_data,
        status=document.status,
        change_summary=f"Converted from document type {old_type_id} to {target_type}",
        created_by=current_user.id
    )

    db.add(version)
    db.commit()
    db.refresh(document)

    return document


@router.get("/by-type/{type_id}", response_model=List[DocumentListResponse])
async def list_documents_by_type(
    type_id: uuid.UUID,
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, le=100, description="Number of documents to return"),
    offset: int = Query(0, description="Number of documents to skip"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List documents of specific type
    """
    # Verify document type exists and user has access
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    query = db.query(Document).filter(
        Document.tenant_id == current_tenant.id,
        Document.document_type_id == type_id
    )

    # Filter by status if specified
    if status:
        query = query.filter(Document.status == status)

    # Apply pagination and ordering
    documents = query.order_by(desc(Document.updated_at)).offset(offset).limit(limit).all()

    return documents
