"""
Main authentication router that imports and combines all modular auth endpoints.

This module serves as the entry point for all authentication-related operations,
organizing them into logical sub-modules for better maintainability.
"""

from fastapi import APIRouter

# Import all modular auth routers
from app.api.v1.auth.authentication import router as auth_router
from app.api.v1.auth.registration import router as registration_router
from app.api.v1.auth.password import router as password_router
from app.api.v1.auth.session import router as session_router

# Create main auth router
router = APIRouter()

# Include all auth sub-routers with appropriate prefixes and tags
router.include_router(
    auth_router,
    tags=["Authentication"],
    responses={
        401: {"description": "Unauthorized"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    registration_router,
    tags=["Registration"],
    responses={
        400: {"description": "Bad Request"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    password_router,
    tags=["Password Management"],
    responses={
        400: {"description": "Bad Request"},
        401: {"description": "Unauthorized"},
        422: {"description": "Validation Error"}
    }
)

router.include_router(
    session_router,
    tags=["Session Management"],
    responses={
        401: {"description": "Unauthorized"},
        422: {"description": "Validation Error"}
    }
)