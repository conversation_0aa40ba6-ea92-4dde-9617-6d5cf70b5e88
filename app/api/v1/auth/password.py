"""
Password management endpoints for reset and change operations.

This module handles password-related operations using Supabase Auth:
- Password reset request via email
- Password reset with token
- Password change for authenticated users
- Password strength validation
"""

from fastapi import APIRouter, Depends, HTTPException, status, Header
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.core.database import get_db
from app.api.deps import get_current_user
from app.services.auth_service import auth_service
from app.models.user import User
from app.schemas.user import (
    ForgotPasswordRequest,
    ResetPasswordRequest,
    PasswordChangeRequest,
    MessageResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/forgot-password", response_model=MessageResponse)
async def request_password_reset(
    request: ForgotPasswordRequest
):
    """
    Request password reset via email.
    
    This endpoint:
    - Validates the email address format
    - Sends password reset email via Supabase Auth
    - Returns success message (regardless of email existence for security)
    - Logs the password reset request
    
    Args:
        request: Email address for password reset
        
    Returns:
        MessageResponse: Success message
        
    Note:
        Always returns success message for security reasons,
        regardless of whether the email exists in the system.
    """
    try:
        result = await auth_service.request_password_reset(request.email)
        
        return MessageResponse(
            message=result["message"],
            details={
                "email": request.email,
                "reset_requested": True
            }
        )
        
    except Exception as e:
        logger.error(f"Error requesting password reset: {str(e)}")
        # Return success message for security (don't reveal if email exists)
        return MessageResponse(
            message="If the email exists, a password reset link has been sent",
            details={
                "email": request.email,
                "reset_requested": True
            }
        )


@router.post("/reset-password", response_model=MessageResponse)
async def reset_password(
    request: ResetPasswordRequest
):
    """
    Reset password using token from reset email.
    
    This endpoint:
    - Validates the reset token from email
    - Updates user's password in Supabase Auth
    - Invalidates the reset token
    - Returns success message
    
    Args:
        request: Reset token and new password
        
    Returns:
        MessageResponse: Password reset result
        
    Raises:
        HTTPException: If reset fails or token is invalid
    """
    try:
        result = await auth_service.reset_password(
            access_token=request.token,
            new_password=request.new_password
        )
        
        return MessageResponse(
            message=result["message"],
            details={"password_reset": True}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during password reset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed due to server error"
        )


@router.post("/change-password", response_model=MessageResponse)
async def change_password(
    request: PasswordChangeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Change password for authenticated user.
    
    This endpoint:
    - Validates current password
    - Updates password in both Supabase and local database
    - Requires user to be authenticated
    - Validates new password strength
    
    Args:
        request: Current and new password
        current_user: Authenticated user from dependency
        db: Database session
        
    Returns:
        MessageResponse: Password change result
        
    Raises:
        HTTPException: If password change fails
    """
    try:
        result = await auth_service.change_password(
            user_id=str(current_user.id),
            current_password=request.current_password,
            new_password=request.new_password,
            db=db
        )
        
        return MessageResponse(
            message=result["message"],
            details={
                "user_id": str(current_user.id),
                "password_changed": True
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during password change: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed due to server error"
        )


@router.post("/validate-password", response_model=MessageResponse)
async def validate_password_strength(
    password_data: dict
):
    """
    Validate password strength without changing it.
    
    This endpoint:
    - Checks password against strength requirements
    - Returns validation result and feedback
    - Helps with frontend password validation
    
    Args:
        password_data: Contains password to validate
        
    Returns:
        MessageResponse: Validation result and feedback
    """
    try:
        password = password_data.get("password", "")
        
        if not password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password is required"
            )
        
        # Password strength validation
        issues = []
        
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password should contain at least one special character")
        
        if len(password) > 100:
            issues.append("Password must be less than 100 characters")
        
        is_valid = len(issues) == 0
        
        return MessageResponse(
            message="Password is valid" if is_valid else "Password does not meet requirements",
            details={
                "valid": is_valid,
                "issues": issues,
                "strength": "strong" if is_valid and len(password) >= 12 else "medium" if is_valid else "weak"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password validation failed"
        )


@router.post("/verify-current-password", response_model=MessageResponse)
async def verify_current_password(
    password_data: dict,
    current_user: User = Depends(get_current_user)
):
    """
    Verify user's current password without changing it.
    
    This endpoint:
    - Validates the provided password against user's current password
    - Returns verification result
    - Useful for sensitive operations requiring password confirmation
    
    Args:
        password_data: Contains password to verify
        current_user: Authenticated user from dependency
        
    Returns:
        MessageResponse: Password verification result
        
    Raises:
        HTTPException: If verification fails
    """
    try:
        password = password_data.get("password", "")
        
        if not password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password is required"
            )
        
        from app.core.security import verify_password
        
        is_valid = verify_password(password, current_user.hashed_password)
        
        return MessageResponse(
            message="Password is correct" if is_valid else "Password is incorrect",
            details={
                "valid": is_valid,
                "user_id": str(current_user.id)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying current password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password verification failed"
        )
