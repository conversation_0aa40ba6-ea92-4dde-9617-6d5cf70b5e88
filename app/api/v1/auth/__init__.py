"""
Modular authentication API endpoints.

This package contains organized authentication endpoints split into logical modules:
- authentication: Login, logout, token refresh
- registration: User registration and email verification
- password: Password reset and change operations
- session: Session management and user info
"""

from fastapi import APIRouter
from .authentication import router as auth_router
from .registration import router as registration_router
from .password import router as password_router
from .session import router as session_router
from .monitoring import router as monitoring_router

# Main auth router that combines all sub-routers
router = APIRouter()

# Include all auth sub-routers
router.include_router(auth_router, tags=["authentication"])
router.include_router(registration_router, tags=["registration"])
router.include_router(password_router, tags=["password"])
router.include_router(session_router, tags=["session"])
router.include_router(monitoring_router, prefix="/monitoring", tags=["monitoring"])
