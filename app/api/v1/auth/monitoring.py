"""
Authentication monitoring and metrics endpoints.

This module provides endpoints for monitoring authentication system health,
metrics, and performance indicators.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any
import logging
from datetime import datetime

from app.api.deps import get_current_superuser
from app.models.user import User
from app.core.auth_exceptions import auth_metrics
from app.core.supabase import get_supabase_client
from app.schemas.user import AuthHealthCheck

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/metrics", response_model=Dict[str, Any])
async def get_auth_metrics(
    current_user: User = Depends(get_current_superuser)
):
    """
    Get authentication system metrics.
    
    This endpoint provides metrics about authentication operations
    including login attempts, registrations, and other auth events.
    
    Args:
        current_user: Must be a superuser to access metrics
        
    Returns:
        Dict containing authentication metrics
    """
    try:
        metrics = auth_metrics.get_metrics()
        
        # Calculate derived metrics
        total_login_attempts = metrics.get("login_attempts", 0)
        login_successes = metrics.get("login_successes", 0)
        login_failures = metrics.get("login_failures", 0)
        
        success_rate = 0.0
        if total_login_attempts > 0:
            success_rate = (login_successes / total_login_attempts) * 100
        
        return {
            "raw_metrics": metrics,
            "derived_metrics": {
                "login_success_rate": round(success_rate, 2),
                "total_users_registered": metrics.get("registrations", 0),
                "total_password_resets": metrics.get("password_resets", 0),
                "total_email_verifications": metrics.get("email_verifications", 0)
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error retrieving auth metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve authentication metrics"
        )


@router.post("/metrics/reset")
async def reset_auth_metrics(
    current_user: User = Depends(get_current_superuser)
):
    """
    Reset authentication metrics counters.
    
    This endpoint resets all authentication metrics to zero.
    Use with caution as this will clear historical data.
    
    Args:
        current_user: Must be a superuser to reset metrics
        
    Returns:
        Success message
    """
    try:
        auth_metrics.reset_metrics()
        
        logger.info(f"Auth metrics reset by user: {current_user.email}")
        
        return {
            "message": "Authentication metrics reset successfully",
            "reset_by": current_user.email,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error resetting auth metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset authentication metrics"
        )


@router.get("/health/detailed", response_model=Dict[str, Any])
async def detailed_auth_health_check():
    """
    Perform detailed health check of authentication system.
    
    This endpoint checks the health of all authentication components
    including Supabase connection, database connectivity, and service status.
    
    Returns:
        Detailed health check results
    """
    try:
        health_results = {
            "overall_status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {}
        }
        
        # Check Supabase connection
        try:
            supabase_client = get_supabase_client()
            supabase_health = supabase_client.health_check()
            health_results["components"]["supabase"] = supabase_health
        except Exception as e:
            health_results["components"]["supabase"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_results["overall_status"] = "unhealthy"
        
        # Check auth service availability
        try:
            from app.services.auth_service import auth_service
            # Simple check to see if auth service is accessible
            health_results["components"]["auth_service"] = {
                "status": "healthy",
                "initialized": auth_service is not None
            }
        except Exception as e:
            health_results["components"]["auth_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_results["overall_status"] = "unhealthy"
        
        # Check metrics collection
        try:
            metrics = auth_metrics.get_metrics()
            health_results["components"]["metrics"] = {
                "status": "healthy",
                "total_events": sum(metrics.values())
            }
        except Exception as e:
            health_results["components"]["metrics"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        return health_results
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {str(e)}")
        return {
            "overall_status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


@router.get("/status", response_model=Dict[str, Any])
async def get_auth_system_status():
    """
    Get current authentication system status.
    
    This endpoint provides a quick overview of the authentication system
    status without requiring authentication.
    
    Returns:
        System status information
    """
    try:
        # Basic status check
        status_info = {
            "service": "authentication",
            "status": "operational",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
        
        # Quick Supabase connectivity check
        try:
            supabase_client = get_supabase_client()
            supabase_health = supabase_client.health_check()
            status_info["supabase_status"] = supabase_health["status"]
        except Exception:
            status_info["supabase_status"] = "unavailable"
            status_info["status"] = "degraded"
        
        return status_info
        
    except Exception as e:
        logger.error(f"Status check failed: {str(e)}")
        return {
            "service": "authentication",
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": "Status check failed"
        }


@router.get("/logs/recent")
async def get_recent_auth_logs(
    limit: int = 100,
    current_user: User = Depends(get_current_superuser)
):
    """
    Get recent authentication logs.
    
    This endpoint returns recent authentication events for monitoring
    and debugging purposes.
    
    Args:
        limit: Maximum number of log entries to return
        current_user: Must be a superuser to access logs
        
    Returns:
        Recent authentication log entries
    """
    try:
        # In a real implementation, you would fetch logs from your logging system
        # This is a placeholder that returns sample data
        
        return {
            "message": "Log retrieval not implemented",
            "note": "In production, this would return recent auth logs from your logging system",
            "requested_limit": limit,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error retrieving auth logs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve authentication logs"
        )
