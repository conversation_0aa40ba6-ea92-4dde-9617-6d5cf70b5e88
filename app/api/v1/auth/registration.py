"""
User registration and email verification endpoints.

This module handles user registration operations using Supabase Auth:
- New user registration with email verification
- Email verification with tokens
- Resending verification emails
- Registration validation and error handling
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import logging

from app.core.database import get_db
from app.services.auth_service import auth_service
from app.schemas.user import (
    UserCreate,
    UserRegistrationResponse,
    MessageResponse,
    EmailVerificationRequest,
    EmailVerificationResponse,
    ResendVerificationRequest
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/register", response_model=UserRegistrationResponse)
async def register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new user with Supabase Auth and local database.
    
    This endpoint:
    - Validates user registration data
    - Creates user account in Supabase Auth
    - Stores user information in local database
    - Sends email verification (if configured)
    - Returns user information and registration status
    
    Args:
        user_data: User registration information
        db: Database session
        
    Returns:
        UserRegistrationResponse: Registration result and user info
        
    Raises:
        HTTPException: If registration fails
    """
    try:
        user, auth_data = await auth_service.register_user(user_data, db)
        
        return UserRegistrationResponse(
            user=user,
            message="User registered successfully. Please check your email for verification.",
            email_confirmation_sent=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during registration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed due to server error"
        )


@router.get("/verify-email/{token}", response_model=MessageResponse)
async def verify_email(token: str):
    """
    Verify user's email address using verification token.

    This endpoint:
    - Validates the email verification token
    - Confirms the user's email address in Supabase
    - Updates user's email verification status
    - Returns verification result

    Args:
        token: Email verification token from email link

    Returns:
        MessageResponse: Verification result message

    Raises:
        HTTPException: If verification fails
    """
    try:
        result = await auth_service.verify_email(token)
        return MessageResponse(
            message=result["message"],
            details={"verified": True}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during email verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed due to server error"
        )


@router.get("/confirm", response_model=MessageResponse)
async def confirm_email(
    token_hash: str = None,
    type: str = None,
    access_token: str = None,
    refresh_token: str = None
):
    """
    Handle email confirmation from Supabase redirect.

    This endpoint handles the redirect from Supabase email confirmation links.
    It processes the tokens and confirms the user's email address.

    Args:
        token_hash: Token hash from Supabase
        type: Confirmation type (usually 'signup')
        access_token: Access token from Supabase
        refresh_token: Refresh token from Supabase

    Returns:
        MessageResponse: Confirmation result
    """
    try:
        if not token_hash and not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing confirmation token"
            )

        # If we have an access token, the user is already confirmed
        if access_token:
            # Verify the access token with Supabase
            user_data = await auth_service.get_user_by_token(access_token)

            if user_data:
                return MessageResponse(
                    message="Email confirmed successfully! You can now log in.",
                    details={
                        "confirmed": True,
                        "user_id": user_data["id"],
                        "email": user_data["email"]
                    }
                )

        # Handle token hash verification
        if token_hash:
            result = await auth_service.verify_email(token_hash)
            return MessageResponse(
                message=result["message"],
                details={"confirmed": True}
            )

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid confirmation parameters"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during email confirmation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email confirmation failed"
        )


@router.post("/verify-email", response_model=EmailVerificationResponse)
async def verify_email_with_otp(
    verification_data: dict
):
    """
    Verify email using OTP (One-Time Password) code.
    
    This endpoint handles email verification using OTP codes sent via email,
    as an alternative to token-based verification.
    
    Args:
        verification_data: Contains email and OTP code
        
    Returns:
        EmailVerificationResponse: Verification result
        
    Raises:
        HTTPException: If verification fails
    """
    try:
        email = verification_data.get("email")
        otp = verification_data.get("otp")
        
        if not email or not otp:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email and OTP are required"
            )
        
        # Verify OTP with Supabase
        from app.core.supabase import get_auth_client
        auth_client = get_auth_client()
        
        auth_response = auth_client.verify_otp({
            "email": email,
            "token": otp,
            "type": "email"
        })
        
        if not auth_response.user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid OTP code"
            )
        
        return EmailVerificationResponse(
            message="Email verified successfully",
            email=email
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during OTP verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed due to server error"
        )


@router.post("/resend-verification", response_model=MessageResponse)
async def resend_verification_email(
    request: ResendVerificationRequest
):
    """
    Resend email verification to user.
    
    This endpoint:
    - Validates the email address
    - Resends verification email via Supabase
    - Returns success message (regardless of email existence for security)
    
    Args:
        request: Email address for resending verification
        
    Returns:
        MessageResponse: Success message
        
    Raises:
        HTTPException: If resend operation fails
    """
    try:
        result = await auth_service.resend_verification_email(request.email)
        return MessageResponse(
            message=result["message"],
            details={"email": request.email}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error resending verification: {str(e)}")
        # Return success message for security (don't reveal if email exists)
        return MessageResponse(
            message="If the email exists, a verification email has been sent",
            details={"email": request.email}
        )


@router.post("/check-email", response_model=MessageResponse)
async def check_email_availability(
    email_data: EmailVerificationRequest,
    db: Session = Depends(get_db)
):
    """
    Check if email address is available for registration.
    
    This endpoint:
    - Checks if email exists in local database
    - Returns availability status
    - Helps with frontend validation
    
    Args:
        email_data: Email address to check
        db: Database session
        
    Returns:
        MessageResponse: Email availability status
    """
    try:
        from app.models.user import User
        
        existing_user = db.query(User).filter(User.email == email_data.email).first()
        
        if existing_user:
            return MessageResponse(
                message="Email is already registered",
                details={"available": False}
            )
        else:
            return MessageResponse(
                message="Email is available",
                details={"available": True}
            )
            
    except Exception as e:
        logger.error(f"Error checking email availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check email availability"
        )
