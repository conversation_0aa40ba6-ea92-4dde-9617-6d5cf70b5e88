"""
Authentication endpoints for login, logout, and token operations.

This module handles core authentication operations using Supabase Auth:
- User login with email/password
- User logout and session invalidation
- Token refresh operations
- Authentication health checks
"""

from fastapi import APIRouter, Depends, HTTPException, status, Header
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.core.database import get_db
from app.services.auth_service import auth_service
from app.schemas.user import (
    Token,
    RefreshToken,
    MessageResponse,
    UserLoginRequest,
    SupabaseAuthResponse,
    AuthHealthCheck,
    AuthErrorResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/login", response_model=SupabaseAuthResponse)
async def login_user(
    login_data: UserLoginRequest,
    db: Session = Depends(get_db)
):
    """
    Authenticate user with email and password using Supa<PERSON> Auth.
    
    This endpoint:
    - Validates user credentials with Supabase
    - Returns access and refresh tokens
    - Updates user's last login timestamp
    - Handles inactive user accounts
    
    Args:
        login_data: User login credentials
        db: Database session
        
    Returns:
        SupabaseAuthResponse: Authentication tokens and user info
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        user, auth_data = await auth_service.authenticate_user(
            email=login_data.email,
            password=login_data.password,
            db=db
        )
        
        return SupabaseAuthResponse(
            access_token=auth_data["session"].access_token,
            refresh_token=auth_data["session"].refresh_token,
            expires_in=auth_data["session"].expires_in,
            token_type="bearer",
            user=auth_data["user"].__dict__
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed due to server error"
        )


@router.post("/login/oauth", response_model=Token)
async def oauth_login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    OAuth2-compatible login endpoint for compatibility with existing clients.
    
    This endpoint provides OAuth2 password flow compatibility while using
    Supabase Auth under the hood.
    
    Args:
        form_data: OAuth2 password form data
        db: Database session
        
    Returns:
        Token: Access token information
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        user, auth_data = await auth_service.authenticate_user(
            email=form_data.username,  # OAuth2 uses 'username' field for email
            password=form_data.password,
            db=db
        )
        
        return Token(
            access_token=auth_data["session"].access_token,
            token_type="bearer",
            expires_in=auth_data["session"].expires_in,
            refresh_token=auth_data["session"].refresh_token
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during OAuth login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed due to server error"
        )


@router.post("/logout", response_model=MessageResponse)
async def logout_user(
    authorization: Optional[str] = Header(None)
):
    """
    Logout user by invalidating their Supabase session.
    
    This endpoint:
    - Invalidates the user's Supabase session
    - Clears authentication state
    - Logs the logout event
    
    Args:
        authorization: Bearer token from Authorization header
        
    Returns:
        MessageResponse: Success message
        
    Raises:
        HTTPException: If logout fails or token is invalid
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header"
        )
    
    access_token = authorization.split(" ")[1]
    
    try:
        result = await auth_service.logout_user(access_token)
        return MessageResponse(message=result["message"])
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during logout: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed due to server error"
        )


@router.post("/refresh", response_model=Token)
async def refresh_access_token(
    refresh_data: RefreshToken
):
    """
    Refresh user's access token using refresh token.
    
    This endpoint:
    - Validates the refresh token with Supabase
    - Issues a new access token
    - Returns updated token information
    
    Args:
        refresh_data: Refresh token data
        
    Returns:
        Token: New access token information
        
    Raises:
        HTTPException: If token refresh fails
    """
    try:
        auth_data = await auth_service.refresh_token(refresh_data.refresh_token)
        
        return Token(
            access_token=auth_data["session"].access_token,
            token_type="bearer",
            expires_in=auth_data["session"].expires_in,
            refresh_token=auth_data["session"].refresh_token
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during token refresh: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed due to server error"
        )


@router.get("/health", response_model=AuthHealthCheck)
async def auth_health_check(db: Session = Depends(get_db)):
    """
    Check the health of authentication services.
    
    This endpoint:
    - Tests Supabase connection
    - Tests database connection
    - Returns service status information
    
    Args:
        db: Database session
        
    Returns:
        AuthHealthCheck: Health status information
    """
    from datetime import datetime
    from app.core.supabase import get_supabase_client
    
    try:
        # Test Supabase connection
        supabase_client = get_supabase_client()
        supabase_health = supabase_client.health_check()
        
        # Test database connection
        try:
            db.execute("SELECT 1")
            db_status = "healthy"
        except Exception:
            db_status = "unhealthy"
        
        return AuthHealthCheck(
            status="healthy" if supabase_health["status"] == "healthy" and db_status == "healthy" else "unhealthy",
            supabase_connection=supabase_health["status"],
            database_connection=db_status,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return AuthHealthCheck(
            status="unhealthy",
            supabase_connection="error",
            database_connection="error",
            timestamp=datetime.utcnow()
        )
