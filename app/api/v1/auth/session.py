"""
Session management and user information endpoints.

This module handles session-related operations:
- Current user information retrieval
- Session validation and status
- User profile management
- Session activity tracking
"""

from fastapi import APIRouter, Depends, HTTPException, status, Header
from sqlalchemy.orm import Session
from typing import Optional
import logging
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_user
from app.services.auth_service import auth_service
from app.models.user import User
from app.schemas.user import (
    UserResponse,
    UserProfileUpdate,
    UserPreferences,
    UserPreferencesUpdate,
    SessionInfo,
    MessageResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current authenticated user's information.
    
    This endpoint:
    - Returns detailed user profile information
    - Includes user preferences and settings
    - Requires valid authentication token
    
    Args:
        current_user: Authenticated user from dependency
        
    Returns:
        UserResponse: Current user's profile information
    """
    try:
        return UserResponse.from_orm(current_user)
        
    except Exception as e:
        logger.error(f"Error retrieving user info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )


@router.put("/me", response_model=UserResponse)
async def update_user_profile(
    profile_update: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's profile information.
    
    This endpoint:
    - Updates user's profile fields (email, full_name)
    - Validates email uniqueness if changed
    - Updates both local database and Supabase metadata
    
    Args:
        profile_update: Profile fields to update
        current_user: Authenticated user from dependency
        db: Database session
        
    Returns:
        UserResponse: Updated user profile information
        
    Raises:
        HTTPException: If update fails or email is already taken
    """
    try:
        # Check if email is being changed and if it's already taken
        if profile_update.email and profile_update.email != current_user.email:
            existing_user = db.query(User).filter(
                User.email == profile_update.email,
                User.id != current_user.id
            ).first()
            
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email is already registered"
                )
        
        # Update user fields
        if profile_update.email:
            current_user.email = profile_update.email
        
        if profile_update.full_name:
            current_user.full_name = profile_update.full_name
        
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        # Update Supabase user metadata
        try:
            from app.core.supabase import get_supabase_service
            service_client = get_supabase_service()
            
            service_client.auth.admin.update_user_by_id(
                str(current_user.id),
                {
                    "email": current_user.email,
                    "user_metadata": {
                        "full_name": current_user.full_name
                    }
                }
            )
        except Exception as e:
            logger.warning(f"Failed to update Supabase metadata: {str(e)}")
        
        logger.info(f"User profile updated: {current_user.email}")
        return UserResponse.from_orm(current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


@router.get("/me/preferences", response_model=UserPreferences)
async def get_user_preferences(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user's preferences and settings.
    
    Args:
        current_user: Authenticated user from dependency
        
    Returns:
        UserPreferences: User's preferences and settings
    """
    try:
        preferences = current_user.preferences or {}
        
        return UserPreferences(
            theme=preferences.get("theme", "light"),
            language=preferences.get("language", "en"),
            notifications=preferences.get("notifications", {
                "email_notifications": True,
                "push_notifications": True,
                "marketing_emails": False
            }),
            timezone=preferences.get("timezone", "UTC")
        )
        
    except Exception as e:
        logger.error(f"Error retrieving user preferences: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user preferences"
        )


@router.put("/me/preferences", response_model=UserPreferences)
async def update_user_preferences(
    preferences_update: UserPreferencesUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's preferences and settings.
    
    Args:
        preferences_update: Preferences to update
        current_user: Authenticated user from dependency
        db: Database session
        
    Returns:
        UserPreferences: Updated user preferences
        
    Raises:
        HTTPException: If update fails
    """
    try:
        current_preferences = current_user.preferences or {}
        
        # Update preferences with new values
        if preferences_update.theme is not None:
            current_preferences["theme"] = preferences_update.theme
        
        if preferences_update.language is not None:
            current_preferences["language"] = preferences_update.language
        
        if preferences_update.notifications is not None:
            current_preferences["notifications"] = preferences_update.notifications
        
        if preferences_update.timezone is not None:
            current_preferences["timezone"] = preferences_update.timezone
        
        current_user.preferences = current_preferences
        current_user.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"User preferences updated: {current_user.email}")
        
        return UserPreferences(
            theme=current_preferences.get("theme", "light"),
            language=current_preferences.get("language", "en"),
            notifications=current_preferences.get("notifications", {}),
            timezone=current_preferences.get("timezone", "UTC")
        )
        
    except Exception as e:
        logger.error(f"Error updating user preferences: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user preferences"
        )


@router.get("/session", response_model=SessionInfo)
async def get_session_info(
    authorization: Optional[str] = Header(None)
):
    """
    Get current session information and status.
    
    This endpoint:
    - Validates the current session token
    - Returns session status and user information
    - Provides session expiration details
    
    Args:
        authorization: Bearer token from Authorization header
        
    Returns:
        SessionInfo: Current session information
        
    Raises:
        HTTPException: If session is invalid
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header"
        )
    
    access_token = authorization.split(" ")[1]
    
    try:
        user_data = await auth_service.get_user_by_token(access_token)
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired session"
            )
        
        return SessionInfo(
            user_id=user_data["id"],
            email=user_data["email"],
            is_authenticated=True,
            session_expires_at=user_data.get("expires_at"),
            last_activity=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving session info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve session information"
        )


@router.post("/session/validate", response_model=MessageResponse)
async def validate_session(
    authorization: Optional[str] = Header(None)
):
    """
    Validate current session token.
    
    This endpoint:
    - Checks if the session token is valid
    - Returns validation status
    - Useful for frontend session checks
    
    Args:
        authorization: Bearer token from Authorization header
        
    Returns:
        MessageResponse: Session validation result
    """
    if not authorization or not authorization.startswith("Bearer "):
        return MessageResponse(
            message="Invalid session",
            details={"valid": False, "reason": "missing_token"}
        )
    
    access_token = authorization.split(" ")[1]
    
    try:
        user_data = await auth_service.get_user_by_token(access_token)
        
        is_valid = user_data is not None
        
        return MessageResponse(
            message="Session is valid" if is_valid else "Session is invalid",
            details={
                "valid": is_valid,
                "user_id": user_data["id"] if is_valid else None
            }
        )
        
    except Exception as e:
        logger.error(f"Error validating session: {str(e)}")
        return MessageResponse(
            message="Session validation failed",
            details={"valid": False, "reason": "validation_error"}
        )
