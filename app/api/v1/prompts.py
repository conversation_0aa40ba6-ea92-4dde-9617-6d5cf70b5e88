"""
AI Prompt Management API endpoints.
Handles CRUD operations for AI prompts with proper authentication and authorization.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIPrompt, AIAgent
from app.schemas.agent import (
    PromptCreate,
    PromptUpdate,
    PromptResponse,
    PromptListItem,
    PromptListResponse,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for prompt management"""
    # In a real implementation, check user roles/permissions
    return True


def get_prompts_query(db: Session, doc_type: str, tenant_id: Optional[uuid.UUID] = None):
    """Get base query for prompts filtered by doc_type and tenant"""
    query = db.query(AIPrompt).filter(AIPrompt.doc_type == doc_type)
    
    if tenant_id:
        # Include system prompts and tenant-specific prompts
        query = query.filter(
            or_(
                AIPrompt.tenant_id == tenant_id,
                AIPrompt.tenant_id.is_(None)  # System prompts
            )
        )
    else:
        # Only system prompts for non-tenant requests
        query = query.filter(AIPrompt.tenant_id.is_(None))
    
    return query


@router.get("/{doc_type}", response_model=PromptListResponse)
async def get_prompts_for_doc_type(
    doc_type: str,
    prompt_type: Optional[str] = Query(None, description="Filter by prompt type"),
    category: Optional[str] = Query(None, description="Filter by category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get all prompts for a specific document type
    """
    query = get_prompts_query(db, doc_type, current_tenant.id)
    
    # Apply filters
    if prompt_type:
        query = query.filter(AIPrompt.prompt_type == prompt_type)
    if category:
        query = query.filter(AIPrompt.category == category)
    if is_active is not None:
        query = query.filter(AIPrompt.is_active == is_active)
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    prompts = query.order_by(AIPrompt.created_at.desc()).offset(offset).limit(limit).all()
    
    return PromptListResponse(
        prompts=[PromptListItem.from_orm(prompt) for prompt in prompts],
        total=total
    )


@router.get("/{doc_type}/{prompt_id}", response_model=PromptResponse)
async def get_prompt_details(
    doc_type: str,
    prompt_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get specific prompt details
    """
    query = get_prompts_query(db, doc_type, current_tenant.id)
    prompt = query.filter(AIPrompt.id == prompt_id).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found"
        )
    
    return PromptResponse.from_orm(prompt)


@router.post("/{doc_type}", response_model=PromptResponse)
async def create_prompt(
    doc_type: str,
    prompt_data: PromptCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Create a new prompt (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Ensure doc_type matches
    if prompt_data.doc_type != doc_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Prompt doc_type must match URL parameter"
        )
    
    # Check for duplicate names within tenant/doc_type
    existing_prompt = db.query(AIPrompt).filter(
        and_(
            AIPrompt.name == prompt_data.name,
            AIPrompt.doc_type == doc_type,
            AIPrompt.tenant_id == current_tenant.id
        )
    ).first()
    
    if existing_prompt:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Prompt with this name already exists for this document type"
        )
    
    # Validate agent_id if provided
    if prompt_data.agent_id:
        agent = db.query(AIAgent).filter(
            and_(
                AIAgent.id == prompt_data.agent_id,
                AIAgent.doc_type == doc_type,
                or_(
                    AIAgent.tenant_id == current_tenant.id,
                    AIAgent.tenant_id.is_(None)  # System agents
                )
            )
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid agent_id or agent not accessible"
            )
    
    # Create prompt
    prompt = AIPrompt(
        name=prompt_data.name,
        description=prompt_data.description,
        doc_type=doc_type,
        prompt_template=prompt_data.prompt_template,
        variables=prompt_data.variables,
        prompt_type=prompt_data.prompt_type,
        category=prompt_data.category,
        agent_id=prompt_data.agent_id,
        tenant_id=current_tenant.id,
        created_by=current_user.id
    )
    
    db.add(prompt)
    db.commit()
    db.refresh(prompt)
    
    return PromptResponse.from_orm(prompt)


@router.put("/{doc_type}/{prompt_id}", response_model=PromptResponse)
async def update_prompt(
    doc_type: str,
    prompt_id: uuid.UUID,
    prompt_data: PromptUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Update prompt configuration (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get prompt (only tenant-owned prompts can be updated)
    prompt = db.query(AIPrompt).filter(
        and_(
            AIPrompt.id == prompt_id,
            AIPrompt.doc_type == doc_type,
            AIPrompt.tenant_id == current_tenant.id
        )
    ).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found or not editable"
        )
    
    # Check for name conflicts if name is being updated
    if prompt_data.name and prompt_data.name != prompt.name:
        existing_prompt = db.query(AIPrompt).filter(
            and_(
                AIPrompt.name == prompt_data.name,
                AIPrompt.doc_type == doc_type,
                AIPrompt.tenant_id == current_tenant.id,
                AIPrompt.id != prompt_id
            )
        ).first()
        
        if existing_prompt:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Prompt with this name already exists"
            )
    
    # Validate agent_id if being updated
    if prompt_data.agent_id:
        agent = db.query(AIAgent).filter(
            and_(
                AIAgent.id == prompt_data.agent_id,
                AIAgent.doc_type == doc_type,
                or_(
                    AIAgent.tenant_id == current_tenant.id,
                    AIAgent.tenant_id.is_(None)  # System agents
                )
            )
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid agent_id or agent not accessible"
            )
    
    # Update fields
    update_data = prompt_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(prompt, field, value)
    
    db.commit()
    db.refresh(prompt)
    
    return PromptResponse.from_orm(prompt)


@router.delete("/{doc_type}/{prompt_id}", response_model=MessageResponse)
async def delete_prompt(
    doc_type: str,
    prompt_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Delete a prompt (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get prompt (only tenant-owned prompts can be deleted)
    prompt = db.query(AIPrompt).filter(
        and_(
            AIPrompt.id == prompt_id,
            AIPrompt.doc_type == doc_type,
            AIPrompt.tenant_id == current_tenant.id
        )
    ).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found or not deletable"
        )
    
    # Prevent deletion of system default prompts
    if prompt.is_system_default:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete system default prompts"
        )
    
    db.delete(prompt)
    db.commit()
    
    return MessageResponse(message="Prompt deleted successfully")
