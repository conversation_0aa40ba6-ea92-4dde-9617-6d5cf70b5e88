"""
Specialized Analytics API endpoints.
Provides export usage, refinement patterns, and form completion analytics.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, extract
from typing import Optional, List, Dict, Any
from datetime import datetime, date, timedelta
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document, DocumentRefinementJob
from app.models.document_type import DocumentType
from app.models.export import ExportJob, ExportHistory
from app.models.form_schema import FormValidationLog
from app.schemas.analytics import (
    ExportUsageResponse,
    ExportFormatMetrics,
    RefinementPatternsResponse,
    RefinementPattern,
    FormCompletionResponse,
    FormFieldMetrics,
    ChartData,
    MetricValue
)
from app.services.analytics_service import AnalyticsService

router = APIRouter()


def check_analytics_permissions(user: User) -> bool:
    """Check if user has permissions to view analytics"""
    return True


@router.get("/export-usage", response_model=ExportUsageResponse)
async def get_export_usage_analytics(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    document_type: Optional[str] = Query(None, description="Filter by document type"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get export usage analytics and patterns
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    try:
        analytics_service = AnalyticsService(db)
        
        # Base query for export jobs
        export_query = db.query(ExportJob).filter(
            and_(
                ExportJob.tenant_id == current_tenant.id,
                func.date(ExportJob.created_at) >= start_date,
                func.date(ExportJob.created_at) <= end_date
            )
        )
        
        # Filter by document type if specified
        if document_type:
            doc_type_obj = db.query(DocumentType).filter(
                DocumentType.slug == document_type
            ).first()
            if doc_type_obj:
                export_query = export_query.join(
                    Document, ExportJob.document_id == Document.id
                ).filter(
                    Document.document_type_id == doc_type_obj.id
                )
        
        export_jobs = export_query.all()
        
        # Calculate overall metrics
        total_exports = len(export_jobs)
        successful_exports = len([job for job in export_jobs if job.status == 'completed'])
        success_rate = (successful_exports / total_exports * 100) if total_exports > 0 else 0
        
        # Get format breakdown
        format_stats = db.query(
            ExportJob.export_format,
            func.count(ExportJob.id).label('total'),
            func.sum(
                func.case(
                    (ExportJob.status == 'completed', 1),
                    else_=0
                )
            ).label('successful'),
            func.avg(
                func.extract('epoch', ExportJob.completed_at - ExportJob.created_at)
            ).label('avg_time')
        ).filter(
            and_(
                ExportJob.tenant_id == current_tenant.id,
                func.date(ExportJob.created_at) >= start_date,
                func.date(ExportJob.created_at) <= end_date
            )
        ).group_by(
            ExportJob.export_format
        ).all()
        
        format_metrics = []
        format_counts = {}
        
        for stat in format_stats:
            format_name = stat.export_format
            format_counts[format_name] = stat.total
            
            # Get average file size from export history
            avg_file_size = db.query(
                func.avg(ExportHistory.file_size)
            ).join(
                ExportJob, ExportHistory.export_job_id == ExportJob.id
            ).filter(
                and_(
                    ExportJob.export_format == format_name,
                    ExportJob.tenant_id == current_tenant.id,
                    func.date(ExportJob.created_at) >= start_date,
                    func.date(ExportJob.created_at) <= end_date
                )
            ).scalar() or 0
            
            format_metrics.append(ExportFormatMetrics(
                format_name=format_name,
                total_exports=stat.total,
                success_rate=(stat.successful / stat.total * 100) if stat.total > 0 else 0,
                avg_file_size_mb=(avg_file_size / (1024 * 1024)) if avg_file_size else 0,
                avg_export_time=stat.avg_time or 0
            ))
        
        # Find most popular format
        most_popular_format = max(format_counts.items(), key=lambda x: x[1])[0] if format_counts else "N/A"
        
        # Create format distribution chart
        format_distribution = ChartData(
            title="Export Format Distribution",
            type="pie",
            series=[{
                "name": "Export Formats",
                "data": [
                    {"name": format_name, "value": count}
                    for format_name, count in format_counts.items()
                ]
            }]
        )
        
        # Usage by time (daily)
        time_usage = db.query(
            func.date(ExportJob.created_at).label('date'),
            func.count(ExportJob.id).label('count')
        ).filter(
            and_(
                ExportJob.tenant_id == current_tenant.id,
                func.date(ExportJob.created_at) >= start_date,
                func.date(ExportJob.created_at) <= end_date
            )
        ).group_by(
            func.date(ExportJob.created_at)
        ).order_by(
            func.date(ExportJob.created_at)
        ).all()
        
        usage_by_time = ChartData(
            title="Export Usage Over Time",
            type="line",
            series=[{
                "name": "Daily Exports",
                "data": [
                    {"date": result.date.isoformat(), "value": result.count}
                    for result in time_usage
                ]
            }]
        )
        
        # Usage by document type
        doc_type_usage = db.query(
            DocumentType.name,
            func.count(ExportJob.id).label('count')
        ).join(
            Document, ExportJob.document_id == Document.id
        ).join(
            DocumentType, Document.document_type_id == DocumentType.id
        ).filter(
            and_(
                ExportJob.tenant_id == current_tenant.id,
                func.date(ExportJob.created_at) >= start_date,
                func.date(ExportJob.created_at) <= end_date
            )
        ).group_by(
            DocumentType.name
        ).all()
        
        usage_by_document_type = ChartData(
            title="Export Usage by Document Type",
            type="bar",
            series=[{
                "name": "Exports",
                "data": [
                    {"name": result.name, "value": result.count}
                    for result in doc_type_usage
                ]
            }]
        )
        
        # Performance trends
        performance_trends = analytics_service.get_export_usage_trend(
            current_tenant.id, start_date, end_date
        )
        
        # Integration usage (simplified)
        integration_usage = {
            "confluence": format_counts.get("confluence", 0),
            "notion": format_counts.get("notion", 0),
            "jira": format_counts.get("jira", 0),
            "pdf": format_counts.get("pdf", 0),
            "docx": format_counts.get("docx", 0),
            "html": format_counts.get("html", 0)
        }
        
        return ExportUsageResponse(
            period_start=start_date,
            period_end=end_date,
            total_exports=total_exports,
            success_rate=success_rate,
            most_popular_format=most_popular_format,
            format_metrics=format_metrics,
            format_distribution=format_distribution,
            usage_by_time=usage_by_time,
            usage_by_document_type=usage_by_document_type,
            performance_trends=performance_trends,
            integration_usage=integration_usage
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate export usage analytics: {str(e)}"
        )


@router.get("/refinement-patterns/{doc_type}", response_model=RefinementPatternsResponse)
async def get_refinement_patterns(
    doc_type: str,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get refinement patterns analytics for a document type
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.slug == doc_type
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    try:
        analytics_service = AnalyticsService(db)
        
        # Get refinement jobs for this document type
        refinement_jobs = db.query(DocumentRefinementJob).join(
            Document, DocumentRefinementJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(DocumentRefinementJob.started_at) >= start_date,
                func.date(DocumentRefinementJob.started_at) <= end_date
            )
        ).all()
        
        total_refinements = len(refinement_jobs)
        successful_refinements = len([job for job in refinement_jobs if job.status == 'completed'])
        refinement_success_rate = (successful_refinements / total_refinements * 100) if total_refinements > 0 else 0
        
        # Get documents with refinements
        documents_with_refinements = db.query(Document).join(
            DocumentRefinementJob, DocumentRefinementJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(DocumentRefinementJob.started_at) >= start_date,
                func.date(DocumentRefinementJob.started_at) <= end_date
            )
        ).distinct().all()
        
        total_documents = db.query(Document).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).count()
        
        avg_refinements_per_doc = total_refinements / len(documents_with_refinements) if documents_with_refinements else 0
        
        # Analyze refinement patterns
        pattern_stats = db.query(
            DocumentRefinementJob.refinement_type,
            func.count(DocumentRefinementJob.id).label('frequency'),
            func.sum(
                func.case(
                    (DocumentRefinementJob.status == 'completed', 1),
                    else_=0
                )
            ).label('successful')
        ).join(
            Document, DocumentRefinementJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(DocumentRefinementJob.started_at) >= start_date,
                func.date(DocumentRefinementJob.started_at) <= end_date
            )
        ).group_by(
            DocumentRefinementJob.refinement_type
        ).all()
        
        common_patterns = []
        for stat in pattern_stats:
            success_rate = (stat.successful / stat.frequency * 100) if stat.frequency > 0 else 0
            
            # Analyze common triggers (simplified)
            common_triggers = ["user_feedback", "quality_improvement", "style_adjustment"]
            
            common_patterns.append(RefinementPattern(
                pattern_type=stat.refinement_type,
                frequency=stat.frequency,
                avg_improvement_score=75.0,  # TODO: Implement improvement scoring
                common_triggers=common_triggers,
                success_rate=success_rate
            ))
        
        # Refinement trends over time
        trend_data = db.query(
            func.date(DocumentRefinementJob.started_at).label('date'),
            func.count(DocumentRefinementJob.id).label('count')
        ).join(
            Document, DocumentRefinementJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(DocumentRefinementJob.started_at) >= start_date,
                func.date(DocumentRefinementJob.started_at) <= end_date
            )
        ).group_by(
            func.date(DocumentRefinementJob.started_at)
        ).order_by(
            func.date(DocumentRefinementJob.started_at)
        ).all()
        
        refinement_trends = ChartData(
            title="Refinement Trends",
            type="line",
            series=[{
                "name": "Daily Refinements",
                "data": [
                    {"date": result.date.isoformat(), "value": result.count}
                    for result in trend_data
                ]
            }]
        )
        
        # Before/after metrics (simplified)
        before_after_metrics = {
            "quality_score": analytics_service.calculate_metric_value(85.0, 75.0),
            "user_satisfaction": analytics_service.calculate_metric_value(4.2, 3.8),
            "completion_rate": analytics_service.calculate_metric_value(92.0, 85.0)
        }
        
        # User satisfaction impact
        user_satisfaction_impact = 15.0  # Percentage improvement
        
        # Optimization suggestions
        optimization_suggestions = []
        if avg_refinements_per_doc > 2:
            optimization_suggestions.append("High refinement rate suggests initial AI output quality could be improved")
        if refinement_success_rate < 80:
            optimization_suggestions.append("Refinement success rate is below optimal - review refinement processes")
        
        optimization_suggestions.extend([
            "Consider implementing automated quality checks before refinement",
            "Analyze most common refinement patterns to improve initial generation",
            "Provide better guidance to users on effective refinement requests"
        ])
        
        return RefinementPatternsResponse(
            document_type=document_type.name,
            period_start=start_date,
            period_end=end_date,
            total_refinements=total_refinements,
            avg_refinements_per_doc=avg_refinements_per_doc,
            refinement_success_rate=refinement_success_rate,
            common_patterns=common_patterns,
            refinement_trends=refinement_trends,
            before_after_metrics=before_after_metrics,
            user_satisfaction_impact=user_satisfaction_impact,
            optimization_suggestions=optimization_suggestions
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate refinement patterns analytics: {str(e)}"
        )


@router.get("/form-completion/{doc_type}", response_model=FormCompletionResponse)
async def get_form_completion_analytics(
    doc_type: str,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get form completion analytics for a document type
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.slug == doc_type
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    try:
        # Get form validation logs for this document type
        validation_logs = db.query(FormValidationLog).filter(
            and_(
                func.date(FormValidationLog.created_at) >= start_date,
                func.date(FormValidationLog.created_at) <= end_date
            )
        ).all()
        
        # Filter logs that match this document type (simplified)
        # In a real implementation, you'd have a proper relationship
        relevant_logs = [log for log in validation_logs if doc_type in str(log.form_data)]
        
        # Get documents created for this type (form completions)
        completed_documents = db.query(Document).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).all()
        
        total_starts = len(relevant_logs)  # Form validation attempts
        total_completions = len(completed_documents)  # Successful document creations
        completion_rate = (total_completions / total_starts * 100) if total_starts > 0 else 0
        
        # Calculate average completion time (simplified)
        completion_times = []
        for doc in completed_documents:
            # Estimate completion time based on creation timestamp
            # In a real implementation, you'd track form start/end times
            completion_times.append(300)  # 5 minutes average
        
        avg_completion_time = sum(completion_times) / len(completion_times) if completion_times else 0
        
        # Analyze form fields (simplified - using document type form schema)
        form_schema = document_type.form_schema
        field_metrics = []
        
        if isinstance(form_schema, dict) and 'questions' in form_schema:
            for question in form_schema['questions']:
                field_name = question.get('id', 'unknown')
                
                # Calculate metrics for this field (simplified)
                field_metrics.append(FormFieldMetrics(
                    field_name=field_name,
                    completion_rate=85.0,  # TODO: Calculate from actual data
                    avg_time_spent=30.0,   # TODO: Calculate from actual data
                    error_rate=5.0,        # TODO: Calculate from validation logs
                    abandonment_rate=10.0  # TODO: Calculate from actual data
                ))
        
        # Create completion funnel chart
        funnel_data = [
            {"stage": "Form Started", "value": total_starts},
            {"stage": "Form Completed", "value": total_completions}
        ]
        
        completion_funnel = ChartData(
            title="Form Completion Funnel",
            type="funnel",
            series=[{
                "name": "Completion Funnel",
                "data": funnel_data
            }]
        )
        
        # User behavior metrics (simplified)
        bounce_rate = 15.0  # Percentage who leave without completing any fields
        return_rate = 25.0  # Percentage who come back to complete
        
        # Device breakdown (simplified)
        device_breakdown = {
            "desktop": 70.0,
            "mobile": 25.0,
            "tablet": 5.0
        }
        
        # Identify bottleneck fields
        bottleneck_fields = []
        for field in field_metrics:
            if field.error_rate > 10 or field.abandonment_rate > 20:
                bottleneck_fields.append(field.field_name)
        
        # Improvement opportunities
        improvement_opportunities = [
            "Simplify form fields with high error rates",
            "Add progress indicators to reduce abandonment",
            "Optimize mobile form experience",
            "Provide better field validation messages"
        ]
        
        if completion_rate < 70:
            improvement_opportunities.append("Overall completion rate is below optimal - review form design")
        
        return FormCompletionResponse(
            document_type=document_type.name,
            period_start=start_date,
            period_end=end_date,
            total_starts=total_starts,
            total_completions=total_completions,
            completion_rate=completion_rate,
            avg_completion_time=avg_completion_time,
            field_metrics=field_metrics,
            completion_funnel=completion_funnel,
            bounce_rate=bounce_rate,
            return_rate=return_rate,
            device_breakdown=device_breakdown,
            bottleneck_fields=bottleneck_fields,
            improvement_opportunities=improvement_opportunities
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate form completion analytics: {str(e)}"
        )
