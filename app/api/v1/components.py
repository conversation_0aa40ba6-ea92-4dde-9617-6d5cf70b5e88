"""
Component library API endpoints.
Handles reusable components and section patterns.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.template import ComponentLibrary, SectionLibrary, ComponentUsageLog, SectionUsageLog
from app.schemas.template import (
    ComponentCreate,
    ComponentUpdate,
    ComponentResponse,
    ComponentListResponse,
    ComponentListItem,
    SectionCreate,
    SectionResponse,
    SectionListResponse,
    MessageResponse,
    VisibilityLevel,
    ComponentType
)

router = APIRouter()


def get_accessible_components_query(db: Session, user: User, tenant: Tenant, doc_type: Optional[str] = None):
    """Get query for components accessible to the user"""
    query = db.query(ComponentLibrary).filter(
        ComponentLibrary.is_active == True,
        or_(
            # User's own components
            and_(
                ComponentLibrary.user_id == user.id,
                ComponentLibrary.tenant_id == tenant.id
            ),
            # Tenant-visible components
            and_(
                ComponentLibrary.tenant_id == tenant.id,
                ComponentLibrary.visibility == VisibilityLevel.TENANT
            ),
            # Public components
            ComponentLibrary.visibility == VisibilityLevel.PUBLIC
        )
    )
    
    # Filter by document type if specified
    if doc_type and doc_type != "common":
        query = query.filter(
            or_(
                ComponentLibrary.document_types.contains([doc_type]),
                ComponentLibrary.document_types == []  # Universal components
            )
        )
    elif doc_type == "common":
        query = query.filter(ComponentLibrary.document_types == [])
    
    return query


def get_accessible_sections_query(db: Session, user: User, tenant: Tenant, doc_type: str):
    """Get query for sections accessible to the user"""
    return db.query(SectionLibrary).filter(
        SectionLibrary.document_type == doc_type,
        SectionLibrary.is_active == True,
        or_(
            # User's own sections
            and_(
                SectionLibrary.user_id == user.id,
                SectionLibrary.tenant_id == tenant.id
            ),
            # Tenant-visible sections
            and_(
                SectionLibrary.tenant_id == tenant.id,
                SectionLibrary.visibility == VisibilityLevel.TENANT
            ),
            # Public sections
            SectionLibrary.visibility == VisibilityLevel.PUBLIC
        )
    )


# Component endpoints
@router.get("/components/{doc_type}", response_model=ComponentListResponse)
async def get_components(
    doc_type: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    search: Optional[str] = Query(None, description="Search in name and description"),
    component_type: Optional[ComponentType] = Query(None, description="Filter by component type"),
    category: Optional[str] = Query(None, description="Filter by category"),
    tags: Optional[str] = Query(None, description="Comma-separated tags to filter by"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get reusable components for document type
    """
    query = get_accessible_components_query(db, current_user, current_tenant, doc_type)
    
    # Apply filters
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                ComponentLibrary.name.ilike(search_term),
                ComponentLibrary.description.ilike(search_term)
            )
        )
    
    if component_type:
        query = query.filter(ComponentLibrary.component_type == component_type)
    
    if category:
        query = query.filter(ComponentLibrary.category == category)
    
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        for tag in tag_list:
            query = query.filter(ComponentLibrary.tags.contains([tag]))
    
    # Get total count
    total_count = query.count()
    
    # Apply pagination and ordering
    components = query.order_by(desc(ComponentLibrary.usage_count), ComponentLibrary.name).offset(offset).limit(limit).all()
    
    # Get available categories
    categories_query = get_accessible_components_query(db, current_user, current_tenant, doc_type)
    categories = db.query(ComponentLibrary.category).filter(
        ComponentLibrary.category.isnot(None)
    ).distinct().all()
    category_list = [cat[0] for cat in categories if cat[0]]
    
    # Convert to list items
    component_items = [
        ComponentListItem(
            id=component.id,
            name=component.name,
            description=component.description,
            component_type=component.component_type,
            document_types=component.document_types,
            category=component.category,
            tags=component.tags,
            usage_count=component.usage_count,
            created_at=component.created_at
        )
        for component in components
    ]
    
    return ComponentListResponse(
        components=component_items,
        total_count=total_count,
        document_type=doc_type if doc_type != "common" else None,
        categories=category_list
    )


@router.post("/components/{doc_type}", response_model=ComponentResponse)
async def save_component(
    doc_type: str,
    component_data: ComponentCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Save component to library
    """
    # Set document types
    document_types = component_data.document_types
    if doc_type != "common" and doc_type not in document_types:
        document_types.append(doc_type)
    
    # Create component
    component = ComponentLibrary(
        name=component_data.name,
        description=component_data.description,
        content=component_data.content,
        component_type=component_data.component_type,
        document_types=document_types,
        variables=component_data.variables,
        component_metadata=component_data.component_metadata,
        visibility=component_data.visibility,
        tags=component_data.tags,
        category=component_data.category,
        user_id=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(component)
    db.commit()
    db.refresh(component)
    
    return component


@router.get("/components/{doc_type}/{component_id}", response_model=ComponentResponse)
async def get_component(
    doc_type: str,
    component_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific component
    """
    component = get_accessible_components_query(db, current_user, current_tenant, doc_type).filter(
        ComponentLibrary.id == component_id
    ).first()
    
    if not component:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Component not found"
        )
    
    # Log usage
    usage_log = ComponentUsageLog(
        component_id=component.id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        document_type=doc_type
    )
    
    db.add(usage_log)
    
    # Update usage count
    component.usage_count += 1
    component.last_used_at = datetime.now(timezone.utc)
    
    db.commit()
    
    return component


# Section endpoints
@router.get("/sections/{doc_type}", response_model=SectionListResponse)
async def get_sections(
    doc_type: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    search: Optional[str] = Query(None, description="Search in name and description"),
    tags: Optional[str] = Query(None, description="Comma-separated tags to filter by"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get common section patterns
    """
    query = get_accessible_sections_query(db, current_user, current_tenant, doc_type)
    
    # Apply filters
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                SectionLibrary.name.ilike(search_term),
                SectionLibrary.description.ilike(search_term)
            )
        )
    
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        for tag in tag_list:
            query = query.filter(SectionLibrary.tags.contains([tag]))
    
    # Get total count
    total_count = query.count()
    
    # Apply pagination and ordering
    sections = query.order_by(SectionLibrary.order_hint, SectionLibrary.name).offset(offset).limit(limit).all()
    
    return SectionListResponse(
        sections=sections,
        total_count=total_count,
        document_type=doc_type
    )


@router.post("/sections/{doc_type}", response_model=SectionResponse)
async def save_section(
    doc_type: str,
    section_data: SectionCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Save section pattern
    """
    # Validate document type matches
    if section_data.document_type != doc_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Document type in URL must match section document type"
        )
    
    # Create section
    section = SectionLibrary(
        name=section_data.name,
        description=section_data.description,
        content=section_data.content,
        document_type=section_data.document_type,
        variables=section_data.variables,
        section_metadata=section_data.section_metadata,
        visibility=section_data.visibility,
        tags=section_data.tags,
        order_hint=section_data.order_hint,
        user_id=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(section)
    db.commit()
    db.refresh(section)
    
    return section


@router.get("/sections/{doc_type}/{section_id}", response_model=SectionResponse)
async def get_section(
    doc_type: str,
    section_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific section
    """
    section = get_accessible_sections_query(db, current_user, current_tenant, doc_type).filter(
        SectionLibrary.id == section_id
    ).first()
    
    if not section:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Section not found"
        )
    
    # Log usage
    usage_log = SectionUsageLog(
        section_id=section.id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        document_type=doc_type
    )
    
    db.add(usage_log)
    
    # Update usage count
    section.usage_count += 1
    section.last_used_at = datetime.now(timezone.utc)
    
    db.commit()
    
    return section
