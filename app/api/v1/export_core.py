"""
Core export API endpoints for basic document export formats.
Handles Word, PDF, Markdown, and document-type specific exports.
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Union
import uuid
from datetime import datetime, timezone, timedelta

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.export import ExportJob, ExportFile
from app.schemas.export import (
    ExportJobResponse,
    WordExportRequest,
    PDFExportRequest,
    MarkdownExportRequest,
    OpenAPIExportRequest,
    SwaggerExportRequest,
    PlantUMLExportRequest,
    WireframesExportRequest,
    MessageResponse
)

router = APIRouter()


async def create_export_job(
    document_id: uuid.UUID,
    export_format: str,
    export_options: dict,
    user: User,
    tenant: Tenant,
    db: Session
) -> ExportJob:
    """Create a new export job"""
    # Verify document exists and user has access
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.tenant_id == tenant.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Create export job
    export_job = ExportJob(
        document_id=document_id,
        export_format=export_format,
        export_options=export_options,
        user_id=user.id,
        tenant_id=tenant.id,
        estimated_completion=datetime.now(timezone.utc) + timedelta(minutes=5)
    )
    
    db.add(export_job)
    db.commit()
    db.refresh(export_job)
    
    return export_job


async def process_export_job(job_id: uuid.UUID, db: Session):
    """Background task to process export job"""
    # This would be implemented with actual export logic
    # For now, we'll simulate the process
    job = db.query(ExportJob).filter(ExportJob.id == job_id).first()
    if not job:
        return
    
    try:
        # Update job status
        job.status = "processing"
        job.started_at = datetime.now(timezone.utc)
        job.progress = 10
        db.commit()
        
        # Simulate processing time
        import asyncio
        await asyncio.sleep(2)
        
        # Create result file (simulated)
        export_file = ExportFile(
            export_job_id=job.id,
            filename=f"export_{job.id}.{_get_file_extension(job.export_format)}",
            original_filename=f"{job.document.title}.{_get_file_extension(job.export_format)}",
            file_path=f"/exports/{job.id}",
            file_size=1024,  # Simulated size
            content_type=_get_content_type(job.export_format),
            expires_at=datetime.now(timezone.utc) + timedelta(days=7)
        )
        
        db.add(export_file)
        
        # Complete job
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)
        job.result_file_id = export_file.id
        
        db.commit()
        
    except Exception as e:
        job.status = "failed"
        job.error_message = str(e)
        db.commit()


def _get_file_extension(export_format: str) -> str:
    """Get file extension for export format"""
    extensions = {
        "word": "docx",
        "pdf": "pdf",
        "markdown": "md",
        "openapi": "yaml",
        "swagger": "html",
        "plantuml": "png",
        "wireframes": "png"
    }
    return extensions.get(export_format, "txt")


def _get_content_type(export_format: str) -> str:
    """Get MIME type for export format"""
    content_types = {
        "word": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "pdf": "application/pdf",
        "markdown": "text/markdown",
        "openapi": "application/x-yaml",
        "swagger": "text/html",
        "plantuml": "image/png",
        "wireframes": "image/png"
    }
    return content_types.get(export_format, "application/octet-stream")


# Generic export endpoint
@router.post("/{doc_id}/export/{format}", response_model=ExportJobResponse)
async def export_document_generic(
    doc_id: uuid.UUID,
    format: str,
    background_tasks: BackgroundTasks,
    export_options: dict = {},
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Generic export endpoint - format varies by document type
    """
    # Validate format
    supported_formats = ["word", "pdf", "markdown", "openapi", "swagger", "plantuml", "wireframes"]
    if format not in supported_formats:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported export format: {format}"
        )
    
    # Create export job
    export_job = await create_export_job(
        doc_id, format, export_options, current_user, current_tenant, db
    )
    
    # Start background processing
    background_tasks.add_task(process_export_job, export_job.id, db)
    
    return export_job


# Word export
@router.post("/{doc_id}/export/word", response_model=ExportJobResponse)
async def export_to_word(
    doc_id: uuid.UUID,
    request: WordExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export document as Word document
    """
    export_job = await create_export_job(
        doc_id, "word", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job


# PDF export
@router.post("/{doc_id}/export/pdf", response_model=ExportJobResponse)
async def export_to_pdf(
    doc_id: uuid.UUID,
    request: PDFExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export document as PDF
    """
    export_job = await create_export_job(
        doc_id, "pdf", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job


# Markdown export
@router.post("/{doc_id}/export/markdown", response_model=ExportJobResponse)
async def export_to_markdown(
    doc_id: uuid.UUID,
    request: MarkdownExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export document as Markdown
    """
    export_job = await create_export_job(
        doc_id, "markdown", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job


# Document type specific exports
@router.post("/{doc_id}/export/openapi", response_model=ExportJobResponse)
async def export_to_openapi(
    doc_id: uuid.UUID,
    request: OpenAPIExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export PRD as OpenAPI specification
    """
    export_job = await create_export_job(
        doc_id, "openapi", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job


@router.post("/{doc_id}/export/swagger", response_model=ExportJobResponse)
async def export_to_swagger(
    doc_id: uuid.UUID,
    request: SwaggerExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export API documentation as Swagger UI
    """
    export_job = await create_export_job(
        doc_id, "swagger", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job


@router.post("/{doc_id}/export/plantuml", response_model=ExportJobResponse)
async def export_to_plantuml(
    doc_id: uuid.UUID,
    request: PlantUMLExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export architecture document as PlantUML diagrams
    """
    export_job = await create_export_job(
        doc_id, "plantuml", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job


@router.post("/{doc_id}/export/wireframes", response_model=ExportJobResponse)
async def export_to_wireframes(
    doc_id: uuid.UUID,
    request: WireframesExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export design document as wireframes
    """
    export_job = await create_export_job(
        doc_id, "wireframes", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_export_job, export_job.id, db)
    return export_job
