"""
Team Performance and AI Effectiveness Analytics API endpoints.
Provides team collaboration metrics and AI performance analytics.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc, distinct
from typing import Optional, List
from datetime import datetime, date, timedelta
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document, DocumentRefinementJob
from app.models.document_type import DocumentType
from app.models.agent import AgentJob, AIAgent
from app.models.learning import AIFeedback
from app.schemas.analytics import (
    TeamPerformanceResponse,
    TeamMemberMetrics,
    AIEffectivenessResponse,
    AIModelMetrics,
    ChartData
)
from app.services.analytics_service import AnalyticsService

router = APIRouter()


def check_analytics_permissions(user: User) -> bool:
    """Check if user has permissions to view analytics"""
    return True


@router.get("/team-performance", response_model=TeamPerformanceResponse)
async def get_team_performance(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get team performance analytics
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    try:
        analytics_service = AnalyticsService(db)
        
        # Get all team members
        all_members = db.query(User).filter(
            User.tenant_id == current_tenant.id
        ).all()
        
        # Get active members (those who created documents in the period)
        active_member_ids = db.query(distinct(Document.user_id)).filter(
            and_(
                Document.tenant_id == current_tenant.id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).all()
        
        active_member_ids = [uid[0] for uid in active_member_ids]
        
        # Get new members (joined during the period)
        new_members = db.query(User).filter(
            and_(
                User.tenant_id == current_tenant.id,
                func.date(User.created_at) >= start_date,
                func.date(User.created_at) <= end_date
            )
        ).count()
        
        # Get total documents created in period
        total_documents = db.query(Document).filter(
            and_(
                Document.tenant_id == current_tenant.id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).count()
        
        # Calculate completion rate
        completed_documents = db.query(Document).filter(
            and_(
                Document.tenant_id == current_tenant.id,
                Document.status.in_(["approved", "published"]),
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).count()
        
        completion_rate = (completed_documents / total_documents * 100) if total_documents > 0 else 0
        
        # Calculate average documents per member
        avg_docs_per_member = total_documents / len(active_member_ids) if active_member_ids else 0
        
        # Get collaboration metrics
        documents_shared = db.query(Document).filter(
            and_(
                Document.tenant_id == current_tenant.id,
                func.json_array_length(Document.shared_with) > 0,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).count()
        
        # Calculate collaboration score (simplified)
        collaboration_score = min(100, (documents_shared / total_documents * 100)) if total_documents > 0 else 0
        
        # Get productivity trend
        productivity_data = analytics_service.get_document_creation_trend(
            current_tenant.id, start_date, end_date
        )
        
        # Get collaboration trend (documents shared over time)
        collab_results = db.query(
            func.date(Document.created_at).label('date'),
            func.count(Document.id).label('shared_count')
        ).filter(
            and_(
                Document.tenant_id == current_tenant.id,
                func.json_array_length(Document.shared_with) > 0,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).group_by(
            func.date(Document.created_at)
        ).order_by(
            func.date(Document.created_at)
        ).all()
        
        collaboration_trend = ChartData(
            title="Collaboration Trend",
            type="line",
            series=[{
                "name": "Shared Documents",
                "data": [
                    {"date": result.date.isoformat(), "value": result.shared_count}
                    for result in collab_results
                ]
            }]
        )
        
        # Get individual team member metrics
        team_members = []
        top_performers = []
        
        for member in all_members:
            if member.id in active_member_ids:
                # Get member's documents
                member_docs = db.query(Document).filter(
                    and_(
                        Document.user_id == member.id,
                        func.date(Document.created_at) >= start_date,
                        func.date(Document.created_at) <= end_date
                    )
                ).all()
                
                docs_created = len(member_docs)
                
                # Calculate collaboration score for this member
                shared_docs = [d for d in member_docs if d.shared_with and len(d.shared_with) > 0]
                member_collab_score = (len(shared_docs) / docs_created * 100) if docs_created > 0 else 0
                
                # AI usage rate (documents with AI generation)
                ai_docs = db.query(AgentJob).filter(
                    and_(
                        AgentJob.user_id == member.id,
                        func.date(AgentJob.created_at) >= start_date,
                        func.date(AgentJob.created_at) <= end_date
                    )
                ).count()
                
                ai_usage_rate = (ai_docs / docs_created * 100) if docs_created > 0 else 0
                
                member_metrics = TeamMemberMetrics(
                    user_id=member.id,
                    user_name=member.full_name,
                    documents_created=docs_created,
                    avg_completion_time=None,  # TODO: Implement completion time tracking
                    collaboration_score=member_collab_score,
                    ai_usage_rate=ai_usage_rate,
                    last_active=member.last_login or member.created_at
                )
                
                team_members.append(member_metrics)
                
                # Add to top performers if they have good metrics
                if docs_created >= 3 or member_collab_score >= 50:
                    top_performers.append(member_metrics)
        
        # Sort top performers by documents created
        top_performers.sort(key=lambda x: x.documents_created, reverse=True)
        top_performers = top_performers[:5]  # Top 5
        
        return TeamPerformanceResponse(
            period_start=start_date,
            period_end=end_date,
            total_members=len(all_members),
            active_members=len(active_member_ids),
            new_members=new_members,
            total_documents=total_documents,
            avg_documents_per_member=avg_docs_per_member,
            completion_rate=completion_rate,
            collaboration_score=collaboration_score,
            documents_shared=documents_shared,
            comments_made=0,  # TODO: Implement comment tracking
            productivity_trend=productivity_data,
            collaboration_trend=collaboration_trend,
            top_performers=top_performers,
            team_members=team_members
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate team performance analytics: {str(e)}"
        )


@router.get("/ai-effectiveness/{doc_type}", response_model=AIEffectivenessResponse)
async def get_ai_effectiveness(
    doc_type: str,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get AI effectiveness analytics for a specific document type
    """
    if not check_analytics_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view analytics"
        )
    
    # Default to last 30 days
    if not end_date:
        end_date = date.today()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.slug == doc_type
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    try:
        analytics_service = AnalyticsService(db)
        
        # Get AI jobs for this document type
        ai_jobs = db.query(AgentJob).join(
            Document, AgentJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                AgentJob.tenant_id == current_tenant.id,
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).all()
        
        total_generations = len(ai_jobs)
        successful_generations = len([job for job in ai_jobs if job.status == 'completed'])
        success_rate = (successful_generations / total_generations * 100) if total_generations > 0 else 0
        
        # Get user feedback for AI outputs
        feedback = db.query(AIFeedback).filter(
            and_(
                AIFeedback.document_type_id == document_type.id,
                AIFeedback.tenant_id == current_tenant.id,
                func.date(AIFeedback.created_at) >= start_date,
                func.date(AIFeedback.created_at) <= end_date
            )
        ).all()
        
        avg_user_rating = sum(f.rating for f in feedback) / len(feedback) if feedback else 0
        
        # Calculate refinement rate
        documents_with_ai = db.query(Document).join(
            AgentJob, AgentJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                Document.tenant_id == current_tenant.id,
                func.date(Document.created_at) >= start_date,
                func.date(Document.created_at) <= end_date
            )
        ).all()
        
        documents_with_refinements = 0
        for doc in documents_with_ai:
            refinement_count = db.query(DocumentRefinementJob).filter(
                DocumentRefinementJob.document_id == doc.id
            ).count()
            if refinement_count > 0:
                documents_with_refinements += 1
        
        refinement_rate = (documents_with_refinements / len(documents_with_ai) * 100) if documents_with_ai else 0
        
        # Get performance trend
        performance_results = db.query(
            func.date(AgentJob.created_at).label('date'),
            func.count(AgentJob.id).label('total'),
            func.sum(
                func.case(
                    (AgentJob.status == 'completed', 1),
                    else_=0
                )
            ).label('successful')
        ).join(
            Document, AgentJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                AgentJob.tenant_id == current_tenant.id,
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).group_by(
            func.date(AgentJob.created_at)
        ).order_by(
            func.date(AgentJob.created_at)
        ).all()
        
        performance_trend = ChartData(
            title="AI Performance Trend",
            type="line",
            series=[{
                "name": "Success Rate (%)",
                "data": [
                    {
                        "date": result.date.isoformat(),
                        "value": (result.successful / result.total * 100) if result.total > 0 else 0
                    }
                    for result in performance_results
                ]
            }]
        )
        
        # Get satisfaction trend
        satisfaction_results = db.query(
            func.date(AIFeedback.created_at).label('date'),
            func.avg(AIFeedback.rating).label('avg_rating')
        ).filter(
            and_(
                AIFeedback.document_type_id == document_type.id,
                AIFeedback.tenant_id == current_tenant.id,
                func.date(AIFeedback.created_at) >= start_date,
                func.date(AIFeedback.created_at) <= end_date
            )
        ).group_by(
            func.date(AIFeedback.created_at)
        ).order_by(
            func.date(AIFeedback.created_at)
        ).all()
        
        satisfaction_trend = ChartData(
            title="User Satisfaction Trend",
            type="line",
            series=[{
                "name": "Average Rating",
                "data": [
                    {"date": result.date.isoformat(), "value": float(result.avg_rating)}
                    for result in satisfaction_results
                ]
            }]
        )
        
        # Get model performance breakdown
        model_stats = db.query(
            AgentJob.model_used,
            func.count(AgentJob.id).label('total'),
            func.sum(
                func.case(
                    (AgentJob.status == 'completed', 1),
                    else_=0
                )
            ).label('successful'),
            func.avg(AgentJob.execution_time_ms).label('avg_time'),
            func.avg(AgentJob.tokens_used).label('avg_tokens')
        ).join(
            Document, AgentJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                AgentJob.tenant_id == current_tenant.id,
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).group_by(
            AgentJob.model_used
        ).all()
        
        model_performance = []
        for stat in model_stats:
            if stat.model_used:
                # Get user satisfaction for this model
                model_feedback = db.query(AIFeedback).join(
                    AgentJob, AIFeedback.document_id == AgentJob.document_id
                ).filter(
                    and_(
                        AgentJob.model_used == stat.model_used,
                        AIFeedback.document_type_id == document_type.id,
                        AIFeedback.tenant_id == current_tenant.id
                    )
                ).all()
                
                model_satisfaction = sum(f.rating for f in model_feedback) / len(model_feedback) if model_feedback else 0
                
                model_performance.append(AIModelMetrics(
                    model_name=stat.model_used,
                    total_generations=stat.total,
                    success_rate=(stat.successful / stat.total * 100) if stat.total > 0 else 0,
                    avg_generation_time=stat.avg_time / 1000 if stat.avg_time else 0,  # Convert to seconds
                    avg_tokens_used=stat.avg_tokens or 0,
                    user_satisfaction=model_satisfaction
                ))
        
        # Token usage trend
        token_results = db.query(
            func.date(AgentJob.created_at).label('date'),
            func.sum(AgentJob.tokens_used).label('total_tokens')
        ).join(
            Document, AgentJob.document_id == Document.id
        ).filter(
            and_(
                Document.document_type_id == document_type.id,
                AgentJob.tenant_id == current_tenant.id,
                AgentJob.tokens_used.isnot(None),
                func.date(AgentJob.created_at) >= start_date,
                func.date(AgentJob.created_at) <= end_date
            )
        ).group_by(
            func.date(AgentJob.created_at)
        ).order_by(
            func.date(AgentJob.created_at)
        ).all()
        
        token_usage_trend = ChartData(
            title="Token Usage Trend",
            type="bar",
            series=[{
                "name": "Tokens Used",
                "data": [
                    {"date": result.date.isoformat(), "value": result.total_tokens or 0}
                    for result in token_results
                ]
            }]
        )
        
        # Generate insights
        common_issues = []
        improvement_suggestions = []
        
        if success_rate < 80:
            common_issues.append("AI generation success rate is below optimal threshold")
            improvement_suggestions.append("Review and optimize AI prompts and model configurations")
        
        if avg_user_rating < 3.5:
            common_issues.append("User satisfaction with AI outputs is below average")
            improvement_suggestions.append("Analyze user feedback to identify common quality issues")
        
        if refinement_rate > 50:
            common_issues.append("High refinement rate indicates AI outputs need frequent adjustments")
            improvement_suggestions.append("Fine-tune AI models based on refinement patterns")
        
        # Cost analysis (simplified)
        total_tokens = sum(job.tokens_used for job in ai_jobs if job.tokens_used)
        estimated_cost = total_tokens * 0.00001  # Rough estimate
        
        cost_analysis = {
            "total_tokens": total_tokens,
            "estimated_cost_usd": estimated_cost,
            "cost_per_document": estimated_cost / len(documents_with_ai) if documents_with_ai else 0
        }
        
        return AIEffectivenessResponse(
            document_type=document_type.name,
            period_start=start_date,
            period_end=end_date,
            total_generations=total_generations,
            success_rate=success_rate,
            avg_user_rating=avg_user_rating,
            refinement_rate=refinement_rate,
            performance_trend=performance_trend,
            satisfaction_trend=satisfaction_trend,
            model_performance=model_performance,
            common_issues=common_issues,
            improvement_suggestions=improvement_suggestions,
            token_usage_trend=token_usage_trend,
            cost_analysis=cost_analysis
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate AI effectiveness analytics: {str(e)}"
        )
