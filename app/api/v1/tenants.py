from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.prd import PRD
from app.schemas.tenant import (
    TenantResponse,
    TenantUpdate,
    TenantSettingsUpdate,
    TenantMember,
    TenantMemberInvite,
    TenantMemberRoleUpdate,
    TenantUsageStats,
    MessageResponse
)

router = APIRouter()

@router.get("/current", response_model=TenantResponse)
async def get_current_tenant_info(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get current tenant information
    """
    return current_tenant

@router.get("/settings", response_model=TenantResponse)
async def get_tenant_settings(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get tenant settings
    """
    return current_tenant

@router.put("/current", response_model=TenantResponse)
async def update_current_tenant(
    tenant_update: TenantUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update current tenant info
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can update tenant information"
        )

    # Check if slug is being updated and if it already exists
    if tenant_update.slug and tenant_update.slug != current_tenant.slug:
        existing_tenant = db.query(Tenant).filter(
            Tenant.slug == tenant_update.slug,
            Tenant.id != current_tenant.id
        ).first()
        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant slug already exists"
            )
        current_tenant.slug = tenant_update.slug

    if tenant_update.name:
        current_tenant.name = tenant_update.name
    if tenant_update.plan:
        current_tenant.plan = tenant_update.plan
    if tenant_update.theme:
        current_tenant.theme = tenant_update.theme

    db.commit()
    db.refresh(current_tenant)

    return current_tenant

@router.put("/settings", response_model=TenantResponse)
async def update_tenant_settings(
    settings_update: TenantSettingsUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update tenant settings
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can update tenant settings"
        )

    current_tenant.settings = settings_update.settings
    db.commit()
    db.refresh(current_tenant)

    return current_tenant

@router.get("/members", response_model=List[TenantMember])
async def list_tenant_members(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List tenant members
    """
    # Check if user has admin or member role (viewers might not see all members)
    if current_user.role not in ["admin", "member"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view tenant members"
        )

    members = db.query(User).filter(User.tenant_id == current_tenant.id).all()

    return [
        TenantMember(
            id=member.id,
            email=member.email,
            full_name=member.full_name,
            role=member.role,
            is_active=member.is_active,
            created_at=member.created_at,
            last_login=member.last_login
        )
        for member in members
    ]

@router.post("/members/invite", response_model=MessageResponse)
async def invite_tenant_member(
    invite_data: TenantMemberInvite,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Invite new member to tenant
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can invite new members"
        )

    # Check if user already exists in this tenant
    existing_user = db.query(User).filter(
        User.email == invite_data.email,
        User.tenant_id == current_tenant.id
    ).first()

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already a member of this tenant"
        )

    # TODO: Implement invitation logic
    # - Generate invitation token
    # - Send invitation email
    # - Store pending invitation in database
    # - Handle invitation acceptance

    return {"message": f"Invitation sent to {invite_data.email}"}

@router.delete("/members/{user_id}", response_model=MessageResponse)
async def remove_tenant_member(
    user_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Remove member from tenant
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can remove members"
        )

    # Find the user to remove
    user_to_remove = db.query(User).filter(
        User.id == user_id,
        User.tenant_id == current_tenant.id
    ).first()

    if not user_to_remove:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in this tenant"
        )

    # Prevent removing yourself
    if user_to_remove.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot remove yourself from the tenant"
        )

    # Check if this is the last admin
    if user_to_remove.role == "admin":
        admin_count = db.query(User).filter(
            User.tenant_id == current_tenant.id,
            User.role == "admin",
            User.is_active == True
        ).count()

        if admin_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove the last admin from the tenant"
            )

    # Soft delete - deactivate user
    user_to_remove.is_active = False
    db.commit()

    return {"message": f"User {user_to_remove.email} has been removed from the tenant"}

@router.put("/members/{user_id}/role", response_model=MessageResponse)
async def update_member_role(
    user_id: uuid.UUID,
    role_update: TenantMemberRoleUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update member role
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can update member roles"
        )

    # Validate role
    valid_roles = ["admin", "member", "viewer"]
    if role_update.role not in valid_roles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid role. Must be one of: {', '.join(valid_roles)}"
        )

    # Find the user to update
    user_to_update = db.query(User).filter(
        User.id == user_id,
        User.tenant_id == current_tenant.id
    ).first()

    if not user_to_update:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in this tenant"
        )

    # Prevent changing your own role
    if user_to_update.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own role"
        )

    # Check if demoting the last admin
    if user_to_update.role == "admin" and role_update.role != "admin":
        admin_count = db.query(User).filter(
            User.tenant_id == current_tenant.id,
            User.role == "admin",
            User.is_active == True
        ).count()

        if admin_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot demote the last admin"
            )

    user_to_update.role = role_update.role
    db.commit()

    return {"message": f"User role updated to {role_update.role}"}

@router.get("/usage", response_model=TenantUsageStats)
async def get_tenant_usage(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get usage statistics for the tenant
    """
    # Check if user has admin role
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only tenant admins can view usage statistics"
        )

    # Get user statistics
    total_users = db.query(User).filter(User.tenant_id == current_tenant.id).count()
    active_users = db.query(User).filter(
        User.tenant_id == current_tenant.id,
        User.is_active == True
    ).count()

    # Get PRD statistics
    total_prds = db.query(PRD).filter(PRD.tenant_id == current_tenant.id).count()

    # PRDs created this month
    from datetime import datetime, timedelta
    current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    prds_this_month = db.query(PRD).filter(
        PRD.tenant_id == current_tenant.id,
        PRD.created_at >= current_month_start
    ).count()

    # Calculate storage used (rough estimate based on PRD content)
    prd_content_size = db.query(func.sum(func.length(PRD.content))).filter(
        PRD.tenant_id == current_tenant.id
    ).scalar() or 0
    storage_used_mb = prd_content_size / (1024 * 1024)  # Convert to MB

    # Define plan limits
    plan_limits = {
        "free": {
            "max_users": 5,
            "max_prds": 10,
            "max_storage_mb": 100,
            "max_api_calls_per_month": 1000
        },
        "pro": {
            "max_users": 50,
            "max_prds": 100,
            "max_storage_mb": 1000,
            "max_api_calls_per_month": 10000
        },
        "enterprise": {
            "max_users": -1,  # unlimited
            "max_prds": -1,   # unlimited
            "max_storage_mb": -1,  # unlimited
            "max_api_calls_per_month": -1  # unlimited
        }
    }

    current_plan_limits = plan_limits.get(current_tenant.plan, plan_limits["free"])

    return TenantUsageStats(
        total_users=total_users,
        active_users=active_users,
        total_prds=total_prds,
        prds_this_month=prds_this_month,
        storage_used_mb=round(storage_used_mb, 2),
        api_calls_this_month=0,  # TODO: Implement API call tracking
        plan_limits=current_plan_limits
    )