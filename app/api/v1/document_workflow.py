"""
Document workflow API endpoints.
Handles document generation workflows: form submission, validation, follow-up questions, regeneration.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import Optional, List
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import (
    Document, DocumentVersion, DocumentGenerationSession,
    DocumentWorkflowState, WorkflowTemplate, DocumentWorkflowAssignment, DocumentWorkflowComment
)
from app.models.document_type import DocumentType
from app.schemas.document import (
    DocumentResponse,
    FormSubmitRequest,
    FormValidationRequest,
    FormValidationResponse,
    FormSchemaResponse,
    FollowUpQuestionsRequest,
    FollowUpQuestionsResponse,
    FollowUpAnswersRequest,
    GenerationStatus,
    RegenerateRequest,
    WorkflowStateResponse,
    WorkflowTemplateResponse,
    WorkflowAssignmentResponse,
    WorkflowCommentResponse,
    WorkflowStateUpdateRequest,
    WorkflowTemplateCreateRequest,
    WorkflowAssignmentCreateRequest,
    WorkflowCommentCreateRequest,
    WorkflowAssignmentUpdateRequest,
    WorkflowCommentUpdateRequest,
    MessageResponse
)
from app.services.anthropic_service import anthropic_service
from app.api.v1.document_utils import validate_form_data

router = APIRouter()


@router.post("/form/submit", response_model=DocumentResponse)
async def submit_form(
    form_request: FormSubmitRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Submit form for any document type
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == form_request.document_type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Validate form data
    validation_result = validate_form_data(form_request.form_data, document_type)
    if not validation_result["is_valid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Form validation failed: {validation_result['errors']}"
        )

    if form_request.save_as_draft:
        # Create draft document without generating content
        title = form_request.title or f"Draft {document_type.name}"
        document = Document(
            title=title,
            content="",  # Empty content for draft
            document_type_id=form_request.document_type_id,
            form_data=form_request.form_data,
            status="draft",
            user_id=current_user.id,
            tenant_id=current_tenant.id
        )

        db.add(document)
        db.commit()
        db.refresh(document)

        return document
    else:
        # Generate document content
        try:
            user_context = {
                "tenant_name": current_tenant.name,
                "user_name": current_user.full_name,
                "document_type": document_type.name
            }

            content = await anthropic_service.generate_document(
                form_request.form_data,
                document_type,
                user_context
            )

            title = form_request.title or f"New {document_type.name}"

            document = Document(
                title=title,
                content=content,
                document_type_id=form_request.document_type_id,
                form_data=form_request.form_data,
                user_id=current_user.id,
                tenant_id=current_tenant.id
            )

            db.add(document)
            db.commit()
            db.refresh(document)

            # Create initial version
            version = DocumentVersion(
                document_id=document.id,
                version="1.0",
                version_number=1,
                title=document.title,
                content=document.content,
                form_data=document.form_data,
                status=document.status,
                change_summary="Initial version",
                created_by=current_user.id
            )

            db.add(version)

            # Update document type usage count
            document_type.usage_count += 1

            db.commit()

            return document

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate document: {str(e)}"
            )


@router.post("/form/validate", response_model=FormValidationResponse)
async def validate_form(
    validation_request: FormValidationRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Validate form data against document type schema
    """
    # Get document type
    document_type = db.query(DocumentType).filter(
        DocumentType.id == validation_request.document_type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    validation_result = validate_form_data(validation_request.form_data, document_type)

    return FormValidationResponse(
        is_valid=validation_result["is_valid"],
        errors=validation_result["errors"],
        warnings=validation_result.get("warnings", [])
    )


@router.get("/form/schema/{type_id}", response_model=FormSchemaResponse)
async def get_form_schema(
    type_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get form schema for document type
    """
    document_type = db.query(DocumentType).filter(
        DocumentType.id == type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    schema = document_type.form_schema
    required_fields = []
    optional_fields = []

    # Extract required and optional fields from schema
    if "fields" in schema:
        for field in schema["fields"]:
            if field.get("required", False):
                required_fields.append(field["name"])
            else:
                optional_fields.append(field["name"])

    return FormSchemaResponse(
        document_type_id=type_id,
        form_schema=schema,
        required_fields=required_fields,
        optional_fields=optional_fields
    )


@router.post("/{doc_id}/followup-questions", response_model=FollowUpQuestionsResponse)
async def get_followup_questions(
    doc_id: uuid.UUID,
    request: FollowUpQuestionsRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get AI follow-up questions
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to get follow-up questions"
        )

    try:
        # Generate follow-up questions using AI
        questions = await anthropic_service.generate_followup_questions(
            document.form_data,
            document.content,
            request.context
        )

        # Create generation session
        session = DocumentGenerationSession(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            session_type="followup",
            original_form_data=document.form_data,
            followup_questions=questions
        )

        db.add(session)
        db.commit()
        db.refresh(session)

        return FollowUpQuestionsResponse(
            questions=questions,
            session_id=str(session.id)
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate follow-up questions: {str(e)}"
        )


@router.post("/{doc_id}/followup-answers", response_model=DocumentResponse)
async def submit_followup_answers(
    doc_id: uuid.UUID,
    answers_request: FollowUpAnswersRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Submit follow-up answers
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Find the generation session
    session = db.query(DocumentGenerationSession).filter(
        DocumentGenerationSession.id == answers_request.session_id,
        DocumentGenerationSession.document_id == doc_id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generation session not found"
        )

    try:
        # Update session with answers
        session.followup_answers = answers_request.answers
        session.status = "processing"

        # Regenerate document with follow-up answers
        document_type = db.query(DocumentType).filter(
            DocumentType.id == document.document_type_id
        ).first()

        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name,
            "document_type": document_type.name
        }

        enhanced_content = await anthropic_service.enhance_document_with_followup(
            document.content,
            document.form_data,
            answers_request.answers,
            user_context
        )

        # Update document
        document.content = enhanced_content

        # Create new version
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Enhanced with follow-up answers",
            created_by=current_user.id
        )

        db.add(version)

        # Update session
        session.status = "completed"
        session.completed_at = datetime.now(timezone.utc)
        session.generated_content = enhanced_content

        db.commit()
        db.refresh(document)

        return document

    except Exception as e:
        session.status = "failed"
        session.error_message = str(e)
        db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process follow-up answers: {str(e)}"
        )


@router.get("/{doc_id}/generation-status", response_model=GenerationStatus)
async def get_generation_status(
    doc_id: uuid.UUID,
    session_id: Optional[str] = Query(None, description="Specific session ID to check"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Check generation progress
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Get the latest or specific generation session
    query = db.query(DocumentGenerationSession).filter(
        DocumentGenerationSession.document_id == doc_id
    )

    if session_id:
        session = query.filter(DocumentGenerationSession.id == session_id).first()
    else:
        session = query.order_by(desc(DocumentGenerationSession.started_at)).first()

    if not session:
        # No generation session found, assume document is completed
        return GenerationStatus(
            status="completed",
            progress=100,
            message="Document generation completed"
        )

    return GenerationStatus(
        status=session.status,
        progress=session.progress,
        message=f"Generation {session.status}",
        estimated_completion=session.completed_at,
        error_details=session.error_message
    )


@router.post("/{doc_id}/regenerate", response_model=DocumentResponse)
async def regenerate_document(
    doc_id: uuid.UUID,
    regenerate_request: RegenerateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Regenerate document with new inputs
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to regenerate document"
        )

    try:
        # Create regeneration session
        session = DocumentGenerationSession(
            document_id=doc_id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            session_type="regeneration",
            original_form_data=document.form_data,
            followup_answers=regenerate_request.followup_answers or {},
            generation_instructions=regenerate_request.regeneration_instructions,
            status="processing"
        )

        db.add(session)
        db.commit()

        # Use updated form data if provided
        form_data = regenerate_request.form_data or document.form_data

        # Get document type
        document_type = db.query(DocumentType).filter(
            DocumentType.id == document.document_type_id
        ).first()

        user_context = {
            "tenant_name": current_tenant.name,
            "user_name": current_user.full_name,
            "document_type": document_type.name,
            "regeneration_instructions": regenerate_request.regeneration_instructions,
            "preserve_structure": regenerate_request.preserve_structure
        }

        # Regenerate content
        if regenerate_request.preserve_structure:
            new_content = await anthropic_service.regenerate_document_content(
                document.content,
                form_data,
                regenerate_request.followup_answers or {},
                user_context
            )
        else:
            new_content = await anthropic_service.generate_document(
                form_data,
                document_type,
                user_context
            )

        # Update document
        document.content = new_content
        if regenerate_request.form_data:
            document.form_data = form_data

        # Create new version
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary="Document regenerated",
            created_by=current_user.id
        )

        db.add(version)

        # Update session
        session.status = "completed"
        session.progress = 100
        session.completed_at = datetime.now(timezone.utc)
        session.generated_content = new_content

        db.commit()
        db.refresh(document)

        return document

    except Exception as e:
        if 'session' in locals():
            session.status = "failed"
            session.error_message = str(e)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate document: {str(e)}"
        )


# Workflow State Management Endpoints

@router.get("/{doc_id}/workflow-state", response_model=WorkflowStateResponse)
async def get_workflow_state(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get current workflow state for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Get current workflow state
    workflow_state = db.query(DocumentWorkflowState).filter(
        DocumentWorkflowState.document_id == doc_id
    ).order_by(desc(DocumentWorkflowState.created_at)).first()

    if not workflow_state:
        # Create initial workflow state if none exists
        workflow_state = DocumentWorkflowState(
            document_id=doc_id,
            current_state=document.status,
            user_id=current_user.id,
            tenant_id=current_tenant.id
        )
        db.add(workflow_state)
        db.commit()
        db.refresh(workflow_state)

    return workflow_state


@router.post("/{doc_id}/workflow-state", response_model=WorkflowStateResponse)
async def update_workflow_state(
    doc_id: uuid.UUID,
    state_update: WorkflowStateUpdateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update workflow state for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update workflow state"
        )

    # Get current workflow state
    current_workflow_state = db.query(DocumentWorkflowState).filter(
        DocumentWorkflowState.document_id == doc_id
    ).order_by(desc(DocumentWorkflowState.created_at)).first()

    # Create new workflow state
    new_workflow_state = DocumentWorkflowState(
        document_id=doc_id,
        workflow_template_id=current_workflow_state.workflow_template_id if current_workflow_state else None,
        current_state=state_update.new_state,
        previous_state=current_workflow_state.current_state if current_workflow_state else document.status,
        assigned_to=state_update.assigned_to,
        assigned_role=state_update.assigned_role,
        due_date=state_update.due_date,
        workflow_data=state_update.workflow_data or {},
        priority=state_update.priority or "normal",
        user_id=current_user.id,
        tenant_id=current_tenant.id
    )

    db.add(new_workflow_state)

    # Update document status
    document.status = state_update.new_state

    # Create version if significant change
    if state_update.transition_notes:
        latest_version = db.query(DocumentVersion).filter(
            DocumentVersion.document_id == doc_id
        ).order_by(desc(DocumentVersion.version_number)).first()

        new_version_number = (latest_version.version_number + 1) if latest_version else 1
        new_version = f"{new_version_number}.0"

        document.version = new_version
        document.version_number = new_version_number

        version = DocumentVersion(
            document_id=document.id,
            version=new_version,
            version_number=new_version_number,
            title=document.title,
            content=document.content,
            form_data=document.form_data,
            status=document.status,
            change_summary=state_update.transition_notes,
            created_by=current_user.id
        )
        db.add(version)

    db.commit()
    db.refresh(new_workflow_state)

    return new_workflow_state


# Workflow Assignment Endpoints

@router.get("/{doc_id}/workflow-assignments", response_model=List[WorkflowAssignmentResponse])
async def get_workflow_assignments(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get workflow assignments for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    assignments = db.query(DocumentWorkflowAssignment).filter(
        DocumentWorkflowAssignment.document_id == doc_id,
        DocumentWorkflowAssignment.tenant_id == current_tenant.id
    ).order_by(desc(DocumentWorkflowAssignment.created_at)).all()

    return assignments


@router.post("/{doc_id}/workflow-assignments", response_model=WorkflowAssignmentResponse)
async def create_workflow_assignment(
    doc_id: uuid.UUID,
    assignment_request: WorkflowAssignmentCreateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create workflow assignment for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create workflow assignment"
        )

    # Get current workflow state
    workflow_state = db.query(DocumentWorkflowState).filter(
        DocumentWorkflowState.document_id == doc_id
    ).order_by(desc(DocumentWorkflowState.created_at)).first()

    if not workflow_state:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No workflow state found for document"
        )

    assignment = DocumentWorkflowAssignment(
        document_id=doc_id,
        workflow_state_id=workflow_state.id,
        assigned_to=assignment_request.assigned_to,
        assigned_by=current_user.id,
        assignment_type=assignment_request.assignment_type,
        task_description=assignment_request.task_description,
        instructions=assignment_request.instructions,
        due_date=assignment_request.due_date,
        priority=assignment_request.priority,
        tenant_id=current_tenant.id
    )

    db.add(assignment)
    db.commit()
    db.refresh(assignment)

    return assignment


@router.put("/{doc_id}/workflow-assignments/{assignment_id}", response_model=WorkflowAssignmentResponse)
async def update_workflow_assignment(
    doc_id: uuid.UUID,
    assignment_id: uuid.UUID,
    assignment_update: WorkflowAssignmentUpdateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update workflow assignment
    """
    assignment = db.query(DocumentWorkflowAssignment).filter(
        DocumentWorkflowAssignment.id == assignment_id,
        DocumentWorkflowAssignment.document_id == doc_id,
        DocumentWorkflowAssignment.tenant_id == current_tenant.id
    ).first()

    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow assignment not found"
        )

    # Check permissions - only assigned user or document owner can update
    if (assignment.assigned_to != current_user.id and
        assignment.assigned_by != current_user.id):
        document = db.query(Document).filter(Document.id == doc_id).first()
        if document and document.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to update assignment"
            )

    # Update assignment
    if assignment_update.status is not None:
        assignment.status = assignment_update.status
        if assignment_update.status == "completed":
            assignment.completed_at = datetime.now(timezone.utc)

    if assignment_update.completion_notes is not None:
        assignment.completion_notes = assignment_update.completion_notes

    db.commit()
    db.refresh(assignment)

    return assignment


# Workflow Comment Endpoints

@router.get("/{doc_id}/workflow-comments", response_model=List[WorkflowCommentResponse])
async def get_workflow_comments(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get workflow comments for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    comments = db.query(DocumentWorkflowComment).filter(
        DocumentWorkflowComment.document_id == doc_id,
        DocumentWorkflowComment.tenant_id == current_tenant.id
    ).order_by(desc(DocumentWorkflowComment.created_at)).all()

    return comments


@router.post("/{doc_id}/workflow-comments", response_model=WorkflowCommentResponse)
async def create_workflow_comment(
    doc_id: uuid.UUID,
    comment_request: WorkflowCommentCreateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create workflow comment for document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Check permissions - user must have view access
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_view", []) and
        current_user.id not in document.permissions.get("can_edit", []) and
        current_user.id not in document.permissions.get("can_comment", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to comment on document"
        )

    # Get current workflow state
    workflow_state = db.query(DocumentWorkflowState).filter(
        DocumentWorkflowState.document_id == doc_id
    ).order_by(desc(DocumentWorkflowState.created_at)).first()

    comment = DocumentWorkflowComment(
        document_id=doc_id,
        workflow_state_id=workflow_state.id if workflow_state else None,
        comment_text=comment_request.comment_text,
        comment_type=comment_request.comment_type,
        section_reference=comment_request.section_reference,
        line_reference=comment_request.line_reference,
        context_data=comment_request.context_data or {},
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )

    db.add(comment)
    db.commit()
    db.refresh(comment)

    return comment


@router.put("/{doc_id}/workflow-comments/{comment_id}", response_model=WorkflowCommentResponse)
async def update_workflow_comment(
    doc_id: uuid.UUID,
    comment_id: uuid.UUID,
    comment_update: WorkflowCommentUpdateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update workflow comment (resolve/unresolve)
    """
    comment = db.query(DocumentWorkflowComment).filter(
        DocumentWorkflowComment.id == comment_id,
        DocumentWorkflowComment.document_id == doc_id,
        DocumentWorkflowComment.tenant_id == current_tenant.id
    ).first()

    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow comment not found"
        )

    # Check permissions - only comment creator or document owner can update
    if comment.created_by != current_user.id:
        document = db.query(Document).filter(Document.id == doc_id).first()
        if document and document.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to update comment"
            )

    # Update comment
    if comment_update.is_resolved is not None:
        comment.is_resolved = comment_update.is_resolved
        if comment_update.is_resolved:
            comment.resolved_by = current_user.id
            comment.resolved_at = datetime.now(timezone.utc)
        else:
            comment.resolved_by = None
            comment.resolved_at = None

    if comment_update.resolution_notes is not None:
        comment.resolution_notes = comment_update.resolution_notes

    db.commit()
    db.refresh(comment)

    return comment


# Workflow Template Endpoints

@router.get("/templates", response_model=List[WorkflowTemplateResponse])
async def get_workflow_templates(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get workflow templates for tenant
    """
    templates = db.query(WorkflowTemplate).filter(
        WorkflowTemplate.tenant_id == current_tenant.id,
        WorkflowTemplate.is_active == True
    ).order_by(WorkflowTemplate.name).all()

    return templates


@router.post("/templates", response_model=WorkflowTemplateResponse)
async def create_workflow_template(
    template_request: WorkflowTemplateCreateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create workflow template
    """
    template = WorkflowTemplate(
        name=template_request.name,
        description=template_request.description,
        workflow_steps=template_request.workflow_steps,
        default_assignments=template_request.default_assignments or {},
        automation_rules=template_request.automation_rules or {},
        document_types=template_request.document_types or [],
        conditions=template_request.conditions or {},
        is_default=template_request.is_default,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )

    db.add(template)
    db.commit()
    db.refresh(template)

    return template


@router.get("/templates/{template_id}", response_model=WorkflowTemplateResponse)
async def get_workflow_template(
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific workflow template
    """
    template = db.query(WorkflowTemplate).filter(
        WorkflowTemplate.id == template_id,
        WorkflowTemplate.tenant_id == current_tenant.id
    ).first()

    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow template not found"
        )

    return template


@router.post("/{doc_id}/apply-template/{template_id}", response_model=WorkflowStateResponse)
async def apply_workflow_template(
    doc_id: uuid.UUID,
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Apply workflow template to document
    """
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    template = db.query(WorkflowTemplate).filter(
        WorkflowTemplate.id == template_id,
        WorkflowTemplate.tenant_id == current_tenant.id,
        WorkflowTemplate.is_active == True
    ).first()

    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow template not found"
        )

    # Check permissions
    if (document.user_id != current_user.id and
        current_user.id not in document.permissions.get("can_edit", [])):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to apply workflow template"
        )

    # Create new workflow state with template
    workflow_state = DocumentWorkflowState(
        document_id=doc_id,
        workflow_template_id=template_id,
        current_state=template.workflow_steps[0].get("state", "draft") if template.workflow_steps else "draft",
        workflow_data={"template_applied": True, "step_index": 0},
        user_id=current_user.id,
        tenant_id=current_tenant.id
    )

    db.add(workflow_state)
    db.commit()
    db.refresh(workflow_state)

    return workflow_state
