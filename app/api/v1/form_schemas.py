"""
Form schema management API endpoints.
Handles CRUD operations for form schemas and validation.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, ConditionalRule, FormValidationLog
from app.schemas.form_schema import (
    FormSchemaCreate,
    FormSchemaUpdate,
    FormSchemaResponse,
    FormSchemaWithQuestions,
    FormValidationRequest,
    FormValidationResponse,
    FormPreviewResponse,
    FormPreviewSection,
    FormPreviewQuestion,
    ConditionalLogicResponse,
    ConditionalRuleResponse,
    ValidationError,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for form schema management"""
    # In a real implementation, check user roles/permissions
    # For now, we'll assume all authenticated users can manage schemas
    return True


def validate_form_data(form_data: dict, questions: List[FormQuestion]) -> FormValidationResponse:
    """Validate form data against schema questions"""
    errors = []
    warnings = []
    conditional_questions = []
    hidden_questions = []
    
    # Create question lookup
    questions_by_id = {q.question_id: q for q in questions}
    
    # Validate each question
    for question in questions:
        question_id = question.question_id
        value = form_data.get(question_id)
        
        # Check required fields
        if question.is_required and (value is None or value == ""):
            errors.append(ValidationError(
                question_id=question_id,
                error_type="required",
                message=f"Question '{question.question_text}' is required",
                value=value
            ))
            continue
        
        # Skip validation if no value provided for optional field
        if value is None or value == "":
            continue
        
        # Validate based on question type
        if question.question_type == "email":
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, str(value)):
                errors.append(ValidationError(
                    question_id=question_id,
                    error_type="format",
                    message="Invalid email format",
                    value=value
                ))
        
        elif question.question_type == "number":
            try:
                float(value)
            except (ValueError, TypeError):
                errors.append(ValidationError(
                    question_id=question_id,
                    error_type="type",
                    message="Value must be a number",
                    value=value
                ))
        
        # Validate against custom validation rules
        for rule in question.validation_rules:
            rule_type = rule.get("rule_type")
            rule_value = rule.get("value")
            
            if rule_type == "min_length" and len(str(value)) < rule_value:
                errors.append(ValidationError(
                    question_id=question_id,
                    error_type="min_length",
                    message=f"Minimum length is {rule_value} characters",
                    value=value
                ))
            elif rule_type == "max_length" and len(str(value)) > rule_value:
                errors.append(ValidationError(
                    question_id=question_id,
                    error_type="max_length",
                    message=f"Maximum length is {rule_value} characters",
                    value=value
                ))
    
    # TODO: Implement conditional logic validation
    # For now, return all questions as visible
    conditional_questions = [q.question_id for q in questions]
    
    return FormValidationResponse(
        is_valid=len(errors) == 0,
        errors=errors,
        warnings=warnings,
        conditional_questions=conditional_questions,
        hidden_questions=hidden_questions
    )


@router.get("/{doc_type}", response_model=FormSchemaResponse)
async def get_form_schema(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get form schema for document type
    """
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == current_tenant.id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    # Add question count
    question_count = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).count()
    
    schema.question_count = question_count
    return schema


@router.post("/{doc_type}", response_model=FormSchemaResponse)
async def create_form_schema(
    doc_type: str,
    schema_data: FormSchemaCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create new form schema (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Validate document type matches
    if schema_data.document_type != doc_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Document type in URL must match schema document type"
        )
    
    # Check if schema already exists
    existing_schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == current_tenant.id
    ).first()
    
    if existing_schema:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Form schema already exists for document type: {doc_type}"
        )
    
    # Create schema
    schema = FormSchema(
        document_type=schema_data.document_type,
        schema_version=schema_data.schema_version,
        title=schema_data.title,
        description=schema_data.description,
        sections=schema_data.sections,
        global_settings=schema_data.global_settings,
        styling=schema_data.styling,
        schema_metadata=schema_data.metadata,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(schema)
    db.commit()
    db.refresh(schema)
    
    schema.question_count = 0
    return schema


@router.put("/{doc_type}", response_model=FormSchemaResponse)
async def update_form_schema(
    doc_type: str,
    schema_data: FormSchemaUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update form schema (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == current_tenant.id
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    # Update fields
    update_data = schema_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(schema, field, value)
    
    schema.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(schema)
    
    # Add question count
    question_count = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).count()
    
    schema.question_count = question_count
    return schema


@router.post("/{doc_type}/validate", response_model=FormValidationResponse)
async def validate_form_data_endpoint(
    doc_type: str,
    validation_request: FormValidationRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Validate form data against schema
    """
    # Get schema
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == current_tenant.id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    # Get questions
    questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).all()
    
    # Validate form data
    validation_result = validate_form_data(validation_request.form_data, questions)
    
    # Log validation
    validation_log = FormValidationLog(
        form_schema_id=schema.id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        form_data=validation_request.form_data,
        validation_result=validation_result.dict(),
        is_valid=validation_result.is_valid,
        error_count=len(validation_result.errors),
        warning_count=len(validation_result.warnings)
    )
    
    db.add(validation_log)
    db.commit()
    
    return validation_result


@router.get("/{doc_type}/preview", response_model=FormPreviewResponse)
async def preview_form(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Preview form for document type
    """
    # Get schema with questions
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == current_tenant.id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    # Get questions ordered by section and order_index
    questions = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    ).order_by(FormQuestion.section, FormQuestion.order_index).all()
    
    # Group questions by section
    sections_dict = {}
    for question in questions:
        section_name = question.section or "General"
        if section_name not in sections_dict:
            sections_dict[section_name] = []
        
        preview_question = FormPreviewQuestion(
            question_id=question.question_id,
            question_text=question.question_text,
            question_type=question.question_type,
            description=question.description,
            placeholder=question.placeholder,
            options=question.options,
            is_required=question.is_required,
            section=question.section,
            order_index=question.order_index
        )
        sections_dict[section_name].append(preview_question)
    
    # Create sections
    sections = []
    for i, (section_name, section_questions) in enumerate(sections_dict.items()):
        sections.append(FormPreviewSection(
            section_name=section_name,
            questions=section_questions,
            order_index=i
        ))
    
    # Calculate metrics
    total_questions = len(questions)
    required_questions = sum(1 for q in questions if q.is_required)
    estimated_time = max(2, total_questions * 0.5)  # Rough estimate: 30 seconds per question
    
    return FormPreviewResponse(
        schema_id=schema.id,
        document_type=schema.document_type,
        title=schema.title,
        description=schema.description,
        sections=sections,
        total_questions=total_questions,
        required_questions=required_questions,
        estimated_time_minutes=int(estimated_time)
    )
