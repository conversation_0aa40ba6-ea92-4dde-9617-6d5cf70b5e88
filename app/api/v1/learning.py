"""
Learning & AI Enhancement API endpoints.
Handles learning profiles, feedback, patterns, and AI enhancement features.
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from typing import List, Dict, Any
import uuid
from datetime import datetime, timezone, timedelta

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.document_type import DocumentType
from app.models.learning import LearningProfile, AIFeedback, LearnedPattern, LearningSession
from app.schemas.learning import (
    LearningProfileResponse,
    LearningProfileUpdate,
    AIFeedbackCreate,
    AIFeedbackResponse,
    LearnedPatternCreate,
    LearnedPatternUpdate,
    LearnedPatternResponse,
    LearningSuggestionsResponse,
    LearningSettingsUpdate,
    LearningSettingsResponse,
    ResetLearningRequest,
    ResetLearningResponse,
    LearningSessionCreate,
    LearningSessionUpdate,
    LearningSessionResponse,
    LearningAnalyticsResponse,
    PatternEffectivenessMetrics,
    FeedbackTrendData,
    LearningProgressMetrics,
    LearningDataExport,
    LearningDataImportRequest,
    LearningDataImportResponse,
    MessageResponse
)
from app.services.anthropic_service import anthropic_service
from app.services.learning_service import learning_service

router = APIRouter()


@router.get("/profile/{doc_type}", response_model=LearningProfileResponse)
async def get_learning_profile(
    doc_type: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning profile for document type
    """
    # Verify document type exists and user has access
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    # Get or create learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if not profile:
        # Create default profile
        profile = LearningProfile(
            document_type_id=doc_type,
            tenant_id=current_tenant.id
        )
        db.add(profile)
        db.commit()
        db.refresh(profile)
    
    return profile


@router.put("/profile/{doc_type}", response_model=LearningProfileResponse)
async def update_learning_profile(
    doc_type: uuid.UUID,
    profile_update: LearningProfileUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update learning profile for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    # Get or create learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if not profile:
        profile = LearningProfile(
            document_type_id=doc_type,
            tenant_id=current_tenant.id
        )
        db.add(profile)
    
    # Update profile
    update_data = profile_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "settings" and value:
            # Merge settings
            current_settings = profile.settings or {}
            current_settings.update(value)
            profile.settings = current_settings
        else:
            setattr(profile, field, value)
    
    profile.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(profile)
    
    return profile


@router.post("/feedback", response_model=AIFeedbackResponse)
async def submit_feedback(
    feedback_data: AIFeedbackCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Submit feedback on AI output
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == feedback_data.document_type_id,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    # If document_id provided, verify it exists and user has access
    if feedback_data.document_id:
        document = db.query(Document).filter(
            Document.id == feedback_data.document_id,
            Document.tenant_id == current_tenant.id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
    
    # Create feedback record
    feedback = AIFeedback(
        document_id=feedback_data.document_id,
        document_type_id=feedback_data.document_type_id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        feedback_type=feedback_data.feedback_type,
        rating=feedback_data.rating,
        sentiment=feedback_data.sentiment,
        ai_output_section=feedback_data.ai_output_section,
        original_prompt=feedback_data.original_prompt,
        ai_generated_content=feedback_data.ai_generated_content,
        feedback_text=feedback_data.feedback_text,
        suggested_improvement=feedback_data.suggested_improvement,
        tags=feedback_data.tags
    )
    
    db.add(feedback)
    db.commit()
    db.refresh(feedback)
    
    # Update learning profile statistics
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == feedback_data.document_type_id,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if profile:
        profile.total_feedback_received += 1
        if feedback_data.sentiment == "positive":
            profile.positive_feedback_count += 1
        elif feedback_data.sentiment == "negative":
            profile.negative_feedback_count += 1
        
        profile.updated_at = datetime.now(timezone.utc)
        db.commit()

    # Trigger async learning process to analyze feedback
    background_tasks.add_task(
        learning_service.process_feedback_for_learning,
        feedback.id,
        current_tenant.id,
        db
    )

    return feedback


@router.get("/suggestions/{doc_id}", response_model=LearningSuggestionsResponse)
async def get_learning_suggestions(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning-based suggestions for document
    """
    # Verify document exists and user has access
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Get learning profile for document type
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == document.document_type_id,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if not profile or not profile.learning_enabled:
        return LearningSuggestionsResponse(
            suggestions=[],
            total_suggestions=0,
            document_id=doc_id,
            document_type_id=document.document_type_id,
            generated_at=datetime.now(timezone.utc)
        )
    
    # Get applicable patterns
    patterns = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == document.document_type_id,
        LearnedPattern.tenant_id == current_tenant.id,
        LearnedPattern.is_active == True,
        LearnedPattern.confidence_score >= profile.confidence_threshold
    ).all()
    
    try:
        # Generate AI-powered suggestions based on learned patterns
        suggestions = await anthropic_service.generate_learning_suggestions(
            document.content,
            document.form_data,
            patterns,
            profile.user_preferences
        )
        
        return LearningSuggestionsResponse(
            suggestions=suggestions,
            total_suggestions=len(suggestions),
            document_id=doc_id,
            document_type_id=document.document_type_id,
            generated_at=datetime.now(timezone.utc)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate learning suggestions: {str(e)}"
        )


@router.post("/pattern/{doc_type}", response_model=LearnedPatternResponse)
async def record_pattern(
    doc_type: uuid.UUID,
    pattern_data: LearnedPatternCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Record new pattern for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Check if pattern with same name already exists
    existing_pattern = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == doc_type,
        LearnedPattern.tenant_id == current_tenant.id,
        LearnedPattern.pattern_name == pattern_data.pattern_name
    ).first()

    if existing_pattern:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Pattern with this name already exists"
        )

    # Create new pattern
    pattern = LearnedPattern(
        document_type_id=doc_type,
        tenant_id=current_tenant.id,
        pattern_type=pattern_data.pattern_type,
        pattern_name=pattern_data.pattern_name,
        pattern_description=pattern_data.pattern_description,
        pattern_data=pattern_data.pattern_data,
        trigger_conditions=pattern_data.trigger_conditions,
        auto_apply=pattern_data.auto_apply,
        confidence_score=0.5  # Initial confidence
    )

    db.add(pattern)
    db.commit()
    db.refresh(pattern)

    # Update learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    if profile:
        profile.last_learning_update = datetime.now(timezone.utc)
        db.commit()

    return pattern


@router.get("/patterns/{doc_type}", response_model=List[LearnedPatternResponse])
async def get_patterns(
    doc_type: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learned patterns for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get patterns
    patterns = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == doc_type,
        LearnedPattern.tenant_id == current_tenant.id
    ).order_by(desc(LearnedPattern.confidence_score)).all()

    return patterns


@router.put("/pattern/{pattern_id}", response_model=LearnedPatternResponse)
async def update_pattern(
    pattern_id: uuid.UUID,
    pattern_update: LearnedPatternUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update learned pattern
    """
    pattern = db.query(LearnedPattern).filter(
        LearnedPattern.id == pattern_id,
        LearnedPattern.tenant_id == current_tenant.id
    ).first()

    if not pattern:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pattern not found"
        )

    # Update pattern
    update_data = pattern_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(pattern, field, value)

    pattern.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(pattern)

    return pattern


@router.delete("/pattern/{pattern_id}", response_model=MessageResponse)
async def delete_pattern(
    pattern_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete learned pattern
    """
    pattern = db.query(LearnedPattern).filter(
        LearnedPattern.id == pattern_id,
        LearnedPattern.tenant_id == current_tenant.id
    ).first()

    if not pattern:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pattern not found"
        )

    db.delete(pattern)
    db.commit()

    return MessageResponse(message="Pattern deleted successfully")


@router.post("/patterns/bulk-update", response_model=MessageResponse)
async def bulk_update_patterns(
    pattern_updates: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Bulk update patterns (activate/deactivate, update confidence, etc.)
    """
    updated_count = 0

    for update_data in pattern_updates:
        pattern_id = update_data.get("pattern_id")
        if not pattern_id:
            continue

        pattern = db.query(LearnedPattern).filter(
            LearnedPattern.id == pattern_id,
            LearnedPattern.tenant_id == current_tenant.id
        ).first()

        if pattern:
            # Apply updates
            for field, value in update_data.items():
                if field != "pattern_id" and hasattr(pattern, field):
                    setattr(pattern, field, value)

            pattern.updated_at = datetime.now(timezone.utc)
            updated_count += 1

    db.commit()

    return MessageResponse(message=f"Updated {updated_count} patterns successfully")


@router.put("/settings/{doc_type}", response_model=LearningSettingsResponse)
async def update_learning_settings(
    doc_type: uuid.UUID,
    settings_update: LearningSettingsUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update learning settings for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get or create learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    if not profile:
        profile = LearningProfile(
            document_type_id=doc_type,
            tenant_id=current_tenant.id
        )
        db.add(profile)

    # Update settings
    current_settings = profile.settings or {}
    update_data = settings_update.dict(exclude_unset=True)
    current_settings.update(update_data)
    profile.settings = current_settings
    profile.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(profile)

    return LearningSettingsResponse(**profile.settings)


@router.get("/settings/{doc_type}", response_model=LearningSettingsResponse)
async def get_learning_settings(
    doc_type: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning settings for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    if not profile:
        # Return default settings
        return LearningSettingsResponse(
            pattern_detection_sensitivity="medium",
            feedback_weight=1.0,
            pattern_expiry_days=90,
            max_patterns_stored=100
        )

    return LearningSettingsResponse(**profile.settings)


@router.post("/reset/{doc_type}", response_model=ResetLearningResponse)
async def reset_learning_data(
    doc_type: uuid.UUID,
    reset_request: ResetLearningRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Reset learning data for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Verify confirmation
    if reset_request.confirmation != "RESET":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid confirmation. Must be 'RESET'"
        )

    patterns_removed = 0
    feedback_removed = 0
    profile_reset = False

    # Reset patterns
    if reset_request.reset_patterns:
        patterns_query = db.query(LearnedPattern).filter(
            LearnedPattern.document_type_id == doc_type,
            LearnedPattern.tenant_id == current_tenant.id
        )
        patterns_removed = patterns_query.count()
        patterns_query.delete()

    # Reset feedback
    if reset_request.reset_feedback:
        feedback_query = db.query(AIFeedback).filter(
            AIFeedback.document_type_id == doc_type,
            AIFeedback.tenant_id == current_tenant.id
        )
        feedback_removed = feedback_query.count()
        feedback_query.delete()

    # Reset profile
    if reset_request.reset_profile:
        profile = db.query(LearningProfile).filter(
            LearningProfile.document_type_id == doc_type,
            LearningProfile.tenant_id == current_tenant.id
        ).first()

        if profile:
            # Reset statistics and data
            profile.total_documents_processed = 0
            profile.total_feedback_received = 0
            profile.positive_feedback_count = 0
            profile.negative_feedback_count = 0
            profile.learned_patterns = []
            profile.user_preferences = {}
            profile.performance_metrics = {}
            profile.last_learning_update = None
            profile.updated_at = datetime.now(timezone.utc)
            profile_reset = True

    db.commit()

    return ResetLearningResponse(
        message="Learning data reset successfully",
        patterns_removed=patterns_removed,
        feedback_removed=feedback_removed,
        profile_reset=profile_reset,
        reset_at=datetime.now(timezone.utc)
    )


@router.post("/sessions", response_model=LearningSessionResponse)
async def start_learning_session(
    session_data: LearningSessionCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Start a new learning session
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == session_data.document_type_id,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Create learning session
    session = LearningSession(
        document_type_id=session_data.document_type_id,
        tenant_id=current_tenant.id,
        triggered_by_user_id=current_user.id,
        session_type=session_data.session_type,
        input_data=session_data.input_data,
        processing_parameters=session_data.processing_parameters,
        status="processing"
    )

    db.add(session)
    db.commit()
    db.refresh(session)

    # TODO: Trigger background processing based on session type
    # This would be implemented as a background task

    return session


@router.get("/sessions/{session_id}", response_model=LearningSessionResponse)
async def get_learning_session(
    session_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning session details
    """
    session = db.query(LearningSession).filter(
        LearningSession.id == session_id,
        LearningSession.tenant_id == current_tenant.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Learning session not found"
        )

    return session


@router.put("/sessions/{session_id}", response_model=LearningSessionResponse)
async def update_learning_session(
    session_id: uuid.UUID,
    session_update: LearningSessionUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update learning session
    """
    session = db.query(LearningSession).filter(
        LearningSession.id == session_id,
        LearningSession.tenant_id == current_tenant.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Learning session not found"
        )

    # Update session
    update_data = session_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(session, field, value)

    # Set completion time if status is completed or failed
    if session_update.status in ["completed", "failed"]:
        session.completed_at = datetime.now(timezone.utc)
        if session.started_at:
            time_diff = session.completed_at - session.started_at
            session.processing_time_seconds = time_diff.total_seconds()

    db.commit()
    db.refresh(session)

    return session


@router.get("/sessions", response_model=List[LearningSessionResponse])
async def list_learning_sessions(
    doc_type: uuid.UUID = None,
    status: str = None,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List learning sessions with optional filters
    """
    query = db.query(LearningSession).filter(
        LearningSession.tenant_id == current_tenant.id
    )

    if doc_type:
        query = query.filter(LearningSession.document_type_id == doc_type)

    if status:
        query = query.filter(LearningSession.status == status)

    sessions = query.order_by(desc(LearningSession.started_at)).offset(offset).limit(limit).all()

    return sessions


@router.get("/analytics/{doc_type}", response_model=LearningAnalyticsResponse)
async def get_learning_analytics(
    doc_type: uuid.UUID,
    days: int = 30,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive learning analytics for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Calculate date range
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days)

    # Get patterns
    patterns = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == doc_type,
        LearnedPattern.tenant_id == current_tenant.id
    ).all()

    # Get feedback
    feedback = db.query(AIFeedback).filter(
        AIFeedback.document_type_id == doc_type,
        AIFeedback.tenant_id == current_tenant.id,
        AIFeedback.created_at >= start_date
    ).all()

    # Calculate pattern effectiveness
    pattern_effectiveness = []
    for pattern in patterns:
        # Get feedback related to this pattern
        pattern_feedback = [f for f in feedback if str(pattern.id) in (pattern.source_feedback_ids or [])]
        avg_rating = sum(f.rating for f in pattern_feedback) / len(pattern_feedback) if pattern_feedback else 0

        pattern_effectiveness.append(PatternEffectivenessMetrics(
            pattern_id=pattern.id,
            pattern_name=pattern.pattern_name,
            pattern_type=pattern.pattern_type,
            usage_count=pattern.usage_count,
            success_rate=pattern.success_rate,
            confidence_score=pattern.confidence_score,
            avg_feedback_rating=avg_rating,
            last_applied=pattern.last_applied_at
        ))

    # Calculate feedback trends
    feedback_trends = []
    current_date = start_date
    while current_date <= end_date:
        day_feedback = [f for f in feedback if f.created_at.date() == current_date.date()]

        positive_count = len([f for f in day_feedback if f.sentiment == "positive"])
        negative_count = len([f for f in day_feedback if f.sentiment == "negative"])
        neutral_count = len([f for f in day_feedback if f.sentiment == "neutral"])
        avg_rating = sum(f.rating for f in day_feedback) / len(day_feedback) if day_feedback else 0

        feedback_trends.append(FeedbackTrendData(
            date=current_date.strftime("%Y-%m-%d"),
            positive_count=positive_count,
            negative_count=negative_count,
            neutral_count=neutral_count,
            avg_rating=avg_rating
        ))

        current_date += timedelta(days=1)

    # Calculate progress metrics
    active_patterns = len([p for p in patterns if p.is_active])
    patterns_this_month = len([p for p in patterns if p.created_at >= (end_date - timedelta(days=30))])
    feedback_this_month = len([f for f in feedback if f.created_at >= (end_date - timedelta(days=30))])
    avg_confidence = sum(p.confidence_score for p in patterns) / len(patterns) if patterns else 0

    # Top performing patterns (by success rate)
    top_patterns = sorted(pattern_effectiveness, key=lambda x: x.success_rate, reverse=True)[:5]

    progress_metrics = LearningProgressMetrics(
        total_patterns=len(patterns),
        active_patterns=active_patterns,
        patterns_this_month=patterns_this_month,
        total_feedback=len(feedback),
        feedback_this_month=feedback_this_month,
        avg_pattern_confidence=avg_confidence,
        learning_accuracy_trend=[0.7, 0.75, 0.8, 0.82, 0.85],  # Placeholder trend
        top_performing_patterns=top_patterns
    )

    return LearningAnalyticsResponse(
        document_type_id=doc_type,
        tenant_id=current_tenant.id,
        progress_metrics=progress_metrics,
        feedback_trends=feedback_trends,
        pattern_effectiveness=pattern_effectiveness,
        generated_at=datetime.now(timezone.utc)
    )


@router.post("/apply-patterns/{doc_id}", response_model=Dict[str, Any])
async def apply_patterns_to_document(
    doc_id: uuid.UUID,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Apply learned patterns to improve document content
    """
    # Verify document exists and user has access
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )

    # Apply patterns using the learning service
    try:
        result = await learning_service.apply_patterns_to_document(
            document.content,
            document.form_data,
            document.document_type_id,
            current_tenant.id,
            db
        )

        if result["status"] == "applied":
            # Update document with improved content
            document.content = result["content"]
            document.updated_at = datetime.now(timezone.utc)
            db.commit()

        return {
            "message": "Patterns applied successfully",
            "status": result["status"],
            "patterns_applied": result.get("patterns_applied", 0),
            "document_id": str(doc_id)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to apply patterns: {str(e)}"
        )


@router.post("/auto-apply/{doc_type}", response_model=MessageResponse)
async def enable_auto_apply_patterns(
    doc_type: uuid.UUID,
    pattern_ids: List[uuid.UUID],
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Enable auto-apply for specific patterns
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Update patterns to enable auto-apply
    updated_count = 0
    for pattern_id in pattern_ids:
        pattern = db.query(LearnedPattern).filter(
            LearnedPattern.id == pattern_id,
            LearnedPattern.document_type_id == doc_type,
            LearnedPattern.tenant_id == current_tenant.id
        ).first()

        if pattern:
            pattern.auto_apply = True
            pattern.updated_at = datetime.now(timezone.utc)
            updated_count += 1

    db.commit()

    return MessageResponse(message=f"Enabled auto-apply for {updated_count} patterns")


@router.get("/export/{doc_type}", response_model=LearningDataExport)
async def export_learning_data(
    doc_type: uuid.UUID,
    include_feedback: bool = False,
    include_sessions: bool = False,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export learning data for backup or migration
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    # Get patterns
    patterns = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == doc_type,
        LearnedPattern.tenant_id == current_tenant.id
    ).all()

    # Get feedback if requested
    feedback_data = []
    if include_feedback:
        feedback = db.query(AIFeedback).filter(
            AIFeedback.document_type_id == doc_type,
            AIFeedback.tenant_id == current_tenant.id
        ).all()
        feedback_data = [
            {
                "id": str(f.id),
                "feedback_type": f.feedback_type,
                "rating": f.rating,
                "sentiment": f.sentiment,
                "feedback_text": f.feedback_text,
                "suggested_improvement": f.suggested_improvement,
                "tags": f.tags,
                "created_at": f.created_at.isoformat()
            }
            for f in feedback
        ]

    # Get sessions if requested
    sessions_data = []
    if include_sessions:
        sessions = db.query(LearningSession).filter(
            LearningSession.document_type_id == doc_type,
            LearningSession.tenant_id == current_tenant.id
        ).all()
        sessions_data = [
            {
                "id": str(s.id),
                "session_type": s.session_type,
                "status": s.status,
                "input_data": s.input_data,
                "patterns_discovered": s.patterns_discovered,
                "started_at": s.started_at.isoformat()
            }
            for s in sessions
        ]

    # Prepare export data
    export_data = LearningDataExport(
        export_id=str(uuid.uuid4()),
        document_type_id=doc_type,
        tenant_id=current_tenant.id,
        export_timestamp=datetime.now(timezone.utc),
        learning_profile={
            "id": str(profile.id),
            "learning_enabled": profile.learning_enabled,
            "auto_apply_patterns": profile.auto_apply_patterns,
            "confidence_threshold": profile.confidence_threshold,
            "settings": profile.settings,
            "user_preferences": profile.user_preferences,
            "performance_metrics": profile.performance_metrics
        } if profile else None,
        patterns=[
            {
                "id": str(p.id),
                "pattern_type": p.pattern_type,
                "pattern_name": p.pattern_name,
                "pattern_description": p.pattern_description,
                "pattern_data": p.pattern_data,
                "trigger_conditions": p.trigger_conditions,
                "confidence_score": p.confidence_score,
                "usage_count": p.usage_count,
                "success_rate": p.success_rate,
                "is_active": p.is_active,
                "auto_apply": p.auto_apply,
                "created_at": p.created_at.isoformat()
            }
            for p in patterns
        ],
        feedback=feedback_data,
        sessions=sessions_data,
        metadata={
            "export_version": "1.0",
            "document_type_name": document_type.name,
            "total_patterns": len(patterns),
            "total_feedback": len(feedback_data),
            "total_sessions": len(sessions_data)
        }
    )

    return export_data


@router.post("/import/{doc_type}", response_model=LearningDataImportResponse)
async def import_learning_data(
    doc_type: uuid.UUID,
    import_request: LearningDataImportRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Import learning data from backup or migration
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    import_data = import_request.import_data
    options = import_request.import_options

    imported_patterns = 0
    imported_feedback = 0
    imported_sessions = 0
    skipped_items = 0
    errors = []

    try:
        # Import learning profile
        if import_data.learning_profile and options.get("overwrite_existing", False):
            profile = db.query(LearningProfile).filter(
                LearningProfile.document_type_id == doc_type,
                LearningProfile.tenant_id == current_tenant.id
            ).first()

            if profile:
                # Update existing profile
                profile_data = import_data.learning_profile
                profile.learning_enabled = profile_data.get("learning_enabled", True)
                profile.auto_apply_patterns = profile_data.get("auto_apply_patterns", False)
                profile.confidence_threshold = profile_data.get("confidence_threshold", 0.7)
                profile.settings = profile_data.get("settings", {})
                profile.user_preferences = profile_data.get("user_preferences", {})
                profile.performance_metrics = profile_data.get("performance_metrics", {})

        # Import patterns
        for pattern_data in import_data.patterns:
            try:
                # Check if pattern already exists
                existing_pattern = db.query(LearnedPattern).filter(
                    LearnedPattern.document_type_id == doc_type,
                    LearnedPattern.tenant_id == current_tenant.id,
                    LearnedPattern.pattern_name == pattern_data["pattern_name"]
                ).first()

                if existing_pattern and not options.get("merge_patterns", True):
                    skipped_items += 1
                    continue

                if existing_pattern and options.get("merge_patterns", True):
                    # Update existing pattern
                    existing_pattern.pattern_data = pattern_data["pattern_data"]
                    existing_pattern.trigger_conditions = pattern_data["trigger_conditions"]
                    existing_pattern.confidence_score = max(
                        existing_pattern.confidence_score,
                        pattern_data["confidence_score"]
                    )
                    existing_pattern.updated_at = datetime.now(timezone.utc)
                else:
                    # Create new pattern
                    new_pattern = LearnedPattern(
                        document_type_id=doc_type,
                        tenant_id=current_tenant.id,
                        pattern_type=pattern_data["pattern_type"],
                        pattern_name=pattern_data["pattern_name"],
                        pattern_description=pattern_data["pattern_description"],
                        pattern_data=pattern_data["pattern_data"],
                        trigger_conditions=pattern_data["trigger_conditions"],
                        confidence_score=pattern_data["confidence_score"],
                        usage_count=pattern_data.get("usage_count", 0),
                        success_rate=pattern_data.get("success_rate", 0.0),
                        is_active=pattern_data.get("is_active", True),
                        auto_apply=pattern_data.get("auto_apply", False)
                    )
                    db.add(new_pattern)

                imported_patterns += 1

            except Exception as e:
                errors.append(f"Failed to import pattern {pattern_data.get('pattern_name', 'unknown')}: {str(e)}")

        # Import feedback if requested
        if options.get("import_feedback", False):
            for feedback_data in import_data.feedback:
                try:
                    new_feedback = AIFeedback(
                        document_type_id=doc_type,
                        tenant_id=current_tenant.id,
                        user_id=current_user.id,
                        feedback_type=feedback_data["feedback_type"],
                        rating=feedback_data["rating"],
                        sentiment=feedback_data["sentiment"],
                        feedback_text=feedback_data["feedback_text"],
                        suggested_improvement=feedback_data["suggested_improvement"],
                        tags=feedback_data["tags"],
                        processed=True
                    )
                    db.add(new_feedback)
                    imported_feedback += 1

                except Exception as e:
                    errors.append(f"Failed to import feedback: {str(e)}")

        # Import sessions if requested
        if options.get("import_sessions", False):
            for session_data in import_data.sessions:
                try:
                    new_session = LearningSession(
                        document_type_id=doc_type,
                        tenant_id=current_tenant.id,
                        triggered_by_user_id=current_user.id,
                        session_type=session_data["session_type"],
                        status=session_data["status"],
                        input_data=session_data["input_data"],
                        patterns_discovered=session_data["patterns_discovered"]
                    )
                    db.add(new_session)
                    imported_sessions += 1

                except Exception as e:
                    errors.append(f"Failed to import session: {str(e)}")

        db.commit()

        return LearningDataImportResponse(
            import_id=str(uuid.uuid4()),
            status="completed" if not errors else "completed_with_errors",
            imported_patterns=imported_patterns,
            imported_feedback=imported_feedback,
            imported_sessions=imported_sessions,
            skipped_items=skipped_items,
            errors=errors,
            imported_at=datetime.now(timezone.utc)
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Import failed: {str(e)}"
        )
