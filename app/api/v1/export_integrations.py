"""
Integration export API endpoints for third-party platforms.
Handles Confluence, Notion, JIRA, and custom template exports.
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import uuid
from datetime import datetime, timezone, timedelta

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.export import ExportJob, ExportIntegration
from app.schemas.export import (
    ExportJobResponse,
    ConfluenceExportRequest,
    NotionExportRequest,
    JiraExportRequest,
    CustomExportRequest,
    MessageResponse
)

router = APIRouter()


async def create_integration_export_job(
    document_id: uuid.UUID,
    export_format: str,
    export_options: dict,
    user: User,
    tenant: Tenant,
    db: Session
) -> ExportJob:
    """Create a new integration export job"""
    # Verify document exists and user has access
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.tenant_id == tenant.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Create export job
    export_job = ExportJob(
        document_id=document_id,
        export_format=export_format,
        export_options=export_options,
        user_id=user.id,
        tenant_id=tenant.id,
        estimated_completion=datetime.now(timezone.utc) + timedelta(minutes=10)  # Integrations take longer
    )
    
    db.add(export_job)
    db.commit()
    db.refresh(export_job)
    
    return export_job


async def process_confluence_export(job_id: uuid.UUID, db: Session):
    """Process Confluence export job"""
    job = db.query(ExportJob).filter(ExportJob.id == job_id).first()
    if not job:
        return
    
    try:
        job.status = "processing"
        job.started_at = datetime.now(timezone.utc)
        job.progress = 20
        db.commit()
        
        # Simulate Confluence API integration
        import asyncio
        await asyncio.sleep(3)
        
        # Simulate successful publish
        confluence_page_id = f"confluence_page_{uuid.uuid4().hex[:8]}"
        confluence_url = f"{job.export_options.get('confluence_url')}/pages/{confluence_page_id}"
        
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)
        job.result_url = confluence_url
        job.integration_data = {
            "page_id": confluence_page_id,
            "space_key": job.export_options.get("space_key"),
            "page_url": confluence_url
        }
        
        db.commit()
        
    except Exception as e:
        job.status = "failed"
        job.error_message = str(e)
        db.commit()


async def process_notion_export(job_id: uuid.UUID, db: Session):
    """Process Notion export job"""
    job = db.query(ExportJob).filter(ExportJob.id == job_id).first()
    if not job:
        return
    
    try:
        job.status = "processing"
        job.started_at = datetime.now(timezone.utc)
        job.progress = 20
        db.commit()
        
        # Simulate Notion API integration
        import asyncio
        await asyncio.sleep(3)
        
        # Simulate successful creation
        notion_page_id = f"notion_page_{uuid.uuid4().hex[:8]}"
        notion_url = f"https://notion.so/{notion_page_id}"
        
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)
        job.result_url = notion_url
        job.integration_data = {
            "page_id": notion_page_id,
            "database_id": job.export_options.get("database_id"),
            "page_url": notion_url
        }
        
        db.commit()
        
    except Exception as e:
        job.status = "failed"
        job.error_message = str(e)
        db.commit()


async def process_jira_export(job_id: uuid.UUID, db: Session):
    """Process JIRA export job"""
    job = db.query(ExportJob).filter(ExportJob.id == job_id).first()
    if not job:
        return
    
    try:
        job.status = "processing"
        job.started_at = datetime.now(timezone.utc)
        job.progress = 20
        db.commit()
        
        # Simulate JIRA API integration
        import asyncio
        await asyncio.sleep(4)
        
        # Simulate successful ticket creation
        epic_key = f"{job.export_options.get('project_key')}-{uuid.uuid4().hex[:4].upper()}"
        jira_url = f"{job.export_options.get('jira_url')}/browse/{epic_key}"
        
        # Simulate creating subtasks
        subtask_keys = [
            f"{job.export_options.get('project_key')}-{uuid.uuid4().hex[:4].upper()}"
            for _ in range(3)
        ]
        
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)
        job.result_url = jira_url
        job.integration_data = {
            "epic_key": epic_key,
            "epic_url": jira_url,
            "subtask_keys": subtask_keys,
            "project_key": job.export_options.get("project_key")
        }
        
        db.commit()
        
    except Exception as e:
        job.status = "failed"
        job.error_message = str(e)
        db.commit()


async def process_custom_export(job_id: uuid.UUID, db: Session):
    """Process custom template export job"""
    job = db.query(ExportJob).filter(ExportJob.id == job_id).first()
    if not job:
        return
    
    try:
        job.status = "processing"
        job.started_at = datetime.now(timezone.utc)
        job.progress = 30
        db.commit()
        
        # Simulate template processing
        import asyncio
        await asyncio.sleep(2)
        
        # Create result file
        from app.models.export import ExportFile
        
        output_format = job.export_options.get("output_format", "html")
        export_file = ExportFile(
            export_job_id=job.id,
            filename=f"custom_export_{job.id}.{output_format}",
            original_filename=f"{job.document.title}_custom.{output_format}",
            file_path=f"/exports/custom/{job.id}",
            file_size=2048,
            content_type=f"text/{output_format}" if output_format in ["html", "xml"] else "application/octet-stream",
            expires_at=datetime.now(timezone.utc) + timedelta(days=7)
        )
        
        db.add(export_file)
        
        job.status = "completed"
        job.progress = 100
        job.completed_at = datetime.now(timezone.utc)
        job.result_file_id = export_file.id
        
        db.commit()
        
    except Exception as e:
        job.status = "failed"
        job.error_message = str(e)
        db.commit()


# Confluence export
@router.post("/{doc_id}/export/confluence", response_model=ExportJobResponse)
async def export_to_confluence(
    doc_id: uuid.UUID,
    request: ConfluenceExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Publish document to Confluence
    """
    export_job = await create_integration_export_job(
        doc_id, "confluence", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_confluence_export, export_job.id, db)
    return export_job


# Notion export
@router.post("/{doc_id}/export/notion", response_model=ExportJobResponse)
async def export_to_notion(
    doc_id: uuid.UUID,
    request: NotionExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export document to Notion
    """
    export_job = await create_integration_export_job(
        doc_id, "notion", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_notion_export, export_job.id, db)
    return export_job


# JIRA export
@router.post("/{doc_id}/export/jira", response_model=ExportJobResponse)
async def export_to_jira(
    doc_id: uuid.UUID,
    request: JiraExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create JIRA tickets from document
    """
    export_job = await create_integration_export_job(
        doc_id, "jira", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_jira_export, export_job.id, db)
    return export_job


# Custom template export
@router.post("/{doc_id}/export/custom", response_model=ExportJobResponse)
async def export_with_custom_template(
    doc_id: uuid.UUID,
    request: CustomExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Export document using custom template
    """
    export_job = await create_integration_export_job(
        doc_id, "custom", request.dict(), current_user, current_tenant, db
    )
    
    background_tasks.add_task(process_custom_export, export_job.id, db)
    return export_job
