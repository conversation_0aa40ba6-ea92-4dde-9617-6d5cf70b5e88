"""
Authentication dependencies for FastAPI endpoints.

This module provides dependency functions for authentication and authorization,
supporting both local JWT tokens and Supabase Auth tokens.
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError, jwt
from typing import Optional
import logging
import uuid

from app.core.config import settings
from app.core.database import get_db
from app.core.security import verify_token, verify_supabase_token
from app.models.user import User
from app.models.tenant import Tenant
from app.services.auth_service import auth_service

logger = logging.getLogger(__name__)
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token.

    This function supports both local JWT tokens and Supabase Auth tokens,
    providing flexibility for different authentication scenarios.

    Args:
        credentials: Bearer token credentials
        db: Database session

    Returns:
        User: Authenticated user object

    Raises:
        HTTPException: If authentication fails
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    token = credentials.credentials
    user_id = None

    # Try to verify as local JWT token first
    payload = verify_token(token)
    if payload:
        user_id = payload.get("sub")
    else:
        # Try to verify as Supabase token
        payload = verify_supabase_token(token)
        if payload:
            user_id = payload.get("sub")
        else:
            # Try to get user info from Supabase directly
            try:
                user_data = await auth_service.get_user_by_token(token)
                if user_data:
                    user_id = user_data["id"]
            except Exception as e:
                logger.warning(f"Failed to get user from Supabase token: {str(e)}")

    if not user_id:
        raise credentials_exception

    # Get user from database
    try:
        user = db.query(User).filter(User.id == uuid.UUID(user_id)).first()
    except ValueError:
        # Handle case where user_id is not a valid UUID
        raise credentials_exception

    if user is None:
        raise credentials_exception

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user account"
        )

    return user

async def get_current_tenant(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Tenant:
    """
    Get current tenant for the authenticated user.

    Args:
        current_user: Authenticated user
        db: Database session

    Returns:
        Tenant: User's tenant object

    Raises:
        HTTPException: If tenant not found
    """
    tenant = db.query(Tenant).filter(Tenant.id == current_user.tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )

    return tenant


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (additional check for active status).

    Args:
        current_user: Authenticated user

    Returns:
        User: Active user object

    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user account"
        )
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current superuser (admin access required).

    Args:
        current_user: Authenticated user

    Returns:
        User: Superuser object

    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    return current_user


def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None.

    This dependency is useful for endpoints that work for both
    authenticated and anonymous users.

    Args:
        credentials: Optional bearer token credentials
        db: Database session

    Returns:
        Optional[User]: User object if authenticated, None otherwise
    """
    if not credentials:
        return None

    try:
        # Use the same logic as get_current_user but don't raise exceptions
        token = credentials.credentials
        user_id = None

        # Try to verify as local JWT token first
        payload = verify_token(token)
        if payload:
            user_id = payload.get("sub")
        else:
            # Try to verify as Supabase token
            payload = verify_supabase_token(token)
            if payload:
                user_id = payload.get("sub")

        if not user_id:
            return None

        # Get user from database
        try:
            user = db.query(User).filter(User.id == uuid.UUID(user_id)).first()
        except ValueError:
            return None

        if user and user.is_active:
            return user

        return None

    except Exception as e:
        logger.warning(f"Optional auth failed: {str(e)}")
        return None