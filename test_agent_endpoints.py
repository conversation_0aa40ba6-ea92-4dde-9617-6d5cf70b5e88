"""
Test script for AI Agent & Prompt Management API endpoints
"""
import requests
import json
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
DOC_TYPE = "prd"  # Test with PRD document type

# Test data
AGENT_DATA = {
    "name": "Test PRD Agent",
    "description": "A test agent for PRD generation",
    "doc_type": DOC_TYPE,
    "agent_type": "custom",
    "model_name": "claude-3-5-sonnet-20241022",
    "system_prompt": "You are an expert product manager who creates comprehensive PRDs.",
    "temperature": 30,
    "max_tokens": 4000,
    "capabilities": {
        "document_generation": True,
        "refinement": True,
        "follow_up_questions": True,
        "suggestions": True
    },
    "parameters": {
        "focus_area": "technical_requirements",
        "detail_level": "comprehensive"
    }
}

PROMPT_DATA = {
    "name": "Test PRD Prompt",
    "description": "A test prompt for PRD generation",
    "doc_type": DOC_TYPE,
    "prompt_template": "Generate a PRD for {product_name} with the following requirements: {requirements}",
    "variables": ["product_name", "requirements"],
    "prompt_type": "generation",
    "category": "business"
}

EXECUTE_DATA = {
    "input_data": {
        "prompt": "Generate a PRD for a mobile app",
        "product_name": "TaskMaster Pro",
        "requirements": "User authentication, task management, notifications"
    },
    "job_type": "execute"
}


def get_auth_headers():
    """Get authentication headers (placeholder - implement based on your auth system)"""
    # This would need to be implemented based on your authentication system
    # For now, returning empty headers for testing
    return {
        "Content-Type": "application/json",
        # "Authorization": "Bearer your_token_here"
    }


def test_agent_endpoints():
    """Test agent management endpoints"""
    headers = get_auth_headers()
    
    print("=== Testing Agent Management Endpoints ===")
    
    # Test 1: Get agents for document type
    print("\n1. Testing GET /agents/{doc_type}")
    response = requests.get(f"{BASE_URL}/agents/{DOC_TYPE}", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data.get('total', 0)} agents")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Create new agent
    print(f"\n2. Testing POST /agents/{DOC_TYPE}")
    response = requests.post(f"{BASE_URL}/agents/{DOC_TYPE}", 
                           headers=headers, 
                           json=AGENT_DATA)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        agent = response.json()
        agent_id = agent["id"]
        print(f"Created agent with ID: {agent_id}")
        
        # Test 3: Get specific agent
        print(f"\n3. Testing GET /agents/{DOC_TYPE}/{agent_id}")
        response = requests.get(f"{BASE_URL}/agents/{DOC_TYPE}/{agent_id}", headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("Successfully retrieved agent details")
        
        # Test 4: Update agent
        print(f"\n4. Testing PUT /agents/{DOC_TYPE}/{agent_id}")
        update_data = {"description": "Updated test agent description"}
        response = requests.put(f"{BASE_URL}/agents/{DOC_TYPE}/{agent_id}", 
                              headers=headers, 
                              json=update_data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("Successfully updated agent")
        
        return agent_id
    else:
        print(f"Error creating agent: {response.text}")
        return None


def test_prompt_endpoints(agent_id=None):
    """Test prompt management endpoints"""
    headers = get_auth_headers()
    
    print("\n=== Testing Prompt Management Endpoints ===")
    
    # Test 1: Get prompts for document type
    print("\n1. Testing GET /prompts/{doc_type}")
    response = requests.get(f"{BASE_URL}/prompts/{DOC_TYPE}", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data.get('total', 0)} prompts")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Create new prompt
    print(f"\n2. Testing POST /prompts/{DOC_TYPE}")
    prompt_data = PROMPT_DATA.copy()
    if agent_id:
        prompt_data["agent_id"] = agent_id
    
    response = requests.post(f"{BASE_URL}/prompts/{DOC_TYPE}", 
                           headers=headers, 
                           json=prompt_data)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        prompt = response.json()
        prompt_id = prompt["id"]
        print(f"Created prompt with ID: {prompt_id}")
        
        # Test 3: Get specific prompt
        print(f"\n3. Testing GET /prompts/{DOC_TYPE}/{prompt_id}")
        response = requests.get(f"{BASE_URL}/prompts/{DOC_TYPE}/{prompt_id}", headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("Successfully retrieved prompt details")
        
        return prompt_id
    else:
        print(f"Error creating prompt: {response.text}")
        return None


def test_execution_endpoints(agent_id):
    """Test agent execution endpoints"""
    headers = get_auth_headers()
    
    print("\n=== Testing Agent Execution Endpoints ===")
    
    # Test 1: Get capabilities
    print(f"\n1. Testing GET /agents/capabilities/{DOC_TYPE}")
    response = requests.get(f"{BASE_URL}/agents/capabilities/{DOC_TYPE}", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Available capabilities: {list(data.get('capabilities', {}).keys())}")
        print(f"Supported operations: {data.get('supported_operations', [])}")
    else:
        print(f"Error: {response.text}")
    
    # Test 2: Execute agent
    print(f"\n2. Testing POST /agents/{DOC_TYPE}/{agent_id}/execute")
    response = requests.post(f"{BASE_URL}/agents/{DOC_TYPE}/{agent_id}/execute", 
                           headers=headers, 
                           json=EXECUTE_DATA)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        job = response.json()
        job_id = job["id"]
        print(f"Started job with ID: {job_id}")
        
        # Test 3: Get job status
        print(f"\n3. Testing GET /agents/jobs/{job_id}")
        response = requests.get(f"{BASE_URL}/agents/jobs/{job_id}", headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            job_status = response.json()
            print(f"Job status: {job_status['status']}")
            print(f"Progress: {job_status['progress_percentage']}%")
        
        return job_id
    else:
        print(f"Error executing agent: {response.text}")
        return None


def cleanup_test_data(agent_id, prompt_id):
    """Clean up test data"""
    headers = get_auth_headers()
    
    print("\n=== Cleaning Up Test Data ===")
    
    # Delete prompt
    if prompt_id:
        print(f"\nDeleting prompt {prompt_id}")
        response = requests.delete(f"{BASE_URL}/prompts/{DOC_TYPE}/{prompt_id}", headers=headers)
        print(f"Status: {response.status_code}")
    
    # Delete agent
    if agent_id:
        print(f"\nDeleting agent {agent_id}")
        response = requests.delete(f"{BASE_URL}/agents/{DOC_TYPE}/{agent_id}", headers=headers)
        print(f"Status: {response.status_code}")


def main():
    """Run all tests"""
    print("Starting AI Agent & Prompt Management API Tests")
    print("=" * 50)
    
    try:
        # Test agent endpoints
        agent_id = test_agent_endpoints()
        
        # Test prompt endpoints
        prompt_id = test_prompt_endpoints(agent_id)
        
        # Test execution endpoints
        if agent_id:
            job_id = test_execution_endpoints(agent_id)
        
        # Clean up
        cleanup_test_data(agent_id, prompt_id)
        
        print("\n" + "=" * 50)
        print("Tests completed!")
        
    except Exception as e:
        print(f"\nError during testing: {e}")


if __name__ == "__main__":
    main()
